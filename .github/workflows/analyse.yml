name: Analyse

on:
  push:
    branches:
      - main
  pull_request:
    types: [ opened, synchronize, reopened ]

jobs:
  aws:
    uses: ./.github/workflows/aws.yml
    secrets: inherit

  build:
    name: Build
    runs-on: ["self-hosted","mgt-stg-tech-runner-ec2"]
    needs: [ aws ]
    env:
      CODEARTIFACT_AUTH_TOKEN: ${{ needs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 21

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          gradle-version: current
          cache-read-only: false
          gradle-home-cache-cleanup: true

      - name: Prepare # This will cache all (including runtime) dependencies
        run: ./gradlew downloadDependencies --build-cache --info

      - name: Build
        run: ./gradlew testClasses build -x check --build-cache --info -Dorg.gradle.jvmargs=-Xmx1g

  detekt:
    name: Detekt
    runs-on: ["self-hosted","mgt-stg-tech-runner-ec2"]
    needs: [ aws, build ]
    env:
      CODEARTIFACT_AUTH_TOKEN: ${{ needs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 21

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-read-only: true

      - name: Detekt
        run: ./gradlew detekt detektMain detektTest --continue --build-cache --info -Dorg.gradle.jvmargs=-Xmx1g

      - name: Summary
        if: failure()
        run: |
          shopt -s globstar
          echo "### Detekt" >> $GITHUB_STEP_SUMMARY
          echo "$(cat **/reports/detekt/detekt.txt)" >> $GITHUB_STEP_SUMMARY
          echo "$(cat **/reports/detekt/main.txt)" >> $GITHUB_STEP_SUMMARY
          echo "$(cat **/reports/detekt/test.txt)" >> $GITHUB_STEP_SUMMARY

  ktlint:
    name: Ktlint
    runs-on: ["self-hosted","mgt-stg-tech-runner-ec2"]
    needs: [ aws, build ]
    env:
      CODEARTIFACT_AUTH_TOKEN: ${{ needs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 21

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-read-only: true

      - name: Ktlint
        run: ./gradlew ktlintCheck --continue --build-cache --info

      - name: Summary
        if: failure()
        run: |
          shopt -s globstar
          echo "### Ktlint" >> $GITHUB_STEP_SUMMARY
          echo "$(cat **/reports/ktlint/**/*.txt)" >> $GITHUB_STEP_SUMMARY

  test:
    name: Test
    runs-on: ["self-hosted","mgt-stg-tech-runner-ec2"]
    needs: [ aws, build ]
    env:
      CODEARTIFACT_AUTH_TOKEN: ${{ needs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 21

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-read-only: true

      - name: Test
        run: ./gradlew test jacocoTestReport jacocoIntegrationTestReport jacocoAggregateTestReport --build-cache --info -Dorg.gradle.jvmargs=-Xmx2560m

      - name: Test Report
        uses: mikepenz/action-junit-report@v4
        if: always()
        with:
          report_paths: '**/build/test-results/test/TEST-*.xml'

      - name: Cache Code Coverage Report
        uses: actions/cache@v4
        with:
          path: '**/reports/jacoco/**/*.xml'
          key: coverage-${{ github.run_id }}

  sonar:
    name: Sonar
    runs-on: ["self-hosted","mgt-stg-tech-runner-ec2"]
    needs: [ aws, test ]
    env:
      CODEARTIFACT_AUTH_TOKEN: ${{ needs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} # Needed to get PR information, if any
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 21

      - name: Cache SonarCloud packages
        uses: actions/cache@v4
        with:
          path: ~/.sonar/cache
          key: ${{ runner.os }}-sonar

      - name: Cache Code Coverage Report
        uses: actions/cache@v4
        with:
          path: '**/reports/jacoco/**/*.xml'
          key: coverage-${{ github.run_id }}

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-read-only: true

      - name: Sonar
        run: ./gradlew sonar --build-cache --info -Dorg.gradle.jvmargs=-Xmx1g
