name: "PR Title Checker"

on:
  pull_request_target:
    types:
      - opened
      - edited
      - synchronize
      - labeled
      - unlabeled

jobs:
  check_pr_title:
    name: Check PR Title
    runs-on: ubuntu-latest
    steps:
      - uses: thehanimo/pr-title-checker@v1.4.2
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          pass_on_octokit_error: false
          configuration_path: .github/pr-title-checker-config.json
