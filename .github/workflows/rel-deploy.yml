name: Deploy to Release

on:
  workflow_dispatch:

run-name: Deploy [${{ github.ref_name }}] to release

jobs:
  deploy:
    # Only a tag is allowed to deploy
    if: startsWith(github.ref, 'refs/tags/')
    name: Deploy
    uses: Multiplier-Core/devops-github-shared-pipelines/.github/workflows/be-generic-deployment.yml@main
    secrets: inherit
    with:
      deploy_target: release
      service_name: compensationService
      ecr_repository: rel-app-tech-compensationservice-ecr
