# compensation-service

[//]: # (TODO-template-use replace service name and add content)

## Getting Started

### Prerequisites

* JDK 21

## Usage

Build the code in IntelliJ or
```shell
./gradlew clean build
```

### Running the application

[//]: # (TODO-template-use replace Application file)
Run `CompensationServiceApplication.kt` in IntelliJ or
```shell
./gradlew bootRun
```

## Architecture

See [Architecture](https://www.notion.so/usemultiplier/Architecture-2fd3f1b2b2dd43d79c078d7a04db85ba?pvs=4)

## Tools

### Detekt

See [Linting > Detekt](https://www.notion.so/usemultiplier/Linting-7d92a441fe6d402eaedcf4966e076c8e?pvs=4#74173492a5bd48d9b73dfeb9a8120489)

### Ktlint

See [Linting > Ktlint](https://www.notion.so/usemultiplier/Linting-7d92a441fe6d402eaedcf4966e076c8e?pvs=4#525cc940b0e848b895199a9851a3b655)
