package com.multiplier.compensation

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.context.annotation.ComponentScan
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication
@EnableScheduling
@ConfigurationPropertiesScan("com.multiplier")
@ComponentScan("com.multiplier")
class CompensationServiceApplication

fun main(args: Array<String>) {
    runApplication<CompensationServiceApplication>(args = args)
}
