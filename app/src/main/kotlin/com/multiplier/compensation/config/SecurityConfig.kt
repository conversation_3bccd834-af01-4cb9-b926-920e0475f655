package com.multiplier.compensation.config

import com.multiplier.common.transport.http.HttpUserContextFilter
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true)
class SecurityConfig {
    @Bean
    fun filterChain(
        http: HttpSecurity,
        httpUserContextFilter: HttpUserContextFilter,
    ): SecurityFilterChain = http.authorizeHttpRequests {
        it.requestMatchers("/graphql").authenticated()
        it.requestMatchers("/actuator/health").permitAll()
        it.requestMatchers("/actuator/**").hasRole("MULTIPLIER_ADMIN")
        it.requestMatchers("/health").permitAll()
    }.csrf {
        it.disable()
    }.addFilterBefore(httpUserContextFilter, UsernamePasswordAuthenticationFilter::class.java)
        .build()
}
