<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="JSON" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                <maxDepthPerThrowable>30</maxDepthPerThrowable>
                <shortenedClassNameLength>30</shortenedClassNameLength>
                <rootCauseFirst>true</rootCauseFirst>
                <inlineHash>true</inlineHash>
            </throwableConverter>
        </encoder>
    </appender>

    <if condition='isDefined("ENV_VAR")'>
        <then>
            <root level="INFO">
                <appender-ref ref="JSON"/>
            </root>
        </then>
        <else>
            <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
            <property name="LOG_FILE" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/spring.log}"/>
            <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
            <include resource="org/springframework/boot/logging/logback/file-appender.xml"/>

            <root level="INFO">
                <appender-ref ref="CONSOLE"/>
            </root>
        </else>
    </if>

    <logger name="org.jooq.Constants" level="WARN"/>
</configuration>
