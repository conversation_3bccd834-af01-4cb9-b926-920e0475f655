# ===================================================================================================
# BULK UPLOAD SHEETS GENERATOR FOR EOR MIGRATION
# ===================================================================================================
#
# SETUP INSTRUCTIONS:
# ------------------
# Commands to install necessary packages:
#   python3 -m venv myenv
#   source myenv/bin/activate
#   pip install pandas numbers-parser numpy openpyxl
#
# CONFIGURATION REQUIREMENTS:
# --------------------------
# (MANDATORY) Configure - default_payroll_start_date_to_start_new_items_from inside script. Example - "2025-03-01"
# (MANDATORY) Configure - date_to_trigger_revisions_in_payroll_input inside script. Should be migration date + 1/2 days.
#             Example - "2025-03-05" This date would decide when these revisions would be exactly included
#             in the payroll input, and hence the new hire sheet and salary revision sheet.
# (OPTIONAL) Configure - mapping_override_for_component_name inside script.
#
# USAGE:
# -----
# RUN THE SCRIPT, COMMAND - python3 generateBulkUploadSheets.py <input_files_path>

# ===================================================================================================
# IMPORTS
# ===================================================================================================

import pandas as pd
import sys
import os
import json
import numpy as np
from datetime import datetime, timedelta
from openpyxl import load_workbook
from openpyxl.styles import Font
import re
import logging

# Import duplicate resolution functions
from duplicate_resolver import (
    BASE_DUPLICATE_KEY_COLUMNS,
    PAY_SUPPLEMENTS_AND_ALLOWANCES_TO_CONSIDER,
    resolve_duplicates_in_dataframe,
    resolve_duplicates_across_dataframes
)

# Import component replacement functionality
from component_replacer import replace_compensation_components

# ===================================================================================================
# CONFIGURATION
# ===================================================================================================

# Define the list of countries to migrate
# countries_to_migrate = [
#     'BEL', 'BGD', 'BRB', 'CAN', 'CHE', 'CYM', 'DNK', 'ESP', 'GEO', 'HKG', 'IMN', 'IND', 'ISR', 'JPN', 'KEN', 'KOR', 'LKA', 'LTU', 'NPL', 'NZL', 'PAK', 'PAN', 'PER', 'PHL', 'ROU', 'RUS', 'SLV', 'TTO', 'TUR', 'TWN', 'UGA', 'UKR', 'USA', 'VNM', 'ZAF', 'ZMB', 'ZWE'
# ]

countries_to_migrate = [
    'CAN'
]

# Tags for schema
tags = "MULTIPLIER_EOR_OPS_APPROVED"
new_schema_format_enabled = True
enable_component_replacement = True
default_schema_name_for_india = "Multiplier Defined Compensation Structure for India with PF capped at 1800 pm"

# Date configuration
default_payroll_start_date_to_start_new_items_from = "2025-07-01"
date_to_trigger_revisions_in_payroll_input = "2025-06-27"
default_recurring_pay_supplement_item_generation_start = "2024-03-01"

# Cutoff date configuration
# TO override cutoff date for any country that uses a different cutoff.
default_previous_cycle_cut_off_date = '2025-06-15'
country_to_cutoff_map_overrides = {

}

# Global component replacement configuration
# Paths to global component files for replacing components in schemas
GLOBAL_PAY_SUPPLEMENTS_FILE = "./PAY_SUPPLEMENT_SCHEMA.xlsx"
GLOBAL_CONTRACT_ALLOWANCES_FILE = "./CONTRACT_ALLOWANCE_SCHEMA.xlsx"

# ===================================================================================================
# GLOBAL VARIABLES
# ===================================================================================================

# Global list to store all exceptions/warnings
exceptions_observed = []

# Global map to store old allowances compensation label to schema component name mapping
old_allowances_comp_label_to_component_name_map = {}

# Global dictionary to store salary review approved dates mapping (id -> list of {contract_id, updated_on, effective_date, review_status, contracting_entity_type})
salary_review_approved_dates_map = {}

# ===================================================================================================
# UTILITY FUNCTIONS
# ===================================================================================================

def log_exception(message, contract_id=None, metadata=None, level=None):
    """Log an exception/warning with optional contract_id"""
    global exceptions_observed
    exceptions_observed.append({
        "contract_id": contract_id if contract_id else "",
        "message": message,
        "metadata": metadata,
        'level': level if level else "ERROR"
    })
    # Also print to console for immediate feedback
    print(message)

# ===================================================================================================
# MAPPING DICTIONARIES
# ===================================================================================================

mapping_override_for_component_name = {
    # Base salary related
    "base": "Gross Salary",
    "basePay": "Gross Salary",
    "probationBasePay": "Gross Salary",

    # Bonus related
    "annual-bonus": "Fixed Annual Bonus",
    "bonus-allowance": "Bonus Allowance",
    " retention bonus": "Fixed Retention Bonus",
    "annualBonus": "Fixed Annual Bonus",
    "joiningBonus": "Joining Bonus",
    "joining-bonus": "Joining Bonus",

    # Transport related
    "Transport": "Transport Allowance",
    "Transport Allowance": "Transport Allowance",
    "Monthly Transport Allowance": "Transport Allowance",

    # Medical related
    "Medical Insurance": "Medical Insurance Allowance",
    "Medical Expenses\n": "Medical Expenses",
    "Medical Expenses": "Medical Expenses",

    # Breakup allowances
    "basicBreakupAllowance": "Basic Breakup Allowance",
    "hraBreakupAllowance": "Hra Breakup Allowance",
    "specialAllowanceBreakupAllowance": "Special Allowance Breakup Allowance",
    "Other Allowance Breakup Allowance": "Other Allowance",

    # Other allowances
    "Tenure severance": "Tenure Severance Allowance",
    "cost-off-living-allowance": "Cost of Living Allowance",
    "One Off Allowance of AED 10246": "Other Allowance",
    "mealAllowance": "Meal Allowance",
    "accommodationAllowance": "Accommodation Allowance",
    "otherAllowance": "Other Allowance",
    "internetAllowance": "Internet Allowance",
    "internet-allowance": "Internet Allowance",
    "Internet allowance": "Internet Allowance",
    "mobile-and-phone-allowance": "Mobile and Phone Allowance",
    "phoneAllowance": "Phone Allowance",
    "Mobile allowance": "Mobile and Phone Allowance",
    "Mobile": "Mobile and Phone Allowance",
    "mobileAndPhoneAllowance": "Mobile and Phone Allowance",
    "allowances": "Other Allowance",
    "Connectivity Allowance": "Other Allowance",
    "Healthy Lifestyle Allowance": "Other Allowance",
    "Home office allowance": "Other Allowance",
    "deMinimisAllowance": "De Minimis Allowance",
    "252895252": "Other Allowance",
    "On-target bonus": "Fixed Performance Bonus",

    # Month pay related
    "13th-month": "13th Month Pay",
    "13thMonth": "13th Month Pay",
    "14thMonth": "14th Month Pay",
    "AWS 13 Month (Pro-rated)": "13th Month Pay",

    # Deductions
    "SIP": "SIP",
    "ESOP Deduction": "ESOP Deduction",
    "ESOP DEDUCTION": "ESOP Deduction",
    "ESOP Deduction\n": "ESOP Deduction",

    # One-time payments
    "One Time Allowance": "One Time Allowance",
    "One time relocation allowance": "Relocation Allowance",
    "One time payment": "One Time Payment",
    "one off payment for Dec 2024 salary": "One Time Payment",
    "18 days of leave encahsment": "Leave Encashment",
    "one time payment": "One Time Payment",
    "one time payment ": "One Time Payment",
    "Pay Update Effective 1st February 2024 - arrear payment in March": "Other Pay Supplement",

    # Awards
    "Long Service Award": "Long Service Award",

    # Sales related
    "Sales Commissions": "Sales Commission",

    # Important to cross check.
    "other": "Variable Other Bonus",
    "variable-performance-bonus": "Variable Performance Bonus",
    "variablePerformanceBonus": "Variable Performance Bonus",

    # Based on pay_supplement_type_to_name_mapping.
    "Other Pay Supplement": "Other Pay Supplement",
    "Sales Commission": "Sales Commission",
    "Bonus": "Bonus",
    "bonus": "Fixed Other Bonus",
    "Allowance": "Allowance",
    "Variable Performance Bonus": "Variable Performance Bonus"
}

# Components to ignore in processing
component_names_to_ignore = ["13th Month Pay", "14th Month Pay"]

# Pay supplement type to name mapping
pay_supplement_type_to_name_mapping = {
    "ALLOWANCE": "Allowance",
    "BONUS": "Bonus",
    "COMMISSION": "Sales Commission",
    "OTHER": "Other Pay Supplement",
    "VARIABLE_PERFORMANCE_BONUS": "Variable Performance Bonus"
}

# Frequency mapping for pay schedules
old_schema_frequency_to_pay_schedule_frequency_overrides = {
    "ANNUALLY": "ANNUALLY",
    "BI_MONTHLY": "SEMI_MONTHLY",
    "BIWEEKLY": "BI_WEEKLY",
    "BI_WEEKLY": "BI_WEEKLY",
    "DAILY": "DAILY",
    "HALFYEARLY": "SEMI_ANNUALLY",
    "MONTHLY": "MONTHLY",
    "ONCE": "ONE_TIME",
    "QUATERLY": "QUARTERLY",
    "SEMIMONTHLY": "SEMI_MONTHLY",
    "WEEKLY": "WEEKLY"
}

# Frequency mapping for billing
old_schema_frequency_to_new_billing_frequency_overrides = {
    "ANNUALLY": "ANNUALLY",
    "BI_MONTHLY": "SEMIMONTHLY",
    "BI_WEEKLY": "BIWEEKLY",
    "CUSTOM": "",
    "DAILY": "DAILY",
    "HALFYEARLY": "SEMIANNUALLY",
    "HOURLY": "HOURLY",
    "MONTHLY": "MONTHLY",
    "ONCE": "ONETIME",
    "QUATERLY": "QUARTERLY",
    "SEMIMONTHLY": "SEMIMONTHLY",
    "TRI_ANNUALLY": "",
    "WEEKLY": "WEEKLY"
}

# Variable allowances to ignore in processing
variable_allowances_to_ignore = [
    "internetAllowance",
    "mobileAndPhoneAllowance",
    "deMinimisAllowance",
    "allowances"
]

# ===================================================================================================
# HELPER FUNCTIONS
# ===================================================================================================

def get_compensation_schema_name(country_code):
    """Generate the compensation schema name for a country code."""
    if "IND" in country_code:
        return default_schema_name_for_india

    return country_code + "_EOR_" + "Default"


def read_excel(input_file, sheet_name=0):
    """Reads an Excel file and returns a DataFrame.

    Args:
        input_file: Path to the Excel file
        sheet_name: Name or index of the sheet to read (default: 0, first sheet)
    """
    try:
        data = pd.read_excel(input_file, engine='openpyxl', sheet_name=sheet_name)
        return data
    except Exception as e:
        log_exception(f"Error reading Excel file {input_file} (sheet: {sheet_name}): {e}")
        sys.exit(1)


def is_nan_or_empty(value):
    """Check if a value is NaN or empty.

    Args:
        value: The value to check

    Returns:
        bool: True if the value is NaN or empty, False otherwise
    """
    # Check if the value is a string and empty
    if isinstance(value, str) and value == "":
        return True
    # Check if the value is a number and NaN
    elif isinstance(value, (int, float)) and np.isnan(value):
        return True
    return False

def to_snake_case(s: str) -> str:
    # Move bracketed numbers to underscore suffix: "Bonus [1]" -> "Bonus_1"
    s = re.sub(r'\s*\[(\d+)\]', r'_\1', s)
    # Replace ampersands and other special characters with 'and' or remove
    s = re.sub(r'&', 'and', s)
    # Replace non-alphanumeric characters (except underscores) with a space
    s = re.sub(r'[^\w\s]', '', s)
    # Replace spaces and hyphens with underscores
    s = re.sub(r'[\s-]+', '_', s)
    # Convert to lowercase
    s = s.lower()
    # Remove leading/trailing underscores
    s = s.strip('_')
    return s

# ===================================================================================================
# EXTRACTION FUNCTIONS
# ===================================================================================================

def extract_category_key(row, component_name):
    """Extract the category key based on component name.

    Args:
        row: The data row
        component_name: The name of the component

    Returns:
        str: The category key
    """
    if "allowance" in str(component_name).lower():
        return "CONTRACT_ALLOWANCE"
    elif "base" in str(component_name).lower():
        return "CONTRACT_BASE_PAY"
    elif "deduction" in str(component_name).lower():
        return "EMPLOYEE_DEDUCTION"
    elif "bonus" in str(component_name).lower():
        return "PAY_SUPPLEMENT"


def extract_is_taxable(row, component_name):
    """Determine if a component is taxable based on its name.

    Args:
        row: The data row
        component_name: The name of the component

    Returns:
        str: 'Yes' if taxable, 'No' otherwise
    """
    if "allowance" in str(component_name).lower():
        return "Yes"
    elif "base" in str(component_name).lower():
        return "Yes"
    elif "deduction" in str(component_name).lower():
        return "No"
    elif "bonus" in str(component_name).lower():
        return "Yes"


def extract_is_fixed(row, component_name):
    """Determine if a component has a fixed amount based on its name.

    Args:
        row: The data row
        component_name: The name of the component

    Returns:
        str: 'Yes' if fixed, 'No' otherwise
    """
    if "allowance" in str(component_name).lower():
        return "Yes"
    elif "base" in str(component_name).lower():
        return "Yes"
    elif "deduction" in str(component_name).lower():
        return "No"
    elif "bonus" in str(component_name).lower():
        return "No"


def extract_is_prorated(row, component_name):
    """Determine if a component is prorated based on its name.

    Args:
        row: The data row
        component_name: The name of the component

    Returns:
        str: 'Yes' if prorated, 'No' otherwise
    """
    if "allowance" in str(component_name).lower():
        return "Yes"
    elif "base" in str(component_name).lower():
        return "Yes"
    elif "deduction" in str(component_name).lower():
        return "No"
    elif "bonus" in str(component_name).lower():
        return "No"


def extract_is_mandatory(row, component_name):
    """Determine if a component is mandatory based on its name.

    Args:
        row: The data row
        component_name: The name of the component

    Returns:
        str: 'Yes' if mandatory, 'No' otherwise
    """
    if "allowance" in str(component_name).lower():
        return "No"
    elif "base" in str(component_name).lower():
        return "Yes"
    elif "deduction" in str(component_name).lower():
        return "No"
    elif "bonus" in str(component_name).lower():
        return "No"


def extract_is_part_of_base_pay(row, component_name):
    """Determine if a component is part of base pay based on its name.

    Args:
        row: The data row
        component_name: The name of the component

    Returns:
        str: 'Yes' if part of base pay, 'No' otherwise
    """
    if "allowance" in str(component_name).lower():
        return "No"
    elif "base" in str(component_name).lower():
        return "Yes"
    elif "deduction" in str(component_name).lower():
        return "No"
    elif "bonus" in str(component_name).lower():
        return "No"

def extract_notes(row, use_case):
    """Extract and clean notes from a row, removing special characters.

    Args:
        row: The data row
        use_case: The use case (COMPENSATION or other)

    Returns:
        str: Cleaned notes string with special characters removed/replaced
    """
    if use_case == "COMPENSATION":
        notes = row["condition"]
    else:
        notes = row["notes"]
    if is_nan_or_empty(notes):
        return ""

    # Convert to string if not already
    notes = str(notes)

    # Replace smart quotes and apostrophes with regular ones
    notes = notes.replace("'", "'")
    notes = notes.replace("'", "'")
    notes = notes.replace(""", '"')
    notes = notes.replace(""", '"')

    # Replace em dashes and en dashes with regular hyphens
    notes = notes.replace("—", "-")
    notes = notes.replace("–", "-")

    # Replace other problematic characters
    notes = notes.replace("…", "...")
    notes = notes.replace("•", "*")
    notes = notes.replace("°", " degrees")

    # Replace newlines and carriage returns with spaces to avoid validation errors
    notes = notes.replace('\n', ' ')
    notes = notes.replace('\r', ' ')
    notes = notes.replace('\t', ' ')

    # Remove or replace other non-ASCII characters
    import re
    # Replace any remaining non-ASCII characters with spaces
    notes = re.sub(r'[^\x00-\x7F]', ' ', notes)

    # Clean up multiple spaces
    notes = re.sub(r' +', ' ', notes)

    # Strip leading/trailing whitespace
    notes = notes.strip()

    return notes

def extract_value_safely(row, key):
    value = row.get(key, "")
    if is_nan_or_empty(value):
        value = ""
    return str(value)

# ===================================================================================================
# DATA MAPPING FUNCTIONS
# ===================================================================================================

def get_entity_id_to_company_map(legal_entities_file):
    """Converts the legal_entities sheet to a dictionary mapping entity IDs to company IDs.

    Args:
        legal_entities_file: Path to the legal entities Excel file

    Returns:
        dict: Mapping of entity IDs to company IDs
    """
    try:
        data = read_excel(legal_entities_file)
        entity_id_to_company_map = {}
        for _, row in data.iterrows():
            entity_id = str(row["id"])
            entity_id_to_company_map[entity_id] = str(row["company_id"])
        return entity_id_to_company_map
    except Exception as e:
        log_exception(f"Error converting legal_entities sheet to dictionary: {e}")
        sys.exit(1)


def get_contract_id_to_employee_id_map(contracts_file):
    """Converts the contracts sheet to a dictionary mapping contract IDs to employee IDs.

    Args:
        contracts_file: Path to the contracts Excel file

    Returns:
        dict: Mapping of contract IDs to employee IDs
    """
    try:
        data = read_excel(contracts_file)
        contract_id_to_employee_id_map = {}
        for _, row in data.iterrows():
            contract_id = str(row["id"])
            contract_id_to_employee_id_map[contract_id] = str(row.get("employee_id", ""))
        return contract_id_to_employee_id_map
    except Exception as e:
        log_exception(f"Error converting contracts sheet to dictionary: {e}")
        sys.exit(1)


def get_contract_id_to_entity_id_map(contracts_file):
    """Converts the contracts sheet to a dictionary mapping contract IDs to entity IDs.

    Args:
        contracts_file: Path to the contracts Excel file

    Returns:
        dict: Mapping of contract IDs to entity IDs
    """
    try:
        data = read_excel(contracts_file)
        get_contract_id_to_entity_id_map = {}
        for _, row in data.iterrows():
            contract_id = str(row["id"])
            get_contract_id_to_entity_id_map[contract_id] = str(row.get("workplace_entity_id", ""))
        return get_contract_id_to_entity_id_map
    except Exception as e:
        log_exception(f"Error converting contracts sheet to contract id to entity map: {e}")
        sys.exit(1)

def get_contract_id_to_start_on_map(contracts_file):
    """Converts the contracts sheet to a dictionary mapping contract IDs to start dates.

    Args:
        contracts_file: Path to the contracts Excel file

    Returns:
        dict: Mapping of contract IDs to start dates
    """
    try:
        data = read_excel(contracts_file)
        get_contract_id_to_start_on_map = {}
        for _, row in data.iterrows():
            contract_id = str(row["id"])
            if is_nan_or_empty(row.get("start_on", "")) or str(row["start_on"]) == "" or str(row["start_on"]) == "NaT":
                get_contract_id_to_start_on_map[contract_id] = ""
            else:
                get_contract_id_to_start_on_map[contract_id] = pd.to_datetime(row.get("start_on", ""), format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d')
        return get_contract_id_to_start_on_map
    except Exception as e:
        log_exception(f"Error converting contracts sheet to contract id to start on map: {e}")
        sys.exit(1)


def get_contract_id_to_ended_on_map(contracts_file):
    """Converts the contracts sheet to a dictionary mapping contract IDs to end dates.

    Args:
        contracts_file: Path to the contracts Excel file

    Returns:
        dict: Mapping of contract IDs to end dates
    """
    try:
        data = read_excel(contracts_file)
        contract_id_to_ended_on_map = {}
        for _, row in data.iterrows():
            contract_id = str(row["id"])
            if is_nan_or_empty(row.get("ended_on", "")) or str(row["ended_on"]) == "" or str(row["ended_on"]) == "NaT":
                contract_id_to_ended_on_map[contract_id] = ""
            else:
                contract_id_to_ended_on_map[contract_id] = pd.to_datetime(row.get("ended_on", ""), format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d')
        return contract_id_to_ended_on_map
    except Exception as e:
        log_exception(f"Error converting contracts sheet to contract id to ended on map: {e}")
        sys.exit(1)


def get_contract_id_to_status_map(contracts_file):
    """Converts the contracts sheet to a dictionary mapping contract IDs to status.

    Args:
        contracts_file: Path to the contracts Excel file

    Returns:
        dict: Mapping of contract IDs to status
    """
    try:
        data = read_excel(contracts_file)
        contract_id_to_status_map = {}
        for _, row in data.iterrows():
            contract_id = str(row["id"])
            contract_id_to_status_map[contract_id] = str(row.get("status", ""))
        return contract_id_to_status_map
    except Exception as e:
        log_exception(f"Error converting contracts sheet to contract id to status map: {e}")
        sys.exit(1)


def get_contract_id_to_currency_map(contracts_file):
    """Converts the contracts sheet to a dictionary mapping contract IDs to currency.

    Args:
        contracts_file: Path to the contracts Excel file

    Returns:
        dict: Mapping of contract IDs to currency
    """
    try:
        data = read_excel(contracts_file)
        contract_id_to_currency_map = {}
        for _, row in data.iterrows():
            contract_id = str(row["id"])
            contract_id_to_currency_map[contract_id] = str(row.get("currency", ""))
        return contract_id_to_currency_map
    except Exception as e:
        log_exception(f"Error converting contracts sheet to contract id to currency map: {e}")
        sys.exit(1)


def get_contract_id_to_contributions_map(contributions_for_CAN_file):
    """Converts the contributions sheet to a dictionary mapping contract IDs to contribution data.

    Args:
        contributions_for_CAN_file: Path to the contributions Excel file

    Returns:
        dict: Mapping of contract IDs to contribution data {'EE contribution': value, 'ER contribution': value}
    """
    try:
        data = read_excel(contributions_for_CAN_file, sheet_name="Final Canada migration - Active")
        contract_id_to_contributions_map = {}

        for _, row in data.iterrows():
            contract_id = row.get("Contract ID", "")

            # Skip rows with invalid Contract ID
            if is_nan_or_empty(contract_id) or str(contract_id).strip() == "":
                continue

            # Convert to string and remove decimal if it's a float (e.g., 580607.0 -> 580607)
            contract_id = str(contract_id).strip()
            if contract_id.endswith('.0'):
                contract_id = contract_id[:-2]

            # Extract contribution values, default to 0 if empty/invalid
            ee_contribution = row.get("RRSP EE deductions", 0)
            er_contribution = row.get("RRSP ER Benefits", 0)

            # Handle NaN or empty values
            if is_nan_or_empty(ee_contribution):
                ee_contribution = 0
            if is_nan_or_empty(er_contribution):
                er_contribution = 0

            # Convert to float to handle numeric values
            try:
                ee_contribution = float(ee_contribution)
            except (ValueError, TypeError):
                ee_contribution = 0

            try:
                er_contribution = float(er_contribution)
            except (ValueError, TypeError):
                er_contribution = 0

            contract_id_to_contributions_map[contract_id] = {
                'EE contribution': ee_contribution,
                'ER contribution': er_contribution
            }

        print(f"Loaded contributions data for {len(contract_id_to_contributions_map)} contracts from Canada contributions file")
        return contract_id_to_contributions_map
    except Exception as e:
        log_exception(f"Error converting contributions sheet to contract id to contributions map: {e}")
        sys.exit(1)


def get_contract_id_to_config_id_map(payroll_cycle_config_to_contract_file):
    """Converts the payroll_cycle_config_to_contract sheet to a dictionary mapping contract IDs to config IDs.

    Args:
        payroll_cycle_config_to_contract_file: Path to the payroll cycle config to contract Excel file

    Returns:
        dict: Mapping of contract IDs to config IDs
    """
    try:
        data = read_excel(payroll_cycle_config_to_contract_file)
        contract_id_to_config_id_map = {}
        for _, row in data.iterrows():
            contract_id = str(row["contract_id"])
            config_id = str(row["config_id"])
            contract_id_to_config_id_map[contract_id] = config_id
        return contract_id_to_config_id_map
    except Exception as e:
        log_exception(f"Error converting payroll_cycle_config_to_contract sheet to dictionary: {e}")
        sys.exit(1)

def get_config_id_to_previous_cycle_details_map(configs_and_their_cutoffs_file):
    """Converts the configs_and_their_cutoffs sheet to a dictionary mapping config IDs to payroll details.

    Args:
        configs_and_their_cutoffs_file: Path to the configs and their cutoffs Excel file

    Returns:
        dict: Mapping of config IDs to payroll configuration details
    """
    try:
        data = read_excel(configs_and_their_cutoffs_file)
        config_id_to_details_map = {}
        for _, row in data.iterrows():
            if str(row["category"]) != "PREVIOUS":
                continue
            config_id = str(row["config_id"])

            config_details = {
                'start_date': pd.to_datetime(row.get("start_date", ""), format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d'),
                'end_date': pd.to_datetime(row.get("end_date", ""), format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d'),
                'cut_off_to': pd.to_datetime(row.get("cut_off_to", ""), format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d'),
                'frequency': str(row.get("frequency", ""))
            }

            config_id_to_details_map[config_id] = config_details
        return config_id_to_details_map
    except Exception as e:
        log_exception(f"Error converting configs_and_their_cutoffs sheet to dictionary: {e}")
        sys.exit(1)

def get_contract_id_to_previous_payroll_config_map(contract_id_to_config_id_map, config_id_to_previous_cycle_details_map=None):
    """Combines contract-to-config and config-to-details mappings to create a contract-to-payroll-config mapping.

    Args:
        contract_id_to_config_id_map: Mapping of contract IDs to config IDs
        config_id_to_details_map: Mapping of config IDs to payroll configuration details

    Returns:
        dict: Mapping of contract IDs to payroll configuration details
    """
    try:
        contract_id_to_previous_payroll_config_map = {}
        for contract_id, config_id in contract_id_to_config_id_map.items():
            if config_id in config_id_to_previous_cycle_details_map:
                contract_id_to_previous_payroll_config_map[contract_id] = config_id_to_previous_cycle_details_map[config_id]
            else:
                log_exception(f"[ERROR] Config ID {config_id} not found in configs_and_their_cutoffs for contract {contract_id}")
                # # Provide default empty values
                # contract_id_to_previous_payroll_config_map[contract_id] = {
                #     'start_date': '',
                #     'end_date': '',
                #     'cut_off_to': '',
                #     'frequency': ''
                # }
        return contract_id_to_previous_payroll_config_map
    except Exception as e:
        log_exception(f"Error combining contract and config mappings: {e}")
        sys.exit(1)

def get_config_id_to_new_cycle_details_map(configs_and_their_cutoffs_file):
    """Converts the configs_and_their_cutoffs sheet to a dictionary mapping config IDs to payroll details.

    Args:
        configs_and_their_cutoffs_file: Path to the configs and their cutoffs Excel file

    Returns:
        dict: Mapping of config IDs to payroll configuration details
    """
    try:
        data = read_excel(configs_and_their_cutoffs_file)
        config_id_to_new_cycle_details_map = {}
        for _, row in data.iterrows():
            if str(row["category"]) != "TO_MIGRATE":
                continue
            config_id = str(row["config_id"])
            config_details = {
                'start_date': pd.to_datetime(row.get("start_date", ""), format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d'),
                'end_date': pd.to_datetime(row.get("end_date", ""), format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d'),
                'cut_off_to': pd.to_datetime(row.get("cut_off_to", ""), format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d'),
                'frequency': str(row.get("frequency", ""))
            }
            config_id_to_new_cycle_details_map[config_id] = config_details
        return config_id_to_new_cycle_details_map
    except Exception as e:
        log_exception(f"Error converting configs_and_their_cutoffs sheet to dictionary: {e}")
        sys.exit(1)

def get_contract_id_to_new_payroll_config_map(contract_id_to_config_id_map, config_id_to_new_cycle_details_map=None):
    """Combines contract-to-config and config-to-details mappings to create a contract-to-payroll-config mapping.

    Args:
        contract_id_to_config_id_map: Mapping of contract IDs to config IDs
        config_id_to_new_cycle_details_map: Mapping of config IDs to payroll configuration details

    Returns:
        dict: Mapping of contract IDs to payroll configuration details
    """
    try:
        contract_id_to_new_payroll_config_map = {}
        for contract_id, config_id in contract_id_to_config_id_map.items():
            if config_id in config_id_to_new_cycle_details_map:
                contract_id_to_new_payroll_config_map[contract_id] = config_id_to_new_cycle_details_map[config_id]
            else:
                log_exception(f"[ERROR] Config ID {config_id} not found in configs_and_their_cutoffs for contract {contract_id}")
                # # Provide default empty values
                # contract_id_to_new_payroll_config_map[contract_id] = {
                #     'start_date': '',
                #     'end_date': '',
                #     'cut_off_to': '',
                #     'frequency': ''
                # }
        return contract_id_to_new_payroll_config_map
    except Exception as e:
        log_exception(f"Error combining contract and config mappings: {e}")
        sys.exit(1)

def get_country_to_payroll_dates_map(configs_and_their_cutoffs_file):
    """Creates a mapping of country codes to payroll date configurations for TO_MIGRATE category.

    Args:
        configs_and_their_cutoffs_file: Path to the configs and their cutoffs Excel file

    Returns:
        dict: Mapping of country codes to lists of payroll date configurations
    """

    try:
        data = read_excel(configs_and_their_cutoffs_file)
        country_to_payroll_dates_map = {}
        # Track unique combinations per country to avoid duplicates
        country_unique_combinations = {}

        for _, row in data.iterrows():
            # Filter for TO_MIGRATE category only
            if str(row.get("category", "")).upper() != "TO_MIGRATE":
                continue

            country_code = str(row.get("country", ""))
            if country_code == "" or country_code == "nan":
                log_exception(f"[WARNING] Empty country_code found in configs_and_their_cutoffs for TO_MIGRATE category", level='WARNING')
                continue

            # Extract the key fields for duplicate checking
            frequency = str(row.get("frequency", ""))
            start_date = pd.to_datetime(row.get("start_date", ""), format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d')
            # start_date = str(row.get("start_date", ""))
            end_date = pd.to_datetime(row.get("end_date", ""), format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d')
            # end_date = str(row.get("end_date", ""))
            cut_off_to = str(row.get("cut_off_to", ""))

            # Create unique combination key for duplicate detection
            combination_key = (frequency)

            # Initialize country tracking if not exists
            if country_code not in country_unique_combinations:
                country_unique_combinations[country_code] = set()

            # Check if this combination already exists for this country
            if combination_key in country_unique_combinations[country_code]:
                continue

            # Add combination to tracking set
            country_unique_combinations[country_code].add(combination_key)

            # Create payroll date configuration
            payroll_config = {
                'frequency': frequency,
                'start_date': start_date,
                'end_date': end_date,
                'cut_off_to': cut_off_to
            }

            # Initialize country list if not exists
            if country_code not in country_to_payroll_dates_map:
                country_to_payroll_dates_map[country_code] = []

            # Add configuration to country's list
            country_to_payroll_dates_map[country_code].append(payroll_config)

        return country_to_payroll_dates_map
    except Exception as e:
        log_exception(f"Error creating country to payroll dates mapping: {e}")
        sys.exit(1)

# ===================================================================================================
# CONTRACT STATUS FUNCTIONS
# ===================================================================================================

def should_exclude_onboarding_contract_before_2025(contract_id, contract_id_to_status_map, contract_id_to_start_on_map):
    """Check if a contract should be excluded from onboarding before 1st Jan 2025."""
    if contract_id_to_status_map[contract_id] == 'ONBOARDING':
        contract_start_date = pd.to_datetime(contract_id_to_start_on_map[contract_id], format='%Y-%m-%d', errors='coerce')
        if contract_start_date < pd.to_datetime("2025-01-01", format='%Y-%m-%d', errors='coerce'):
            return True
        else:
            return False

def get_is_onboarding_contract(contract_id, contract_id_to_status_map):
    """Check if a contract is in onboarding status.

    Args:
        contract_id: The contract ID to check
        contract_id_to_status_map: Mapping of contract IDs to status

    Returns:
        bool: True if the contract is in onboarding status, False otherwise
    """
    if contract_id_to_status_map[contract_id] == 'ONBOARDING':
        return True
    return False


def get_is_variable_contract_allowance_to_ignore(row):
    """Check if a variable contract allowance should be ignored.

    Args:
        row: The data row to check

    Returns:
        bool: True if the allowance should be ignored, False otherwise
    """
    if row.get("name") in variable_allowances_to_ignore and row.get("pay_component_type") == "VARIABLE_AMOUNT":
        return True
    return False


def get_contract_id_to_country_map(contracts_file):
    """Converts the contracts sheet to a dictionary mapping contract_id to country_code.

    Args:
        contracts_file: Path to the contracts Excel file

    Returns:
        dict: Mapping of contract IDs to country codes
    """
    try:
        data = read_excel(contracts_file)
        contract_id_to_country_map = {}

        # Check if required columns exist
        country_column = "country"

        for _, row in data.iterrows():
            contract_id = str(row["id"])
            country_code = str(row.get(country_column, ""))
            contract_id_to_country_map[contract_id] = country_code

        return contract_id_to_country_map
    except Exception as e:
        log_exception(f"Error converting contracts sheet to contract id to country map: {e}")
        sys.exit(1)

# ===================================================================================================
# CONTRACT GROUPING FUNCTIONS
# ===================================================================================================

def get_country_entity_to_contracts_map(contracts_file, countries_to_migrate):
    """Creates a map of country + workplace_entity_id as key vs the list of contract IDs.

    Args:
        contracts_file: Path to the contracts Excel file
        countries_to_migrate: List of country codes to include in migration

    Returns:
        dict: Mapping of country+entity keys to lists of contract IDs
    """
    try:
        data = read_excel(contracts_file)
        country_entity_to_contracts_map = {}

        print(f"Processing {len(data)} contracts")

        # Check if required columns exist
        country_column = "country"

        if "workplace_entity_id" not in data.columns:
            log_exception("[ERROR] 'workplace_entity_id' column not found in contracts file")
            sys.exit(1)

        country_codes_found = set()

        for _, row in data.iterrows():
            contract_id = str(row["id"])

            # Get country code from the appropriate column
            country_code = ""
            if country_column:
                country_code = str(row.get(country_column, ""))

            workplace_entity_id = str(row.get("workplace_entity_id", ""))

            country_codes_found.add(country_code)

            # Skip if country is not in the list of countries to migrate
            if country_code not in countries_to_migrate:
                log_exception(f"[WARNING] Invalid country {country_code} found for contract {contract_id}. Skipping.", contract_id, level='WARNING')
                # sys.exit(1)
                continue

            # Create key as country_code + workplace_entity_id
            key = f"{country_code}_{workplace_entity_id}"

            # Add contract_id to the list for this key
            if key not in country_entity_to_contracts_map:
                country_entity_to_contracts_map[key] = []

            country_entity_to_contracts_map[key].append(contract_id)

        print(f"Found country codes: {country_codes_found}")
        print(f"Countries to migrate: {countries_to_migrate}")

        return country_entity_to_contracts_map
    except Exception as e:
        log_exception(f"Error creating country + entity to contracts map: {e}")
        sys.exit(1)

# ===================================================================================================
# SALARY REVIEW FUNCTIONS
# ===================================================================================================

def convert_salary_review_sheets_to_map(salary_reviews_file, salary_review_to_compensation_mapping_file):
    """Converts the salary review sheets to a dictionary mapping compensation IDs to salary reviews.

    Args:
        salary_reviews_file: Path to the salary reviews Excel file
        salary_review_to_compensation_mapping_file: Path to the mapping file

    Returns:
        dict: Mapping of compensation IDs to salary review data
    """
    try:
        data_salary_reviews = read_excel(salary_reviews_file)
        data_salary_review_to_comp_mapping = read_excel(salary_review_to_compensation_mapping_file)
        compensation_id_to_salary_review_map = {}
        compensation_id_to_salary_review_id_map = {}

        for _, row in data_salary_review_to_comp_mapping.iterrows():
            compensation_id_to_salary_review_id_map[str(row["compensation_id"])] = str(row["salary_review_id"])

        for _, row in data_salary_reviews.iterrows():
            # if row["review_status"] != "ACTIVATED":
            #     continue
            salary_review_id = str(row["id"])
            for key in compensation_id_to_salary_review_id_map:
                if compensation_id_to_salary_review_id_map[key] == salary_review_id:
                    compensation_id_to_salary_review_map[key] = row.to_dict()

        return compensation_id_to_salary_review_map
    except Exception as e:
        log_exception(f"Error converting salary review sheets to a dictionary: {e}")
        sys.exit(1)


def get_salary_review_id_to_performance_review_id_map(performance_reviews_file):
    """Converts the performance reviews sheet to a dictionary mapping salary review IDs to performance review IDs.

    Args:
        performance_reviews_file: Path to the performance reviews Excel file

    Returns:
        dict: Mapping of salary review IDs to performance review IDs
    """
    try:
        data = read_excel(performance_reviews_file)
        salary_review_id_to_performance_review_id_map = {}
        for _, row in data.iterrows():
            salary_review_id = str(row["salary_review_id"])
            salary_review_id_to_performance_review_id_map[salary_review_id] = str(row["id"])
        return salary_review_id_to_performance_review_id_map
    except Exception as e:
        log_exception(f"Error converting performance reviews sheet to contract id to entity map: {e}")
        sys.exit(1)


def populate_salary_review_approved_dates_map(salary_review_approved_dates_file):
    """Populates the global salary review approved dates dictionary mapping IDs to lists of records with contract_id, updated_on, effective_date, review_status, and contracting_entity_type.

    Args:
        salary_review_approved_dates_file: Path to the salary review approved dates Excel file
    """
    try:
        data = read_excel(salary_review_approved_dates_file)
        global salary_review_approved_dates_map
        total_records = 0
        for _, row in data.iterrows():
            id_value = str(row["id"])
            record = {
                "contract_id": row.get("contract_id", ""),
                "updated_on": row.get("updated_on", ""),
                "effective_date": row.get("effective_date", ""),
                "review_status": row.get("review_status", ""),
                "contracting_entity_type": row.get("contracting_entity_type", "")
            }

            # If ID already exists, append to the list; otherwise create new list
            if id_value in salary_review_approved_dates_map:
                salary_review_approved_dates_map[id_value].append(record)
            else:
                salary_review_approved_dates_map[id_value] = [record]
            total_records += 1

        print(f"Loaded {total_records} salary review approved dates records for {len(salary_review_approved_dates_map)} unique IDs")
    except Exception as e:
        log_exception(f"Error populating salary review approved dates dictionary: {e}")
        sys.exit(1)


def get_salary_review_approved_date_info_by_entity_criteria(salary_review_id):
    """Helper function to get approved date information based on contracting entity type and review status criteria.

    Criteria:
    - For MULTIPLIER_EOR_ENTITY: get record with review_status = APPROVED, fallback to ACTIVATED
    - For PARTNER_EOR_ENTITY: get record with review_status = SENT_TO_OPS, fallback to ACTIVATED

    Args:
        salary_review_id: The salary review ID to look up

    Returns:
        dict: Dictionary containing updated_on, effective_date, review_status, and contracting_entity_type, or empty dict if not found
    """
    global salary_review_approved_dates_map
    records = salary_review_approved_dates_map.get(str(salary_review_id), [])

    # First pass: Look for primary criteria
    for record in records:
        contracting_entity_type = record.get("contracting_entity_type", "")
        review_status = record.get("review_status", "")

        if contracting_entity_type == "MULTIPLIER_EOR_ENTITY" and review_status == "APPROVED":
            return record
        elif contracting_entity_type == "PARTNER_EOR_ENTITY" and review_status == "SENT_TO_OPS":
            return record

    # Second pass: Fallback to ACTIVATED record if primary criteria not found
    for record in records:
        contracting_entity_type = record.get("contracting_entity_type", "")
        review_status = record.get("review_status", "")
        if review_status == "ACTIVATED":
            log_exception(
                f"[WARNING] No audit record found for APPROVED or SENT_TO_OPS status with contracting_entity_type {contracting_entity_type}. Hence using ACTIVATED record for salary review id {salary_review_id}.",
                record.get("contract_id"), level='WARNING'
            )
            return record

    return {}


def populate_old_allowances_comp_label_to_component_name_map(allowances_comp_label_to_component_name_file):
    """Converts the allowances comp label to component name mapping sheet to a dictionary.

    Args:
        allowances_comp_label_to_component_name_file: Path to the allowances mapping Excel file
    """
    global old_allowances_comp_label_to_component_name_map
    try:
        data = read_excel(allowances_comp_label_to_component_name_file)
        for _, row in data.iterrows():
            label = remove_extra_spaces(str(row["Label"]))
            mapping = str(row["Mapping"])
            old_allowances_comp_label_to_component_name_map[label] = mapping

        print(f"Loaded {len(old_allowances_comp_label_to_component_name_map)} allowances component name mappings")
    except Exception as e:
        log_exception(f"Error converting allowances comp label to component name mapping sheet to a dictionary: {e}")
        sys.exit(1)

# ===================================================================================================
# COMPONENT NAME EXTRACTION FUNCTIONS
# ===================================================================================================

def remove_extra_spaces(string):
    """Remove extra spaces from a string, including spaces between words.

    Args:
        string: The input string

    Returns:
        str: The string with extra spaces removed
    """
    # strip() does not work properly when the extra spaces are between the words. Hence, following this.
    return " ".join(string.split())


def extract_component_name_for_compensation(row):
    """Extract the component name for a compensation row.

    Args:
        row: The compensation data row

    Returns:
        str: The extracted component name
    """
    if is_nan_or_empty(row["name"]):
        source_comp_name = row["label"]
        if source_comp_name in mapping_override_for_component_name:
            log_exception(f"[WARNING] Deriving {source_comp_name} from hardcoded mapping overrides as {mapping_override_for_component_name[source_comp_name]}.", str(row["contract_id"]), metadata=source_comp_name, level='WARNING')
            return mapping_override_for_component_name[source_comp_name]
        else:
            log_exception(f"Could not map label {source_comp_name} with the default schema template name. Using name {source_comp_name}", str(row["contract_id"]), metadata=source_comp_name)
            return source_comp_name
    else:
        source_comp_name = row["name"]
        if source_comp_name == 'allowances':
            label = remove_extra_spaces(str(row.get("label", "")))
            if label in old_allowances_comp_label_to_component_name_map:
                return old_allowances_comp_label_to_component_name_map[label]
            else :
                other_allowance_comp_name = mapping_override_for_component_name['allowances']
                log_exception(f"[ERROR] Could not map old compensation label {label} with the label to schema name mapping. Using name {other_allowance_comp_name}", str(row["contract_id"]), metadata=source_comp_name)
                return other_allowance_comp_name
        if source_comp_name in mapping_override_for_component_name:
            log_exception(f"[WARNING] Deriving {source_comp_name} from hardcoded mapping overrides as {mapping_override_for_component_name[source_comp_name]}.", str(row["contract_id"]), metadata=source_comp_name, level='WARNING')
            return mapping_override_for_component_name[source_comp_name]
        else:
            log_exception(f"[ERROR] Could not map name {source_comp_name} with the default schema template name. Using name {source_comp_name}", str(row["contract_id"]), metadata=source_comp_name)
            return source_comp_name

def extract_currency(compensation_currency, contract_currency):
    """Extract the currency for a compensation, falling back to contract currency if needed.

    Args:
        compensation_currency: The currency from the compensation
        contract_currency: The currency from the contract

    Returns:
        str: The appropriate currency to use
    """
    if is_nan_or_empty(compensation_currency):
        return contract_currency
    else:
        return compensation_currency


def extract_component_name_for_pay_supplement(row):
    """Extracts the Component name for the pay supplement.

    Args:
        row: The pay supplement data row

    Returns:
        str: The extracted component name
    """
    if not is_nan_or_empty(row.get("description", "")) and row.get("description", "") != "":
        comp_name = row["description"]
        if comp_name in mapping_override_for_component_name:
            return mapping_override_for_component_name[comp_name]
    #  else:
    #      log_exception(f"Could not map pay supplement description {comp_name} with the default schema template name. ", str(row["contract_id"]), metadata=comp_name)
    #     #  return comp_name
    if pay_supplement_type_to_name_mapping.get(row.get("type", ""), "") != "":
        comp_name = pay_supplement_type_to_name_mapping[row.get("type", "")]
        if comp_name in mapping_override_for_component_name:
            return mapping_override_for_component_name[comp_name]
        else:
            log_exception(f"[ERROR] Could not map pay supplement type {comp_name} with the default schema template name. ", str(row["contract_id"]), metadata=comp_name)
            return comp_name
    else:
        log_exception(f"[ERROR] Could not map pay supplement type {row.get('type', '')} with the default schema template name. ", str(row["contract_id"]), metadata=comp_name)
        sys.exit(1)


def extract_billing_rate_type(row):
    """Extracts the Billing rate type from a row.

    Args:
        row: The data row

    Returns:
        str: The billing rate type
    """
    return "Value"


def extract_billing_frequency(row):
    """Extract the billing frequency for a row, with mapping to new format.

    Args:
        row: The data row

    Returns:
        str: The billing frequency in the new format
    """
    old_schema_frequency = row["frequency"]
    if old_schema_frequency in old_schema_frequency_to_new_billing_frequency_overrides:
        return old_schema_frequency_to_new_billing_frequency_overrides[old_schema_frequency]
    else:
        return old_schema_frequency

# ===================================================================================================
# PAY SCHEDULE FUNCTIONS
# ===================================================================================================

def extract_default_pay_schedule_frequency(contract_id, contract_id_to_new_payroll_config_map=None):
    """Extracts the default pay schedule frequency for a contract."""
    if contract_id in contract_id_to_new_payroll_config_map:
        return contract_id_to_new_payroll_config_map[contract_id]["frequency"]

    return "MONTHLY"

def extract_is_base_component_for_pay_schedule_derivation(component_name):
    if "total_cost_to_company" in component_name:
        return True
    if "basePay" in component_name:
        return True
    if "Gross Salary" in component_name:
        return True
    if "gross_salary" in component_name:
        return True
    if "total_ctc" in component_name:
        return True
    if "base" in component_name:
        return True
    return False

def extract_pay_schedule_name_for_compensation(row, pay_schedule_data_dictionary, contract_id_to_new_payroll_config_map=None):
    """Extracts the Pay schedule name from a compensation row.

    Args:
        row: The compensation data row
        pay_schedule_data_dictionary: Dictionary mapping frequencies to pay schedule names

    Returns:
        str: The pay schedule name
    """
    try:
        component_name = extract_component_name_for_compensation(row)
        is_base_component = extract_is_base_component_for_pay_schedule_derivation(component_name)

        old_schema_payment_frequency = row["payment_frequency"]
        old_schema_billing_frequency = row["frequency"]
        contract_id = row["contract_id"]
        default_pay_schedule_frequency = extract_default_pay_schedule_frequency(contract_id, contract_id_to_new_payroll_config_map)

        if is_base_component:
            if is_nan_or_empty(old_schema_payment_frequency):
                log_exception(f"[WARNING] Could not tag a pay schedule frequency for contract {row['contract_id']}. Picking default payment frequency as {default_pay_schedule_frequency}.", row["contract_id"], level='WARNING')
                pay_schedule_frequency = default_pay_schedule_frequency
            else:
                pay_schedule_frequency = old_schema_frequency_to_pay_schedule_frequency_overrides.get(old_schema_payment_frequency, "")
        else:
            if is_nan_or_empty(old_schema_payment_frequency) and is_nan_or_empty(old_schema_billing_frequency):
                log_exception(f"[WARNING] Could not tag a pay schedule frequency for contract {row['contract_id']}. Picking default payment frequency as {default_pay_schedule_frequency}.", row["contract_id"], level='WARNING')
                pay_schedule_frequency = default_pay_schedule_frequency
            elif is_nan_or_empty(old_schema_payment_frequency):
                pay_schedule_frequency = old_schema_frequency_to_pay_schedule_frequency_overrides.get(old_schema_billing_frequency, "")
            else:
                pay_schedule_frequency = old_schema_frequency_to_pay_schedule_frequency_overrides.get(old_schema_payment_frequency, "")

        if is_nan_or_empty(pay_schedule_frequency):
            pay_schedule_frequency = default_pay_schedule_frequency
            log_exception(f"[WARNING] Could not tag a pay schedule name to the row for contract {row['contract_id']}. Please check whether the payment frequency is present in the old schema sheet for all rows. Picking default payment frequency as {default_pay_schedule_frequency}.", row["contract_id"], level='WARNING')

        if pay_schedule_frequency not in pay_schedule_data_dictionary:
            # Add the missing pay schedule to the dictionary
            log_exception(f"[ERROR] Pay schedule frequency '{pay_schedule_frequency}' not found in pay_schedule_data_dictionary for contract {row['contract_id']}. Using default frequency {default_pay_schedule_frequency}.", contract_id=row['contract_id'], metadata=row)
            pay_schedule_frequency = default_pay_schedule_frequency

        return pay_schedule_data_dictionary[pay_schedule_frequency]
    except KeyError as e:
        log_exception(f"Could not tag a pay schedule name to the row for contract {row['contract_id']} Using MONTHLY as default. Error: {e}", row['contract_id'])
        # Use default pay schedule as fallback
        return pay_schedule_data_dictionary["MONTHLY"]

def extract_pay_schedule_name_for_pay_supplement(row, pay_schedule_data_dictionary):
    """Extracts the Pay schedule name for pay supplement.

    Args:
        row: The pay supplement data row
        pay_schedule_data_dictionary: Dictionary mapping frequencies to pay schedule names

    Returns:
        str: The pay schedule name for the pay supplement
    """
    try:
        if "ONE_TIME" not in pay_schedule_data_dictionary:
            # Add the missing pay schedule to the dictionary
            log_exception(f"[WARNING] Pay schedule frequency 'ONE_TIME' not found in pay_schedule_data_dictionary for contract {row['contract_id']}. Adding it with default name.", row['contract_id'], level='WARNING')
            pay_schedule_data_dictionary["ONE_TIME"] = "One_time_Installment"

        return pay_schedule_data_dictionary["ONE_TIME"]
    except KeyError as e:
        log_exception(f"Could not tag a pay schedule name to the row for contract {row['contract_id']}. Error: {e}", row['contract_id'])
        # Use a default name
        pay_schedule_data_dictionary["ONE_TIME"] = "One_time_Installment"
        return "One_time_Installment"

# ===================================================================================================
# DATE EXTRACTION FUNCTIONS
# ===================================================================================================

def extract_payroll_start_date_to_start_new_items_from(contract_id, contract_id_to_new_payroll_config_map=None):
    if contract_id in contract_id_to_new_payroll_config_map:
        return contract_id_to_new_payroll_config_map[contract_id]["start_date"]

    return default_payroll_start_date_to_start_new_items_from

def extract_start_date(row, compensation_type, compensation_id_to_salary_review_map, contract_id_to_start_on_map):
    """Extracts the Start date from a row.

    Args:
        row: The data row
        compensation_type: Type of compensation (COMPENSATION or PAY_SUPPLEMENT)
        compensation_id_to_salary_review_map: Mapping of compensation IDs to salary reviews
        contract_id_to_start_on_map: Mapping of contract IDs to start dates

    Returns:
        str: The start date in YYYY-MM-DD format
    """

    # By default, start with the created on/updated on as the result_start_date.
    result_start_date = pd.to_datetime(row["created_on"], format='%Y-%m-%d', errors='coerce')
    if str(row["created_on"]) == "" or str(row["created_on"]) == "NaT":
        result_start_date = pd.to_datetime(row["updated_on"], format='%Y-%m-%d', errors='coerce')

    # Check if contract has start on. If yes, override result_start_date with it.
    contract_id = str(row["contract_id"])
    if contract_id in contract_id_to_start_on_map and compensation_type == 'COMPENSATION':
        result_start_date = pd.to_datetime(contract_id_to_start_on_map[contract_id], format='%Y-%m-%d', errors='coerce')

    # Check if the compensation has pay component info. If yes, override result_start_date with it.
    # Ensure that pay component info is not before start date of contract.
    if not is_nan_or_empty(row.get("pay_component_info", "")):
        # Try to extract start date from pay_component_info
        try:
            if not is_nan_or_empty(row.get("pay_component_info", "")):
                import json
                pay_component_info = json.loads(row["pay_component_info"])

                # Check for fixedPayComponentInfo.payOn field
                if pay_component_info.get("fixedPayComponentInfo") and pay_component_info["fixedPayComponentInfo"].get("payOn"):
                    pay_on = pay_component_info["fixedPayComponentInfo"]["payOn"]
                    # Parse the date format (e.g., "4-2022" to "2022-05-01")
                    # Note: month is 0-indexed in the payOn field (0=January, 1=February, etc.)
                    if "-" in pay_on:
                        month_indexed, year = pay_on.split("-")
                        if compensation_type == 'COMPENSATION':
                            # Convert from 0-indexed to 1-indexed month
                            month = str(int(month_indexed) + 1)
                        else:
                            month = str(int(month_indexed))
                        # Create a date for the 1st day of the month
                        start_date = f"{year}-{month.zfill(2)}-01"
                        contract_id = str(row["contract_id"])
                        result_start_date = pd.to_datetime(start_date, format='%Y-%m-%d', errors='coerce')

                        # Compare pay component info start on with contract start on, it should be after contract start on.
                        if contract_id in contract_id_to_start_on_map and compensation_type == 'COMPENSATION':
                            contract_start_date = pd.to_datetime(contract_id_to_start_on_map[contract_id], format='%Y-%m-%d', errors='coerce')
                            if contract_start_date > result_start_date:
                                result_start_date = contract_start_date

        except Exception as e:
            # If there's any error parsing the pay_component_info, fall back to the default logic
            log_exception(f"Error parsing pay_component_info: {e}", str(row.get("contract_id", "")), level='ERROR')

    # Check if this compensation row is a product of an SR, use the SR effective date as the result_start_date.
    compensation_id = str(row["id"])
    salary_review = compensation_id_to_salary_review_map.get(compensation_id, "")
    if salary_review != "":
        salary_review_effective_date = salary_review["effective_date"]
        if compensation_type == "COMPENSATION" and salary_review_effective_date != "":
            result_start_date = pd.to_datetime(salary_review_effective_date, format='%Y-%m-%d', errors='coerce')

    return result_start_date.strftime('%Y-%m-%d')

def extract_end_date(row, compensation_type, contract_id_to_ended_on_map, contract_id_to_status_map):
    """Extracts the End date from a row.

    Args:
        row: The data row
        compensation_type: Type of compensation (COMPENSATION or PAY_SUPPLEMENT)
        contract_id_to_ended_on_map: Mapping of contract IDs to end dates
        contract_id_to_status_map: Mapping of contract IDs to status

    Returns:
        str: The end date in YYYY-MM-DD format, or empty string if no end date
    """
    contract_id = str(row["contract_id"])
    if compensation_type != "COMPENSATION":
        source_start_date = pd.to_datetime(row["created_on"], format='%Y-%m-%d', errors='coerce')
        return source_start_date.strftime('%Y-%m-%d')
    else:
        if contract_id_to_status_map[contract_id] == 'OFFBOARDING' or contract_id_to_status_map[contract_id] == 'ENDED':
            return contract_id_to_ended_on_map[contract_id]
        return ""

def extract_cutoff_date(contract_id, contract_id_to_previous_payroll_config_map):
    cutoff = default_previous_cycle_cut_off_date
    if country_code in country_to_cutoff_map_overrides:
        cutoff = country_to_cutoff_map_overrides[country_code]
    if contract_id in contract_id_to_previous_payroll_config_map and contract_id_to_previous_payroll_config_map[contract_id]["cut_off_to"] is not None:
        print(f"Using cutoff date {contract_id_to_previous_payroll_config_map[contract_id]['cut_off_to']} for contract {contract_id}")
        cutoff = contract_id_to_previous_payroll_config_map[contract_id]["cut_off_to"]
    return cutoff

def extract_end_date_for_old_comp(row, compensation_type):
    """Extracts the End date from an old compensation row.

    Args:
        row: The data row
        compensation_type: Type of compensation (COMPENSATION or PAY_SUPPLEMENT)

    Returns:
        str: The end date in YYYY-MM-DD format
    """
    if compensation_type != "COMPENSATION":
        source_start_date = pd.to_datetime(row["created_on"], format='%Y-%m-%d', errors='coerce')
        return source_start_date.strftime('%Y-%m-%d')
    else:
        source_updated_date = pd.to_datetime(row["updated_on"], format='%Y-%m-%d', errors='coerce')
        return source_updated_date.strftime('%Y-%m-%d')

def extract_processed_until_date(row, compensation_type, contract_id_to_status_map, contract_id_to_ended_on_map, start_date=None, contract_id_to_new_payroll_config_map=None, pay_schedule_data_dictionary=None):
    """Extracts the Processed until date from a row.

    Args:
        row: The data row
        compensation_type: Type of compensation (COMPENSATION or PAY_SUPPLEMENT)
        contract_id_to_status_map: Mapping of contract IDs to status
        contract_id_to_ended_on_map: Mapping of contract IDs to end dates
        start_date: Optional start date to use for calculations

    Returns:
        str: The processed until date in YYYY-MM-DD format
    """
    contract_id = str(row["contract_id"])
    payroll_start_date_to_start_new_items_from = extract_payroll_start_date_to_start_new_items_from(contract_id, contract_id_to_new_payroll_config_map)
    if compensation_type != "COMPENSATION":
        return get_processed_until_date_for_pay_supplements(start_date, "ONE_TIME", row)
    else:
        if is_recurring_variable_compensation(row):
            return get_processed_until_date_for_pay_supplements(start_date, extract_billing_frequency(row), row)
        else:
            if ((contract_id_to_status_map[contract_id] == 'OFFBOARDING' or contract_id_to_status_map[contract_id] == 'ENDED')
                    and contract_id_to_ended_on_map[contract_id] is not None and contract_id_to_ended_on_map[contract_id] != ""):
                if pd.to_datetime(contract_id_to_ended_on_map[contract_id], format='%Y-%m-%d', errors='coerce') < pd.to_datetime(payroll_start_date_to_start_new_items_from, format='%Y-%m-%d', errors='coerce'):
                    return contract_id_to_ended_on_map[contract_id]

            pay_schedule_name = extract_pay_schedule_name_for_compensation(row, pay_schedule_data_dictionary, contract_id_to_new_payroll_config_map)
            if 'Annually' in pay_schedule_name or 'Quarterly' in pay_schedule_name: # Also includes Semi-Annually
                return extrapolate_start_date_after_lowest_possible_date(
                    compensation_start_date=start_date,
                    earlier_possible_date=pd.to_datetime(payroll_start_date_to_start_new_items_from, format='%Y-%m-%d', errors='coerce'),
                    billing_frequency=extract_billing_frequency(row)
                )
            else:
                payroll_start_date = pd.to_datetime(payroll_start_date_to_start_new_items_from, format='%Y-%m-%d', errors='coerce')
                compensation_start_date=pd.to_datetime(start_date, format='%Y-%m-%d', errors='coerce')
                if compensation_start_date >= payroll_start_date:
                    return ''
                else:
                    processed_until_date =  payroll_start_date - pd.Timedelta(days=1)
                    return processed_until_date.strftime('%Y-%m-%d')

def get_processed_until_date_for_pay_supplements(start_date, billing_frequency, row=None):
    """Calculate the processed until date for pay supplements.

    Args:
        start_date: The start date of the pay supplement
        billing_frequency: The billing frequency
        row: Optional data row with additional information

    Returns:
        str: The processed until date in YYYY-MM-DD format, or empty string
    """
    # If row is provided and status is PAID or PROCESSING, return START_DATE as PROCESSED_UNTIL_DATE
    # if row is not None and "status" in row:
    #     status = row.get("status", "")
    #     if status in ["PAID", "PROCESSING"]:
    #         print(f'Pay Supplement status is [{status}] returning start_date [{start_date}] as PROCESSED_UNTIL_DATE.')
    #         return start_date

    # Otherwise, use the original logic
    earliest_date_for_item_generation_for_recurring_pay_supplement = pd.to_datetime(default_recurring_pay_supplement_item_generation_start, format='%Y-%m-%d', errors='coerce')
    lowest_possible_processed_until_date_for_recurring_pay_supplements =  earliest_date_for_item_generation_for_recurring_pay_supplement - pd.Timedelta(days=1)
    compensation_start_date = pd.to_datetime(start_date, format='%Y-%m-%d', errors='coerce')
    if compensation_start_date < earliest_date_for_item_generation_for_recurring_pay_supplement:
        if billing_frequency == "ANNUALLY" or billing_frequency == "SEMIANNUALLY" or billing_frequency == "QUARTERLY":
            return extrapolate_start_date_after_lowest_possible_date(
                compensation_start_date,
                earlier_possible_date=earliest_date_for_item_generation_for_recurring_pay_supplement,
                billing_frequency=billing_frequency
            )
        return lowest_possible_processed_until_date_for_recurring_pay_supplements.strftime('%Y-%m-%d')
    else:
        return ''

def extrapolate_start_date_after_lowest_possible_date(compensation_start_date, earlier_possible_date, billing_frequency):
    """
    Extrapolates the start date with corresponding frequency until it comes after
    earlier_possible_start_date.

    Args:
        compensation_start_date: The original start date of the compensation
        earlier_possible_date: The earliest date allowed for item generation
        billing_frequency: The frequency of billing (ANNUALLY, SEMIANNUALLY, QUARTERLY)

    Returns:
        A date string in format 'YYYY-MM-DD' representing the processed until date
    """
    # Convert dates to datetime objects for calculations
    start_date = pd.to_datetime(compensation_start_date, format='%Y-%m-%d', errors='coerce')
    earliest_date = pd.to_datetime(earlier_possible_date, format='%Y-%m-%d', errors='coerce')

    # If start date is already after or equal to the earliest date, return empty string
    # (indicating no processed until date needed)
    if start_date >= earliest_date:
        return ''

    # Initialize extrapolated date with the start date
    extrapolated_date = start_date

    # Extrapolate the date based on billing frequency until it's after the earliest date
    while extrapolated_date < earliest_date:
        if billing_frequency == "ANNUALLY":
            extrapolated_date = extrapolated_date + pd.DateOffset(years=1)
        elif billing_frequency == "SEMIANNUALLY":
            extrapolated_date = extrapolated_date + pd.DateOffset(months=6)
        elif billing_frequency == "QUARTERLY":
            extrapolated_date = extrapolated_date + pd.DateOffset(months=3)

    # Return the day before the extrapolated date as the processed until date
    processed_until_date = extrapolated_date - pd.Timedelta(days=1)
    return processed_until_date.strftime('%Y-%m-%d')

# ===================================================================================================
# COMPENSATION TYPE FUNCTIONS
# ===================================================================================================

def is_recurring_variable_compensation(row):
    """Check if a row represents recurring variable compensation.

    Args:
        row: The data row to check

    Returns:
        bool: True if the row is recurring variable compensation, False otherwise
    """
    name = row["name"]
    label = row["label"]
    pay_component_type = row["pay_component_type"]

    if name == "variablePerformanceBonus" and label == "variable-performance-bonus" and pay_component_type == "VARIABLE_AMOUNT":
        return True

    if name == "other" and pay_component_type == "VARIABLE_AMOUNT":
        return True

    return False


def exclude_recurring_variable_or_one_time_pay_supplement_already_considered_from_compensation_table(row):
    """Check if a pay supplement should be excluded because it's already considered in the compensation table.

    Args:
        row: The pay supplement data row to check

    Returns:
        bool: True if the pay supplement should be excluded, False otherwise
    """
    type = row["type"]
    description = row["description"]

    if type == 'BONUS' and description == 'Joining bonus':
        return True

    if type == 'OTHER' and description == 'Other':
        return True

    if type == 'VARIABLE_PERFORMANCE_BONUS':
        return True

    return False

# ===================================================================================================
# EFFECTIVE DATE FUNCTIONS
# ===================================================================================================

def extract_source_effective_date_for_compensation(compensation_data_row, compensation_id_to_salary_review_map, contract_id_to_previous_payroll_config_map=None, contract_id_to_new_payroll_config_map=None, contract_id_to_start_on_map=None):
    """Extracts the Source effective date from a compensation row.

    Args:
        compensation_data_row: The compensation data row
        compensation_id_to_salary_review_map: Mapping of compensation IDs to salary reviews
        cutoff: The cutoff date for determining effective dates
        contract_id_to_start_on_map: Mapping of contract IDs to start dates

    Returns:
        str: The source effective date in YYYY-MM-DD format
    """

    cutoff = extract_cutoff_date(str(compensation_data_row["contract_id"]), contract_id_to_previous_payroll_config_map)
    updated_on = pd.to_datetime(compensation_data_row["updated_on"], format='%Y-%m-%d', errors='coerce')
    payroll_start_date_to_start_new_items_from = extract_payroll_start_date_to_start_new_items_from(str(compensation_data_row["contract_id"]), contract_id_to_new_payroll_config_map)

    start_date_str = extract_start_date(compensation_data_row, "COMPENSATION", {}, contract_id_to_start_on_map)
    start_date = pd.to_datetime(start_date_str, format='%Y-%m-%d', errors='coerce')
    payroll_start_date = pd.to_datetime(payroll_start_date_to_start_new_items_from, format='%Y-%m-%d', errors='coerce')

    compensation_id = str(compensation_data_row["id"])
    salary_revision_data = compensation_id_to_salary_review_map.get(compensation_id, {})
    effective_date = start_date
    is_sr = False
    if salary_revision_data:
        salary_revision_date = pd.to_datetime(get_salary_revision_date(salary_revision_data), format='%Y-%m-%d', errors='coerce')
        if salary_revision_date is not None:
            is_sr = True
            effective_date = salary_revision_date

    # Check if effective_date is NaT (Not a Time)
    if pd.isna(effective_date):
        # Use a default date if effective_date is NaT
        return payroll_start_date_to_start_new_items_from

    if effective_date > pd.to_datetime(cutoff, format='%Y-%m-%d', errors='coerce') and compensation_data_row["status"] == "ACTIVE":
        # This might cause even very past dated compensations which were created long back to be skipped from being sent to payroll.
        # Example - Contract id - 562103
        # if is_sr:
        #     return pd.to_datetime(date_to_trigger_revisions_in_payroll_input, format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d')
        # else:
        #     # Might be the case of ONBOARDING contract. Needs to be handled based on updated_on
        #     if updated_on > pd.to_datetime(cutoff, format='%Y-%m-%d', errors='coerce'):
        #         return pd.to_datetime(date_to_trigger_revisions_in_payroll_input, format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d')
        #     else:
        #         return updated_on.strftime('%Y-%m-%d')
        return pd.to_datetime(date_to_trigger_revisions_in_payroll_input, format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d')
    else:
        return effective_date.strftime('%Y-%m-%d')

def get_salary_revision_date(salary_revision_data):
    """Extracts the salary revision date from salary review data based on contracting entity type and review status criteria.

    Criteria:
    - For MULTIPLIER_EOR_ENTITY: get updated_on from record with review_status = SENT_TO_OPS, fallback to ACTIVATED
    - For PARTNER_EOR_ENTITY: get updated_on from record with review_status = APPROVED, fallback to ACTIVATED
    - If neither primary criteria nor ACTIVATED record found, fallback to original salary review effective_date

    Args:
        salary_revision_data: The salary review data

    Returns:
        datetime: The salary revision date
    """
    salary_review_id = salary_revision_data.get("id")

    # Get the appropriate record based on entity criteria
    salary_review_audit_info = get_salary_review_approved_date_info_by_entity_criteria(salary_review_id)

    # If no matching record found, log warning and use fallback
    if not salary_review_audit_info:
        log_exception(
            f"[WARNING] No matching audit record found for salary review id {salary_review_id} with required contracting_entity_type and review_status criteria. Fallback to effective_date {salary_revision_data['effective_date']} from salary review data.",
            salary_revision_data.get("contract_id"), level='WARNING'
        )
        return salary_revision_data["effective_date"]

    updated_on_from_sr_aud = pd.to_datetime(salary_review_audit_info.get("updated_on"), format='%Y-%m-%d', errors='coerce')
    effective_date_from_sr_aud = pd.to_datetime(salary_review_audit_info.get("effective_date"), format='%Y-%m-%d', errors='coerce')

    if not pd.isna(updated_on_from_sr_aud) and not pd.isna(effective_date_from_sr_aud):
        if updated_on_from_sr_aud > effective_date_from_sr_aud:
            return updated_on_from_sr_aud
        else:
            return effective_date_from_sr_aud
    else:
        # Fallback to effective_date if either date is missing.
        log_exception(
            f"[WARNING] Either updated_on_from_sr_aud or effective_date_from_sr_aud is missing for salary review id {salary_review_id}. Fallback to effective_date {salary_revision_data['effective_date']} from salary review data.",
            salary_revision_data.get("contract_id"), level='WARNING'
        )
        return salary_revision_data["effective_date"]

def extract_source_effective_date_for_pay_supplement(compensation_data_row, compensation_id_to_salary_review_map, contract_id_to_previous_payroll_config_map=None, contract_id_to_new_payroll_config_map=None, contract_id_to_start_on_map=None):
    """Extracts the Source effective date from a pay supplement row.

    Args:
        compensation_data_row: The pay supplement data row
        compensation_id_to_salary_review_map: Mapping of compensation IDs to salary reviews
        cutoff: The cutoff date for determining effective dates
        contract_id_to_start_on_map: Optional mapping of contract IDs to start dates

    Returns:
        str: The source effective date in YYYY-MM-DD format
    """
    cutoff = extract_cutoff_date(str(compensation_data_row["contract_id"]), contract_id_to_previous_payroll_config_map)



    start_date_str = extract_start_date(compensation_data_row, "PAY_SUPPLEMENT", {}, contract_id_to_start_on_map)
    effective_date = pd.to_datetime(start_date_str, format='%Y-%m-%d', errors='coerce')

    # Check if effective_date is NaT (Not a Time)
    if pd.isna(effective_date):
        # Use a default date if effective_date is NaT
        return extract_payroll_start_date_to_start_new_items_from(str(compensation_data_row["contract_id"]), contract_id_to_new_payroll_config_map)

    if effective_date >= pd.to_datetime(cutoff, format='%Y-%m-%d', errors='coerce'):
        return pd.to_datetime(date_to_trigger_revisions_in_payroll_input, format='%Y-%m-%d', errors='coerce').strftime('%Y-%m-%d')
    else:
        return effective_date.strftime('%Y-%m-%d')

# ===================================================================================================
# REVISION FUNCTIONS
# ===================================================================================================

def extract_is_backfill_for_revision(compensation_data_row, compensation_id_to_salary_review_map):
    """Determine if a compensation row is for a backfill revision.

    Args:
        compensation_data_row: The compensation data row
        compensation_id_to_salary_review_map: Mapping of compensation IDs to salary reviews

    Returns:
        str: 'Yes' if the row is for a backfill revision, 'No' otherwise
    """
    compensation_id = compensation_data_row["id"]
    if compensation_id_to_salary_review_map.get(str(compensation_id), {}) != {}:
        return "Yes"
    return "No"


def extract_revision_id_or_empty(compensation_data_row, compensation_id_to_salary_review_map):
    """Extract the revision ID for a compensation row, or empty string if none exists.

    Args:
        compensation_data_row: The compensation data row
        compensation_id_to_salary_review_map: Mapping of compensation IDs to salary reviews

    Returns:
        str: The revision ID, or empty string if none exists
    """
    compensation_id = compensation_data_row["id"]
    if compensation_id_to_salary_review_map.get(str(compensation_id), {}) != {}:
        salary_review = compensation_id_to_salary_review_map.get(str(compensation_id), {})
        salary_review_id = salary_review["id"]
        return salary_review_id
    return ""


def exclude_salary_revision_from_backfill(compensation_data_row, compensation_id_to_salary_review_map, cutoff):
    """Determine if a salary revision should be excluded from backfill.

    Args:
        compensation_data_row: The compensation data row
        compensation_id_to_salary_review_map: Mapping of compensation IDs to salary reviews
        cutoff: The cutoff date for determining exclusion

    Returns:
        bool: True if the salary revision should be excluded, False otherwise
    """
    compensation_id = str(compensation_data_row["id"])
    salary_revision_data = compensation_id_to_salary_review_map.get(compensation_id, {})
    if salary_revision_data:  # Changed from 'if not salary_revision_data:'
        salary_revision_date = salary_revision_data.get("effective_date")
        if salary_revision_date is not None:
            effective_date = pd.to_datetime(salary_revision_date, format='%Y-%m-%d', errors='coerce')
            if effective_date >= pd.to_datetime(cutoff, format='%Y-%m-%d', errors='coerce'):
                return True
    return False


def exclude_salary_revision_from_compensation_revision(compensation_data_row, compensation_id_to_salary_review_map, cutoff):
    """Determine if a salary revision should be excluded from compensation revision.

    Args:
        compensation_data_row: The compensation data row
        compensation_id_to_salary_review_map: Mapping of compensation IDs to salary reviews
        cutoff: The cutoff date for determining exclusion

    Returns:
        bool: True if the salary revision should be excluded, False otherwise
    """
    compensation_id = str(compensation_data_row["id"])
    salary_revision_data = compensation_id_to_salary_review_map.get(compensation_id, {})
    if salary_revision_data is None:
        return True
    if salary_revision_data == {}:
        return True
    if salary_revision_data["review_status"] != "SENT_TO_OPS":
        return True
    effective_date = pd.to_datetime(salary_revision_data["effective_date"], format='%Y-%m-%d', errors='coerce')
    if effective_date is None:
        return True
    if effective_date < pd.to_datetime(cutoff, format='%Y-%m-%d', errors='coerce'):
        return True
    return False

# ===================================================================================================
# DATA TRANSFORMATION FUNCTIONS
# ===================================================================================================

def transform_data_for_schema(default_schema, compensation_data, pay_supplements_data, entity, contract_id_to_entity_id_map, country_code):
    """Transforms the data to prepare customer schema sheet.

    Args:
        default_schema: The default schema data
        compensation_data: The compensation data
        pay_supplements_data: The pay supplements data
        entity: The entity ID
        contract_id_to_entity_id_map: Mapping of contract IDs to entity IDs
        country_code: The country code

    Returns:
        DataFrame: The transformed data for the schema sheet
    """
    transformed_rows = []
    customer_schema_map = {}
    schema_currency = ""
    schema_description = ""

    # Process compensations data.
    for _, row in default_schema.iterrows():
        if row.get("Is Mandatory?", "") != "Yes" and row.get("Is Mandatory?", "") != "No":
            continue

        # Get the label from the input schema if available
        # Try different possible column names for the label
        if "Label" in row:
            label = row["Label"]
        elif "Component Display Name" in row:
            label = row["Component Display Name"]
        elif "LABEL" in row:
            label = row["LABEL"]
        else:
            # If no label column is found, use the component name as the label
            label = row.get("Component Name", "")

        transformed_row = {
            "DO NOT MODIFY OR DELETE THIS ROW": str(""),
            "SCHEMA_NAME": str(get_compensation_schema_name(country_code)),
            "COMPONENT_NAME": str(str(row["Component Name"])),
            "CATEGORY_KEY": str(row["Category Key"]),
            "IS_TAXABLE": str(row["Is Taxable?"]),
            "IS_FIXED": str(row["Is Fixed?"]),
            "IS_PRORATED": str(row["Is Prorated?"]),
            "IS_MANDATORY": str(row["Is Mandatory?"]),
            "IS_PART_OF_BASE_PAY": str(row["Is Part of BasePay?"]),
            "COUNTRY_CODE": str(country_code),
            "TAGS": str(tags),
            "LABEL": str(label),
        }
        if new_schema_format_enabled:
            transformed_row["ITEM_TYPE"] =  extract_value_safely(row, "Item Type")
            transformed_row["VALIDATION"] = extract_value_safely(row, "Validation")
            transformed_row["CALCULATION"] = extract_value_safely(row, "Calculation")
            transformed_row["BILLING_RATE_TYPE"] = extract_value_safely(row, "Billing Rate Type")
            transformed_row["IS_OVERTIME_ELIGIBLE"] = extract_value_safely(row, "Is Overtime Eligible?")
            transformed_row["COMPONENT_DESCRIPTION"] = extract_value_safely(row, "Component Description")
            transformed_row["BILLING_FREQUENCY"] = extract_value_safely(row, "Billing Frequency")
            transformed_row["PAY_SCHEDULE_NAME"] = extract_value_safely(row, "Pay Schedule Name")
            transformed_row["CURRENCY"] = extract_value_safely(row, "Currency")
            transformed_row["SCHEMA_DESCRIPTION"] = extract_value_safely(row, "Schema Description")
            transformed_row["IS_PART_OF_CTC"] = extract_value_safely(row, "Is Part of CTC?")
            schema_currency = transformed_row["CURRENCY"]
            schema_description = transformed_row["SCHEMA_DESCRIPTION"]
        transformed_rows.append(transformed_row)
        customer_schema_map[row["Component Name"]] = transformed_row

    for _, row in compensation_data.iterrows():
        contract_id = str(row["contract_id"])
        if contract_id not in contract_id_to_entity_id_map:
            continue
        if str(contract_id_to_entity_id_map[contract_id]) != entity:
            continue
        component_name = extract_component_name_for_compensation(row)
        if component_name in component_names_to_ignore:
            continue
        if is_nan_or_empty(component_name):
            continue
        if component_name in customer_schema_map:
            continue
        transformed_row = {
            "DO NOT MODIFY OR DELETE THIS ROW": "",
            "SCHEMA_NAME": get_compensation_schema_name(country_code),
            "COMPONENT_NAME": str(component_name),
            "CATEGORY_KEY": extract_category_key(row, component_name),
            "IS_TAXABLE": extract_is_taxable(row, component_name),
            "IS_FIXED": extract_is_fixed(row, component_name),
            "IS_PRORATED": extract_is_prorated(row, component_name),
            "IS_MANDATORY": extract_is_mandatory(row, component_name),
            "IS_PART_OF_BASE_PAY": extract_is_part_of_base_pay(row, component_name),
            "COUNTRY_CODE": country_code,
            "TAGS": tags,
            "LABEL": component_name  # Use component name as label if no specific label is available
        }
        if new_schema_format_enabled:
            transformed_row["ITEM_TYPE"] =  "INPUT"
            transformed_row["VALIDATION"] = ""
            transformed_row["CALCULATION"] = ""
            transformed_row["BILLING_RATE_TYPE"] = "Value"
            transformed_row["IS_OVERTIME_ELIGIBLE"] = "No"
            transformed_row["COMPONENT_DESCRIPTION"] = transformed_row["LABEL"]
            transformed_row["BILLING_FREQUENCY"] = ""
            transformed_row["PAY_SCHEDULE_NAME"] = ""
            transformed_row["CURRENCY"] = schema_currency
            transformed_row["SCHEMA_DESCRIPTION"] = schema_description
            transformed_row["IS_PART_OF_CTC"] = "No"
        transformed_rows.append(transformed_row)
        customer_schema_map[component_name] = transformed_row

    for _, row in pay_supplements_data.iterrows():
        contract_id = str(row["contract_id"])
        if contract_id not in contract_id_to_entity_id_map:
            continue
        if str(contract_id_to_entity_id_map[contract_id]) != entity:
            continue
        component_name = extract_component_name_for_pay_supplement(row)
        if is_nan_or_empty(component_name):
            continue
        if component_name in customer_schema_map:
            continue
        transformed_row = {
            "DO NOT MODIFY OR DELETE THIS ROW": str(""),
            "SCHEMA_NAME": str(get_compensation_schema_name(country_code)),
            "COMPONENT_NAME": str(str(component_name)),
            "CATEGORY_KEY": str("PAY_SUPPLEMENT"),
            "IS_TAXABLE": str("Yes"),
            "IS_FIXED": str("No"),
            "IS_PRORATED": str("No"),
            "IS_MANDATORY": str("No"),
            "IS_PART_OF_BASE_PAY": str("No"),
            "COUNTRY_CODE": str(country_code),
            "TAGS": str(tags),
            "LABEL": str(component_name)  # Use component name as label if no specific label is available
        }
        if new_schema_format_enabled:
            transformed_row["ITEM_TYPE"] =  "INPUT"
            transformed_row["VALIDATION"] = ""
            transformed_row["CALCULATION"] = ""
            transformed_row["BILLING_RATE_TYPE"] = "Value"
            transformed_row["IS_OVERTIME_ELIGIBLE"] = "No"
            transformed_row["COMPONENT_DESCRIPTION"] = transformed_row["LABEL"]
            transformed_row["BILLING_FREQUENCY"] = ""
            transformed_row["PAY_SCHEDULE_NAME"] = ""
            transformed_row["CURRENCY"] = schema_currency
            transformed_row["SCHEMA_DESCRIPTION"] = schema_description
            transformed_row["IS_PART_OF_CTC"] = "No"
        transformed_rows.append(transformed_row)
        customer_schema_map[component_name] = transformed_row

    transformed_df = pd.DataFrame(transformed_rows)

    if new_schema_format_enabled:
        metadata_rows = [
            {"DO NOT MODIFY OR DELETE THIS ROW": "Field", "SCHEMA_NAME": "Schema Name", "COMPONENT_NAME": "Component Name", "CATEGORY_KEY":"Category Key", "IS_TAXABLE":"Is Taxable?", "IS_FIXED":"Is Fixed?", "IS_PRORATED":"Is Prorated?", "IS_MANDATORY":"Is Mandatory?", "IS_PART_OF_BASE_PAY":"Is Part of BasePay?", "COUNTRY_CODE": "Country", "TAGS": "Tags", "LABEL": "Label",
             "ITEM_TYPE": "Item Type", "VALIDATION": "Validation", "CALCULATION": "Calculation", "BILLING_RATE_TYPE": "Billing Rate Type", "IS_OVERTIME_ELIGIBLE": "Is Overtime Eligible?", "COMPONENT_DESCRIPTION": "Component Description", "BILLING_FREQUENCY": "Billing Frequency", "PAY_SCHEDULE_NAME": "Pay Schedule Name", "CURRENCY": "Currency", "SCHEMA_DESCRIPTION": "Schema Description", "IS_PART_OF_CTC": "Is Part of CTC?"},
            {"DO NOT MODIFY OR DELETE THIS ROW": "Field", "SCHEMA_NAME": "Mandatory - Name of schema", "COMPONENT_NAME": "Mandatory - Name of component", "CATEGORY_KEY":"Mandatory - Category of component. Select from a predefined list of categories.", "IS_TAXABLE":"Mandatory - Component is taxable or not.", "IS_FIXED":"Mandatory - Select Yes for fixed and No for variable.", "IS_PRORATED":"Mandatory - Indicates if the compensation component is prorated based on working days/hours.", "IS_MANDATORY":"Mandatory - Mandatory components from compliance standpoint.", "IS_PART_OF_BASE_PAY":"Mandatory - Indicates if the compensation component is part of base pay or not.", "COUNTRY_CODE": "Mandatory - Country code of the component.", "TAGS": "Mandatory - Indicates compensation schema tags to enable filtering on schemas.", "LABEL": "Mandatory - Label for the component.",
             "ITEM_TYPE": "Type of the item.", "VALIDATION": "Input validation for the component.", "CALCULATION": "Calculation for the component.", "BILLING_RATE_TYPE": "Billing rate type.", "IS_OVERTIME_ELIGIBLE": "Indicates if the compensation component is overtime eligible or not.", "COMPONENT_DESCRIPTION": "Description for the component.", "BILLING_FREQUENCY": "Billing Frequency for compensation component.", "PAY_SCHEDULE_NAME": "Pay schedule name for the component.", "CURRENCY": "Currency for the component.", "SCHEMA_DESCRIPTION": "Description for the schema.", "IS_PART_OF_CTC": "Indicates if the compensation component is part of CTC or not."},
        ]
    else:
        metadata_rows = [
            {"DO NOT MODIFY OR DELETE THIS ROW": "Field", "SCHEMA_NAME": "Schema Name", "COMPONENT_NAME": "Component Name", "CATEGORY_KEY":"Category Key", "IS_TAXABLE":"Is Taxable?", "IS_FIXED":"Is Fixed?", "IS_PRORATED":"Is Prorated?", "IS_MANDATORY":"Is Mandatory?", "IS_PART_OF_BASE_PAY":"Is Part of BasePay?", "COUNTRY_CODE": "Country", "TAGS": "Tags", "LABEL": "Label"},
            {"DO NOT MODIFY OR DELETE THIS ROW": "Field", "SCHEMA_NAME": "Mandatory - Name of schema", "COMPONENT_NAME": "Mandatory - Name of component", "CATEGORY_KEY":"Mandatory - Category of component. Select from a predefined list of categories.", "IS_TAXABLE":"Mandatory - Component is taxable or not.", "IS_FIXED":"Mandatory - Select Yes for fixed and No for variable.", "IS_PRORATED":"Mandatory - Indicates if the compensation component is prorated based on working days/hours.", "IS_MANDATORY":"Mandatory - Mandatory components from compliance standpoint.", "IS_PART_OF_BASE_PAY":"Mandatory - Indicates if the compensation component is part of base pay or not.", "COUNTRY_CODE": "Mandatory - Country code of the component.", "TAGS": "Mandatory - Indicates compensation schema tags to enable filtering on schemas.", "LABEL": "Mandatory - Label for the component."}
        ]

    # Convert metadata rows to DataFrame
    metadata_df = pd.DataFrame(metadata_rows)

    # Concatenate metadata and transformed data
    final_df = pd.concat([metadata_df, transformed_df], ignore_index=True)

    return final_df

# ===================================================================================================
# FORMATTING FUNCTIONS
# ===================================================================================================

def format_frequency_to_label(frequency):
    """
    Converts a frequency string from uppercase with underscores to camel case.
    Example: SEMI_ANNUALLY becomes "Semi Annually"

    Args:
        frequency: The frequency string to format

    Returns:
        str: The formatted frequency label
    """
    if not frequency:
        return ""

    # Replace underscores with spaces
    words = frequency.replace('_', ' ').lower().split()

    # Capitalize first letter of each word
    formatted_words = [word.capitalize() for word in words]

    # Join words with spaces
    return ' '.join(formatted_words)

def transform_data_for_pay_schedule(pay_schedule_data_dictionary, country_code, country_to_payroll_dates_map):
    """Transforms the data from the old schema to the new schema."""
    transformed_rows = []

    transformed_row_monthly_default_schedule = {
        "DO NOT MODIFY OR DELETE THIS ROW": str(""),
        "PAY_SCHEDULE_NAME": str("Monthly_Default"),
        "PAY_SCHEDULE_FREQUENCY": str("MONTHLY"),
        "REFERENCE_PERIOD_START_DATE": str("2025-01-01"),
        "REFERENCE_PERIOD_END_DATE": str("2025-01-31"),
        "PAY_DATE_RELATIVE_DAYS": str("0"),
        "PAY_DATE_REFERENCE_TYPE": str("PAY_SCHEDULE_END_DATE"),
        "IS_INSTALLMENT": str("No"),
        "COUNTRY_CODE": str(country_code),
        "LABEL": str(format_frequency_to_label("MONTHLY"))
    }
    transformed_row_monthly_installment_schedule = {
        "DO NOT MODIFY OR DELETE THIS ROW": str(""),
        "PAY_SCHEDULE_NAME": str("Monthly_Installment"),
        "PAY_SCHEDULE_FREQUENCY": str("MONTHLY"),
        "REFERENCE_PERIOD_START_DATE": str("2025-01-01"),
        "REFERENCE_PERIOD_END_DATE": str("2025-01-31"),
        "PAY_DATE_RELATIVE_DAYS": str("0"),
        "PAY_DATE_REFERENCE_TYPE": str("COMPENSATION_START_DATE"),
        "IS_INSTALLMENT": str("Yes"),
        "COUNTRY_CODE": str(country_code),
        "LABEL": str(format_frequency_to_label("MONTHLY"))
    }
    pay_schedule_data_dictionary["MONTHLY"] = "Monthly_Default"
    transformed_rows.append(transformed_row_monthly_default_schedule)
    transformed_rows.append(transformed_row_monthly_installment_schedule)

    transformed_row_one_time_installment_schedule = {
        "DO NOT MODIFY OR DELETE THIS ROW": str(""),
        "PAY_SCHEDULE_NAME": str("One_time_Installment"),
        "PAY_SCHEDULE_FREQUENCY": str("ONE_TIME"),
        "REFERENCE_PERIOD_START_DATE": str("2025-01-31"),
        "REFERENCE_PERIOD_END_DATE": str("2025-01-31"),
        "PAY_DATE_RELATIVE_DAYS": str("0"),
        "PAY_DATE_REFERENCE_TYPE": str("COMPENSATION_START_DATE"),
        "IS_INSTALLMENT": str("Yes"),
        "COUNTRY_CODE": str(country_code),
        "LABEL": str(format_frequency_to_label("ONE_TIME"))
    }
    pay_schedule_data_dictionary["ONE_TIME"] = "One_time_Installment"
    transformed_rows.append(transformed_row_one_time_installment_schedule)

    transformed_row_annual_default_schedule = {
        "DO NOT MODIFY OR DELETE THIS ROW": str(""),
        "PAY_SCHEDULE_NAME": str("Annually_Default"),
        "PAY_SCHEDULE_FREQUENCY": str("ANNUALLY"),
        "REFERENCE_PERIOD_START_DATE": str("2025-01-01"),
        "REFERENCE_PERIOD_END_DATE": str("2025-12-31"),
        "PAY_DATE_RELATIVE_DAYS": str("0"),
        "PAY_DATE_REFERENCE_TYPE": str("COMPENSATION_START_DATE"),
        "IS_INSTALLMENT": str("No"),
        "COUNTRY_CODE": str(country_code),
        "LABEL": str(format_frequency_to_label("ANNUALLY"))
    }
    transformed_row_annual_installment_schedule = {
        "DO NOT MODIFY OR DELETE THIS ROW": str(""),
        "PAY_SCHEDULE_NAME": str("Annually_Installment"),
        "PAY_SCHEDULE_FREQUENCY": str("ANNUALLY"),
        "REFERENCE_PERIOD_START_DATE": str("2025-01-01"),
        "REFERENCE_PERIOD_END_DATE": str("2025-12-31"),
        "PAY_DATE_RELATIVE_DAYS": str("0"),
        "PAY_DATE_REFERENCE_TYPE": str("COMPENSATION_START_DATE"),
        "IS_INSTALLMENT": str("Yes"),
        "COUNTRY_CODE": str(country_code),
        "LABEL": str(format_frequency_to_label("ANNUALLY"))
    }
    pay_schedule_data_dictionary["ANNUALLY"] = "Annually_Default"
    transformed_rows.append(transformed_row_annual_default_schedule)
    transformed_rows.append(transformed_row_annual_installment_schedule)

    transformed_row_semi_annual_default_schedule = {
        "DO NOT MODIFY OR DELETE THIS ROW": str(""),
        "PAY_SCHEDULE_NAME": str("Semi-Annually_Default"),
        "PAY_SCHEDULE_FREQUENCY": str("SEMI_ANNUALLY"),
        "REFERENCE_PERIOD_START_DATE": str("2025-01-01"),
        "REFERENCE_PERIOD_END_DATE": str("2025-06-30"),
        "PAY_DATE_RELATIVE_DAYS": str("0"),
        "PAY_DATE_REFERENCE_TYPE": str("COMPENSATION_START_DATE"),
        "IS_INSTALLMENT": str("No"),
        "COUNTRY_CODE": str(country_code),
        "LABEL": str(format_frequency_to_label("SEMI_ANNUALLY"))
    }
    transformed_row_semi_annual_installment_schedule = {
        "DO NOT MODIFY OR DELETE THIS ROW": str(""),
        "PAY_SCHEDULE_NAME": str("Semi-Annually_Installment"),
        "PAY_SCHEDULE_FREQUENCY": str("SEMI_ANNUALLY"),
        "REFERENCE_PERIOD_START_DATE": str("2025-01-01"),
        "REFERENCE_PERIOD_END_DATE": str("2025-06-30"),
        "PAY_DATE_RELATIVE_DAYS": str("0"),
        "PAY_DATE_REFERENCE_TYPE": str("COMPENSATION_START_DATE"),
        "IS_INSTALLMENT": str("Yes"),
        "COUNTRY_CODE": str(country_code),
        "LABEL": str(format_frequency_to_label("SEMI_ANNUALLY"))
    }
    pay_schedule_data_dictionary["SEMI_ANNUALLY"] = "Semi-Annually_Default"
    transformed_rows.append(transformed_row_semi_annual_default_schedule)
    transformed_rows.append(transformed_row_semi_annual_installment_schedule)

    transformed_row_quarterly_default_schedule = {
        "DO NOT MODIFY OR DELETE THIS ROW": str(""),
        "PAY_SCHEDULE_NAME": str("Quarterly_Default"),
        "PAY_SCHEDULE_FREQUENCY": str("QUARTERLY"),
        "REFERENCE_PERIOD_START_DATE": str("2025-01-01"),
        "REFERENCE_PERIOD_END_DATE": str("2025-03-31"),
        "PAY_DATE_RELATIVE_DAYS": str("0"),
        "PAY_DATE_REFERENCE_TYPE": str("COMPENSATION_START_DATE"),
        "IS_INSTALLMENT": str("No"),
        "COUNTRY_CODE": str(country_code),
        "LABEL": str(format_frequency_to_label("QUARTERLY"))
    }
    transformed_row_quarterly_installment_schedule = {
        "DO NOT MODIFY OR DELETE THIS ROW": str(""),
        "PAY_SCHEDULE_NAME": str("Quarterly_Installment"),
        "PAY_SCHEDULE_FREQUENCY": str("QUARTERLY"),
        "REFERENCE_PERIOD_START_DATE": str("2025-01-01"),
        "REFERENCE_PERIOD_END_DATE": str("2025-03-31"),
        "PAY_DATE_RELATIVE_DAYS": str("0"),
        "PAY_DATE_REFERENCE_TYPE": str("COMPENSATION_START_DATE"),
        "IS_INSTALLMENT": str("Yes"),
        "COUNTRY_CODE": str(country_code),
        "LABEL": str(format_frequency_to_label("QUARTERLY"))
    }
    pay_schedule_data_dictionary["QUARTERLY"] = "Quarterly_Default"
    pay_schedule_data_dictionary["QUATERLY"] = "Quarterly_Default"
    transformed_rows.append(transformed_row_quarterly_default_schedule)
    transformed_rows.append(transformed_row_quarterly_installment_schedule)

    if country_code in country_to_payroll_dates_map:
        payroll_dates_config_array = country_to_payroll_dates_map[country_code]
        for payroll_dates_config in payroll_dates_config_array:
            frequency = payroll_dates_config["frequency"]
            start_date = payroll_dates_config["start_date"]
            end_date = payroll_dates_config["end_date"]
            if frequency == 'SEMIMONTHLY':
                transformed_row_semi_monthly_default_schedule = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "PAY_SCHEDULE_NAME": str("SemiMonthly_Default"),
                    "PAY_SCHEDULE_FREQUENCY": str("SEMI_MONTHLY"),
                    "REFERENCE_PERIOD_START_DATE": str("2025-01-01"),
                    "REFERENCE_PERIOD_END_DATE": str("2025-01-15"),
                    "PAY_DATE_RELATIVE_DAYS": str("0"),
                    "PAY_DATE_REFERENCE_TYPE": str("PAY_SCHEDULE_END_DATE"),
                    "IS_INSTALLMENT": str("No"),
                    "COUNTRY_CODE": str(country_code),
                    "LABEL": str(format_frequency_to_label("SEMI_MONTHLY"))
                }
                transformed_row_semi_monthly_installment_schedule = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "PAY_SCHEDULE_NAME": str("SemiMonthly_Installment"),
                    "PAY_SCHEDULE_FREQUENCY": str("SEMI_MONTHLY"),
                    "REFERENCE_PERIOD_START_DATE": str("2025-01-01"),
                    "REFERENCE_PERIOD_END_DATE": str("2025-01-15"),
                    "PAY_DATE_RELATIVE_DAYS": str("0"),
                    "PAY_DATE_REFERENCE_TYPE": str("COMPENSATION_START_DATE"),
                    "IS_INSTALLMENT": str("Yes"),
                    "COUNTRY_CODE": str(country_code),
                    "LABEL": str(format_frequency_to_label("SEMI_MONTHLY"))
                }
                pay_schedule_data_dictionary["SEMI_MONTHLY"] = "SemiMonthly_Default"
                pay_schedule_data_dictionary["SEMIMONTHLY"] = "SemiMonthly_Default"
                transformed_rows.append(transformed_row_semi_monthly_default_schedule)
                transformed_rows.append(transformed_row_semi_monthly_installment_schedule)
            elif frequency == 'BIWEEKLY':
                transformed_row_bi_weekly_default_schedule = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "PAY_SCHEDULE_NAME": str("BiWeekly_Default"),
                    "PAY_SCHEDULE_FREQUENCY": str("BI_WEEKLY"),
                    "REFERENCE_PERIOD_START_DATE": start_date,
                    "REFERENCE_PERIOD_END_DATE": end_date,
                    "PAY_DATE_RELATIVE_DAYS": str("0"),
                    "PAY_DATE_REFERENCE_TYPE": str("PAY_SCHEDULE_END_DATE"),
                    "IS_INSTALLMENT": str("No"),
                    "COUNTRY_CODE": str(country_code),
                    "LABEL": str(format_frequency_to_label("BIWEEKLY"))
                }
                transformed_row_bi_weekly_installment_schedule = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "PAY_SCHEDULE_NAME": str("BiWeekly_Installment"),
                    "PAY_SCHEDULE_FREQUENCY": str("BI_WEEKLY"),
                    "REFERENCE_PERIOD_START_DATE": start_date,
                    "REFERENCE_PERIOD_END_DATE": end_date,
                    "PAY_DATE_RELATIVE_DAYS": str("0"),
                    "PAY_DATE_REFERENCE_TYPE": str("COMPENSATION_START_DATE"),
                    "IS_INSTALLMENT": str("Yes"),
                    "COUNTRY_CODE": str(country_code),
                    "LABEL": str(format_frequency_to_label("BIWEEKLY"))
                }
                pay_schedule_data_dictionary["BIWEEKLY"] = "BiWeekly_Default"
                pay_schedule_data_dictionary["BI_WEEKLY"] = "BiWeekly_Default"
                transformed_rows.append(transformed_row_bi_weekly_default_schedule)
                transformed_rows.append(transformed_row_bi_weekly_installment_schedule)

    transformed_df = pd.DataFrame(transformed_rows)

    # Define metadata rows
    metadata_rows = [
        {"DO NOT MODIFY OR DELETE THIS ROW": "Field", "PAY_SCHEDULE_NAME": "Pay Schedule Name", "PAY_SCHEDULE_FREQUENCY":"Pay Schedule Frequency", "REFERENCE_PERIOD_START_DATE":"Reference Period Start Date", "REFERENCE_PERIOD_END_DATE":"Reference Period End Date", "PAY_DATE_RELATIVE_DAYS":"Pay Date Relative Days", "PAY_DATE_REFERENCE_TYPE":"Pay Date Reference Type", "IS_INSTALLMENT":"Is Installment", "COUNTRY_CODE": "Country", "LABEL": "Label"},
        {"DO NOT MODIFY OR DELETE THIS ROW": "Field Description", "PAY_SCHEDULE_NAME": "Mandatory - Name / Reference of Pay Schedule", "PAY_SCHEDULE_FREQUENCY":"Mandatory - Frequency of the pay schedule.", "REFERENCE_PERIOD_START_DATE":"Mandatory - Start Date of one instance.", "REFERENCE_PERIOD_END_DATE":"Mandatory - End Date of that instance", "PAY_DATE_RELATIVE_DAYS":"Mandatory - Pay Date relative days wrt Pay Date Reference Type", "PAY_DATE_REFERENCE_TYPE":"Mandatory - Reference date for calculating pay date", "IS_INSTALLMENT":"Is Installment", "COUNTRY_CODE": "Mandatory - Country code of the component.", "LABEL": "Mandatory - Label for the pay schedule."}
    ]

    # Convert metadata rows to DataFrame
    metadata_df = pd.DataFrame(metadata_rows)

    # Concatenate metadata and transformed data
    final_df = pd.concat([metadata_df, transformed_df], ignore_index=True)

    return final_df

# Note - Any changes in below method logic (related to pay supplements), should be reflected in the method build_pay_supplement_mapping_backfill_rows().
def transform_data_for_compensation_backfill(compensation_data, pay_schedule_data_dictionary, pay_supplements_data, compensation_id_to_salary_review_map, entity, contract_id_to_entity_id_map, contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map, contract_id_to_ended_on_map, contract_id_to_status_map, contract_id_to_start_on_map, country_code=None, contract_id_to_country_map=None, contract_id_to_currency_map=None, contract_id_to_contributions_map=None):
    """Transforms the data from the old schema to the new schema and resolves duplicates."""
    transformed_rows = []

    previous_contract_processed = None
    previous_start_date = None
    previous_comp_name = None
    # Process compensations data.
    for _, row in compensation_data.iterrows():
        contract_id = str(row["contract_id"])
        if contract_id not in contract_id_to_entity_id_map:
            continue
        if str(contract_id_to_entity_id_map[contract_id]) != entity:
            continue
        if should_exclude_onboarding_contract_before_2025(contract_id, contract_id_to_status_map, contract_id_to_start_on_map):
            continue
        # Filter by country if country_code and contract_id_to_country_map are provided
        if country_code and contract_id_to_country_map and contract_id in contract_id_to_country_map:
            if contract_id_to_country_map[contract_id] != country_code:
                continue
        if get_is_onboarding_contract(contract_id, contract_id_to_status_map):
            continue

        if row["status"] != "ACTIVE" and row["status"] != "COMPLETED":
            continue
        if get_is_variable_contract_allowance_to_ignore(row):
            continue
        component_name = extract_component_name_for_compensation(row)
        if component_name in component_names_to_ignore:
            log_exception(f"Skipping row for contract {contract_id} as component name {component_name} is in component_names_to_ignore", contract_id=contract_id, metadata=row)
            continue
        transformed_row = {}
        if row["status"] == "ACTIVE":
            if extract_billing_frequency(row) == 'ONETIME':
                billing_rate_resolved = row.get("amount", 0)
                if is_recurring_variable_compensation(row) and billing_rate_resolved == 0:
                    billing_rate_resolved = ''
                curr_row_start_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                transformed_row = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "CONTRACT_ID": str(row["contract_id"]),
                    "EMPLOYEE_FULL_NAME": str(""),
                    "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                    "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                    "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                    "BILLING_RATE": str(billing_rate_resolved),
                    "BILLING_FREQUENCY": str("ONETIME"),
                    "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_pay_supplement(row, pay_schedule_data_dictionary)),
                    "IS_INSTALLMENT": str("Yes"),
                    "START_DATE": str(curr_row_start_date),
                    "END_DATE": str(""),
                    "NUMBER_OF_INSTALLMENTS": str("1"),
                    "PROCESSED_UNTIL_DATE": str(extract_processed_until_date(row, "PAY_SUPPLEMENT", {}, {}, start_date=curr_row_start_date, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map,pay_schedule_data_dictionary=pay_schedule_data_dictionary)),
                    "NOTES": extract_notes(row, "COMPENSATION")
                }
                previous_start_date = transformed_row["START_DATE"]
                previous_comp_name = transformed_row["COMPONENT_NAME"]
                previous_contract_processed = transformed_row["CONTRACT_ID"]
            else:
                billing_rate_resolved = row.get("amount", 0)
                if is_recurring_variable_compensation(row) and billing_rate_resolved == 0:
                    billing_rate_resolved = ''
                curr_row_start_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                curr_row_end_date = extract_end_date(row, "COMPENSATION", contract_id_to_ended_on_map, contract_id_to_status_map)
                # Handle edge case for ended contracts.
                if curr_row_end_date < curr_row_start_date and curr_row_end_date != "":
                    log_exception(f"Skipping row for contract {contract_id} as end date {curr_row_end_date} is before start date {curr_row_start_date}", contract_id=contract_id, metadata=row)
                    continue
                transformed_row = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "CONTRACT_ID": str(row["contract_id"]),
                    "EMPLOYEE_FULL_NAME": str(""),
                    "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                    "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                    "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                    "BILLING_RATE": str(billing_rate_resolved),
                    "BILLING_FREQUENCY": str(extract_billing_frequency(row)),
                    "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_compensation(row, pay_schedule_data_dictionary, contract_id_to_previous_payroll_config_map)),
                    "IS_INSTALLMENT": str("No"),
                    "START_DATE": str(curr_row_start_date),
                    "END_DATE": str(curr_row_end_date),
                    "NUMBER_OF_INSTALLMENTS": str(""),
                    "PROCESSED_UNTIL_DATE": str(extract_processed_until_date(row, "COMPENSATION", contract_id_to_status_map, contract_id_to_ended_on_map, start_date=curr_row_start_date, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map,pay_schedule_data_dictionary=pay_schedule_data_dictionary)),
                    "NOTES": extract_notes(row, "COMPENSATION")
                }
                previous_start_date = transformed_row["START_DATE"]
                previous_comp_name = transformed_row["COMPONENT_NAME"]
                previous_contract_processed = transformed_row["CONTRACT_ID"]
        elif row["status"] == "COMPLETED":
            if extract_billing_frequency(row) == 'ONETIME':
                billing_rate_resolved = row.get("amount", 0)
                if is_recurring_variable_compensation(row):
                    billing_rate_resolved = ''
                curr_comp_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                transformed_row = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "CONTRACT_ID": str(row["contract_id"]),
                    "EMPLOYEE_FULL_NAME": str(""),
                    "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                    "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                    "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                    "BILLING_RATE": str(row.get("amount", 0)),
                    "BILLING_FREQUENCY": str("ONETIME"),
                    "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_pay_supplement(row, pay_schedule_data_dictionary)),
                    "IS_INSTALLMENT": str("Yes"),
                    "START_DATE": str(curr_comp_date),
                    "END_DATE": str(""),
                    "NUMBER_OF_INSTALLMENTS": str("1"),
                    "PROCESSED_UNTIL_DATE": str(extract_processed_until_date(row, "PAY_SUPPLEMENT", {}, {}, start_date=curr_comp_date, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map,pay_schedule_data_dictionary=pay_schedule_data_dictionary)),
                    "NOTES": extract_notes(row, "COMPENSATION")
                }
                previous_start_date = transformed_row["START_DATE"]
                previous_comp_name = transformed_row["COMPONENT_NAME"]
                previous_contract_processed = transformed_row["CONTRACT_ID"]
            else:
                curr_start_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                curr_end_date = extract_end_date_for_old_comp(row, "COMPENSATION")
                if str(previous_comp_name) == str(extract_component_name_for_compensation(row)) and str(previous_contract_processed) == str(row["contract_id"]):
                    curr_end_date_as_date = pd.to_datetime(previous_start_date, format='%Y-%m-%d', errors='coerce') - pd.Timedelta(days=1)
                    curr_end_date = curr_end_date_as_date.strftime('%Y-%m-%d')
                # Handle edge case for duplicated data.
                if curr_end_date < curr_start_date and curr_end_date != "":
                    log_exception(f"Skipping row for contract {contract_id} as end date {curr_end_date} is before start date {curr_start_date}", contract_id=contract_id, metadata=row)
                    continue
                billing_rate_resolved = row.get("amount", 0)
                curr_processed_until_date = curr_end_date
                if is_recurring_variable_compensation(row) and billing_rate_resolved == 0:
                    billing_rate_resolved = ''
                    curr_processed_until_date = extract_processed_until_date(row, "COMPENSATION", contract_id_to_status_map, contract_id_to_ended_on_map, start_date=curr_start_date, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map,pay_schedule_data_dictionary=pay_schedule_data_dictionary)

                transformed_row = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "CONTRACT_ID": str(row["contract_id"]),
                    "EMPLOYEE_FULL_NAME": str(""),
                    "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                    "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                    "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                    "BILLING_RATE": str(billing_rate_resolved),
                    "BILLING_FREQUENCY": str(extract_billing_frequency(row)),
                    "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_compensation(row, pay_schedule_data_dictionary, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map)),
                    "IS_INSTALLMENT": str("No"),
                    "START_DATE": str(extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)),
                    "END_DATE": str(curr_end_date),
                    "NUMBER_OF_INSTALLMENTS": str(""),
                    "PROCESSED_UNTIL_DATE": str(curr_processed_until_date),
                    "NOTES": extract_notes(row, "COMPENSATION"),
                }
                previous_start_date = transformed_row["START_DATE"]
                previous_comp_name = transformed_row["COMPONENT_NAME"]
                previous_contract_processed = transformed_row["CONTRACT_ID"]
        if transformed_row["BILLING_RATE"] == 0 or transformed_row["BILLING_RATE"] == '0':
            # transformed_row["BILLING_RATE"] = ""
            continue
        transformed_rows.append(transformed_row)

    # Process pay supplements data.
    for _, row in pay_supplements_data.iterrows():
        contract_id = str(row["contract_id"])
        if contract_id not in contract_id_to_entity_id_map:
            continue
        if str(contract_id_to_entity_id_map[contract_id]) != entity:
            continue
        if get_is_onboarding_contract(contract_id, contract_id_to_status_map):
            continue
        if should_exclude_onboarding_contract_before_2025(contract_id, contract_id_to_status_map, contract_id_to_start_on_map):
            continue
        # Filter by country if country_code and contract_id_to_country_map are provided
        if country_code and contract_id_to_country_map and contract_id in contract_id_to_country_map:
            if contract_id_to_country_map[contract_id] != country_code:
                continue
        if exclude_recurring_variable_or_one_time_pay_supplement_already_considered_from_compensation_table(row):
            continue
        if row["status"] == "REVOKED":
            continue
        component_name = extract_component_name_for_pay_supplement(row)
        if component_name in component_names_to_ignore:
            log_exception(f"Skipping row for contract {contract_id} as component name {component_name} is in component_names_to_ignore", contract_id=contract_id, metadata=row)
            continue
        curr_row_start_date = extract_start_date(row, "PAY_SUPPLEMENT", {}, contract_id_to_start_on_map)
        transformed_row = {
            "DO NOT MODIFY OR DELETE THIS ROW": str(""),
            "CONTRACT_ID": str(row["contract_id"]),
            "EMPLOYEE_FULL_NAME": str(""),
            "COMPONENT_NAME": str(str(extract_component_name_for_pay_supplement(row))),
            "CURRENCY": str(row["currency_code"]),
            "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
            "BILLING_RATE": str(row.get("amount", 0)),
            "BILLING_FREQUENCY": str("ONETIME"),
            "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_pay_supplement(row, pay_schedule_data_dictionary)),
            "IS_INSTALLMENT": str("Yes"),
            "START_DATE": str(curr_row_start_date),
            "END_DATE": str(""),
            "NUMBER_OF_INSTALLMENTS": str("1"),
            "PROCESSED_UNTIL_DATE": str(extract_processed_until_date(row, "PAY_SUPPLEMENT", {}, {}, start_date=curr_row_start_date, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map,pay_schedule_data_dictionary=pay_schedule_data_dictionary)),
            "NOTES": extract_notes(row, "PAY_SUPPLEMENT")
        }
        if transformed_row["BILLING_RATE"] == 0 or transformed_row["BILLING_RATE"] == '0':
            continue
        transformed_rows.append(transformed_row)

    # Add RRSP contributions for Canadian contracts if applicable
    if country_code == "CAN" and contract_id_to_contributions_map is not None and contract_id_to_country_map is not None:
        transformed_rows = append_transformed_rows_with_contributions_for_CAN(
            transformed_rows,
            contract_id_to_contributions_map,
            contract_id_to_country_map
        )

    transformed_df = pd.DataFrame(transformed_rows)

    # Define metadata rows
    metadata_rows = [
        {"DO NOT MODIFY OR DELETE THIS ROW": "Field", "CONTRACT_ID": "Contract ID", "EMPLOYEE_FULL_NAME":"Employee Full Name", "COMPONENT_NAME":"Component Name", "CURRENCY":"Currency", "BILLING_RATE_TYPE":"Billing Rate Type", "BILLING_RATE":"Billing Rate", "BILLING_FREQUENCY":"Billing Frequency", "PAY_SCHEDULE_NAME":"Pay Schedule Name", "IS_INSTALLMENT":"Is Installment?", "START_DATE":"Start Date", "END_DATE":"End Date", "NUMBER_OF_INSTALLMENTS":"Number of installments", "PROCESSED_UNTIL_DATE":"Processed Until Date", "NOTES":"Notes"},
        {"DO NOT MODIFY OR DELETE THIS ROW": "Field Description", "CONTRACT_ID": "Mandatory - Unique identifier for each employee", "EMPLOYEE_FULL_NAME": "Optional - Employee Full Name", "COMPONENT_NAME": "Mandatory - The name of the component.", "CURRENCY": "Mandatory - The currency of the entity.", "BILLING_RATE_TYPE": "Mandatory - Billing rate type.", "BILLING_RATE": "Mandatory - Billing Rate for compensation component.", "BILLING_FREQUENCY": "Mandatory - Billing Frequency for compensation component.", "PAY_SCHEDULE_NAME": "Mandatory - Payment schedule name.", "IS_INSTALLMENT": "Mandatory - Flag indicating whether component is an installment.", "START_DATE": "Mandatory - Start date of the component. For installment, it would be first installment start date", "END_DATE": "Optional - End date of the component. Leave it blank for indefinite end date.", "NUMBER_OF_INSTALLMENTS": "Optional - Number of installments to be paid.", "PROCESSED_UNTIL_DATE": "Optional - Last processed date of the component. Leave it blank create compensation items for all.", "NOTES":"Notes"}
    ]

    # Convert metadata rows to DataFrame
    metadata_df = pd.DataFrame(metadata_rows)

    # Concatenate metadata and transformed data
    final_df = pd.concat([metadata_df, transformed_df], ignore_index=True)

    return final_df


def append_transformed_rows_with_contributions_for_CAN(transformed_rows, contract_id_to_contributions_map, contract_id_to_country_map):
    """Append RRSP contribution rows for Canadian contracts with Gross Salary components.

    Args:
        transformed_rows: List of transformed compensation rows
        contract_id_to_contributions_map: Mapping of contract IDs to contribution data
        contract_id_to_country_map: Mapping of contract IDs to country codes

    Returns:
        List of transformed rows with additional RRSP contribution rows for CAN contracts
    """
    additional_rows = []

    for row in transformed_rows:
        contract_id = str(row.get("CONTRACT_ID", ""))
        component_name = str(row.get("COMPONENT_NAME", ""))
        end_date = str(row.get("END_DATE", ""))

        # Check if this is a Canadian contract
        if contract_id in contract_id_to_country_map and contract_id_to_country_map[contract_id] == "CAN":
            # Check if component name is 'Gross Salary'
            if component_name == "Gross Salary":
                # Check if end date is empty/null/nan or is after 1st Jan 2025
                should_add_contributions = False

                if is_nan_or_empty(end_date) or end_date == "" or end_date == "nan":
                    should_add_contributions = True
                else:
                    try:
                        end_date_parsed = pd.to_datetime(end_date, format='%Y-%m-%d', errors='coerce')
                        cutoff_date = pd.to_datetime("2025-01-01", format='%Y-%m-%d', errors='coerce')
                        if not pd.isna(end_date_parsed) and end_date_parsed > cutoff_date:
                            should_add_contributions = True
                    except Exception as e:
                        log_exception(f"Error parsing end date {end_date} for contract {contract_id}: {e}", contract_id)
                        should_add_contributions = True  # Default to adding contributions if date parsing fails

                if should_add_contributions:
                    # Get contribution values for this contract, default to 0 if not found
                    if contract_id in contract_id_to_contributions_map:
                        contributions = contract_id_to_contributions_map[contract_id]
                    else:
                        # For contracts not present in the sheet but with country CAN, use 0 contributions
                        contributions = {
                            'EE contribution': 0,
                            'ER contribution': 0
                        }
                        log_exception(f"Contract {contract_id} not found in contributions file, using default 0% contributions", contract_id=contract_id, level='INFO')

                    ee_contribution = contributions.get('EE contribution', 0)
                    er_contribution = contributions.get('ER contribution', 0)

                    # Create Employer RRSP contribution row
                    employer_rrsp_row = row.copy()
                    employer_rrsp_row["COMPONENT_NAME"] = "employer_rrsp_contribution"
                    employer_rrsp_row["BILLING_RATE_TYPE"] = "% of Base Pay"
                    employer_rrsp_row["BILLING_RATE"] = "" if er_contribution == 0 or er_contribution == 0.0 else str(er_contribution)
                    additional_rows.append(employer_rrsp_row)

                    # Create Employee RRSP contribution row
                    employee_rrsp_row = row.copy()
                    employee_rrsp_row["COMPONENT_NAME"] = "employee_rrsp_contribution"
                    employee_rrsp_row["BILLING_RATE_TYPE"] = "% of Base Pay"
                    employee_rrsp_row["BILLING_RATE"] = "" if ee_contribution == 0 or ee_contribution == 0.0 else str(ee_contribution)
                    additional_rows.append(employee_rrsp_row)

    print(f"Added {len(additional_rows)} RRSP contribution rows for Canadian contracts")
    return transformed_rows + additional_rows


# Note - Any changes in below method logic, should be reflected in the method build_revision_mapping_backfill_rows().
def transform_data_for_revision_backfill(compensation_data, pay_schedule_data_dictionary, salary_reviews_data, compensation_id_to_salary_review_map, entity, contract_id_to_entity_id_map, contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map, contract_id_to_ended_on_map, contract_id_to_status_map, contract_id_to_start_on_map, country_code=None, contract_id_to_country_map=None, contract_id_to_currency_map=None, contract_id_to_contributions_map=None):
    """Transforms the data from the old schema to the new schema."""
    transformed_rows = []

    compensation_id_to_compensation_map = {}

    previous_contract_processed = None
    previous_start_date = None
    previous_comp_name = None
    # Process compensations data.
    for _, row in compensation_data.iterrows():
        contract_id = str(row["contract_id"])
        if contract_id not in contract_id_to_entity_id_map:
            continue
        if str(contract_id_to_entity_id_map[contract_id]) != entity:
            continue
        if should_exclude_onboarding_contract_before_2025(contract_id, contract_id_to_status_map, contract_id_to_start_on_map):
            continue
        # Filter by country if country_code and contract_id_to_country_map are provided
        if country_code and contract_id_to_country_map and contract_id in contract_id_to_country_map:
            if contract_id_to_country_map[contract_id] != country_code:
                continue
        if row["status"] != "ACTIVE" and row["status"] != "COMPLETED":
            continue
        if get_is_variable_contract_allowance_to_ignore(row):
            continue
        compensation_id_to_compensation_map[str(row["id"])] = row.to_dict()
        component_name = extract_component_name_for_compensation(row)
        if component_name in component_names_to_ignore:
            log_exception(f"Skipping row for contract {contract_id} as component name {component_name} is in component_names_to_ignore", contract_id=contract_id, metadata=row)
            continue
        transformed_row = {}
        if row["status"] == "ACTIVE":
            if extract_billing_frequency(row) == 'ONETIME':
                billing_rate_resolved = row.get("amount", 0)
                if is_recurring_variable_compensation(row) and billing_rate_resolved == 0:
                    billing_rate_resolved = ''
                curr_row_start_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                transformed_row = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "CONTRACT_ID": str(row["contract_id"]),
                    "EMPLOYEE_FULL_NAME": str(""),
                    "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                    "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                    "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                    "BILLING_RATE": str(billing_rate_resolved),
                    "BILLING_FREQUENCY": str("ONETIME"),
                    "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_pay_supplement(row, pay_schedule_data_dictionary)),
                    "IS_INSTALLMENT": str("Yes"),
                    "START_DATE": str(curr_row_start_date),
                    "END_DATE": str(""),
                    "NUMBER_OF_INSTALLMENTS": str("1"),
                    "REASON_CODE": str(""),
                    "SOURCE_EFFECTIVE_DATE": str(extract_source_effective_date_for_compensation(row, compensation_id_to_salary_review_map, contract_id_to_previous_payroll_config_map=contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map, contract_id_to_start_on_map=contract_id_to_start_on_map)),
                    "IS_BACKFILL_FOR_REVISION": str(extract_is_backfill_for_revision(row, compensation_id_to_salary_review_map)),
                    "NOTES": extract_notes(row, "COMPENSATION")
                }
                previous_start_date = transformed_row["START_DATE"]
                previous_comp_name = transformed_row["COMPONENT_NAME"]
                previous_contract_processed = transformed_row["CONTRACT_ID"]
            else:
                billing_rate_resolved = row.get("amount", 0)
                if is_recurring_variable_compensation(row) and billing_rate_resolved == 0:
                    billing_rate_resolved = ''
                curr_row_start_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                curr_row_end_date = extract_end_date(row, "COMPENSATION", contract_id_to_ended_on_map, contract_id_to_status_map)
                if curr_row_end_date < curr_row_start_date and curr_row_end_date != "":
                    log_exception(f"Skipping row for contract {contract_id} as end date {curr_row_end_date} is before start date {curr_row_start_date}", contract_id=contract_id, metadata=row)
                    continue
                transformed_row = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "CONTRACT_ID": str(row["contract_id"]),
                    "EMPLOYEE_FULL_NAME": str(""),
                    "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                    "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                    "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                    "BILLING_RATE": str(billing_rate_resolved),
                    "BILLING_FREQUENCY": str(extract_billing_frequency(row)),
                    "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_compensation(row, pay_schedule_data_dictionary, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map)),
                    "IS_INSTALLMENT": str("No"),
                    "START_DATE": str(curr_row_start_date),
                    "END_DATE": str(curr_row_end_date),
                    "NUMBER_OF_INSTALLMENTS": str(""),
                    "REASON_CODE": str(""),
                    "SOURCE_EFFECTIVE_DATE": str(extract_source_effective_date_for_compensation(row, compensation_id_to_salary_review_map, contract_id_to_previous_payroll_config_map=contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map, contract_id_to_start_on_map=contract_id_to_start_on_map)),
                    "IS_BACKFILL_FOR_REVISION": str(extract_is_backfill_for_revision(row, compensation_id_to_salary_review_map)),
                    "NOTES": extract_notes(row, "COMPENSATION")
                }
                previous_start_date = transformed_row["START_DATE"]
                previous_comp_name = transformed_row["COMPONENT_NAME"]
                previous_contract_processed = transformed_row["CONTRACT_ID"]
        elif row["status"] == "COMPLETED":
            if extract_billing_frequency(row) == 'ONETIME':
                billing_rate_resolved = row.get("amount", 0)
                if is_recurring_variable_compensation(row) and billing_rate_resolved == 0:
                    billing_rate_resolved = ''
                curr_comp_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                transformed_row = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "CONTRACT_ID": str(row["contract_id"]),
                    "EMPLOYEE_FULL_NAME": str(""),
                    "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                    "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                    "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                    "BILLING_RATE": str(billing_rate_resolved),
                    "BILLING_FREQUENCY": str("ONETIME"),
                    "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_pay_supplement(row, pay_schedule_data_dictionary)),
                    "IS_INSTALLMENT": str("Yes"),
                    "START_DATE": str(curr_comp_date),
                    "END_DATE": str(""),
                    "NUMBER_OF_INSTALLMENTS": str("1"),
                    "REASON_CODE": str(""),
                    "SOURCE_EFFECTIVE_DATE": str(extract_source_effective_date_for_compensation(row, compensation_id_to_salary_review_map, contract_id_to_previous_payroll_config_map=contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map, contract_id_to_start_on_map=contract_id_to_start_on_map)),
                    "IS_BACKFILL_FOR_REVISION": str(extract_is_backfill_for_revision(row, compensation_id_to_salary_review_map)),
                    "NOTES": extract_notes(row, "COMPENSATION")
                }
                previous_start_date = transformed_row["START_DATE"]
                previous_comp_name = transformed_row["COMPONENT_NAME"]
                previous_contract_processed = transformed_row["CONTRACT_ID"]
            else:
                curr_start_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                curr_end_date = extract_end_date_for_old_comp(row, "COMPENSATION")
                if str(previous_comp_name) == str(extract_component_name_for_compensation(row)) and str(previous_contract_processed) == str(row["contract_id"]):
                    curr_end_date_as_date = pd.to_datetime(previous_start_date, format='%Y-%m-%d', errors='coerce') - pd.Timedelta(days=1)
                    curr_end_date = curr_end_date_as_date.strftime('%Y-%m-%d')
                # Handle edge case for duplicated data.
                if curr_end_date < curr_start_date and curr_end_date != "":
                    log_exception(f"Skipping row for contract {contract_id} as end date {curr_end_date} is before start date {curr_start_date}", contract_id=contract_id, metadata=row)
                    continue
                billing_rate_resolved = row.get("amount", 0)
                if is_recurring_variable_compensation(row) and billing_rate_resolved == 0:
                    billing_rate_resolved = ''
                transformed_row = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "CONTRACT_ID": str(row["contract_id"]),
                    "EMPLOYEE_FULL_NAME": str(""),
                    "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                    "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                    "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                    "BILLING_RATE": str(billing_rate_resolved),
                    "BILLING_FREQUENCY": str(extract_billing_frequency(row)),
                    "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_compensation(row, pay_schedule_data_dictionary, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map)),
                    "IS_INSTALLMENT": str("No"),
                    "START_DATE": str(curr_start_date),
                    "END_DATE": str(curr_end_date),
                    "NUMBER_OF_INSTALLMENTS": str(""),
                    "REASON_CODE": str(""),
                    "SOURCE_EFFECTIVE_DATE": str(extract_source_effective_date_for_compensation(row, compensation_id_to_salary_review_map, contract_id_to_previous_payroll_config_map=contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map, contract_id_to_start_on_map=contract_id_to_start_on_map)),
                    "IS_BACKFILL_FOR_REVISION": str(extract_is_backfill_for_revision(row, compensation_id_to_salary_review_map)),
                    "NOTES": extract_notes(row, "COMPENSATION")
                }
                previous_start_date = transformed_row["START_DATE"]
                previous_comp_name = transformed_row["COMPONENT_NAME"]
                previous_contract_processed = transformed_row["CONTRACT_ID"]
        if transformed_row["BILLING_RATE"] == 0 or transformed_row["BILLING_RATE"] == '0':
            # transformed_row["BILLING_RATE"] = ""
            continue
        transformed_rows.append(transformed_row)

    # Process pay supplements data.
    for _, row in pay_supplements_data.iterrows():
        contract_id = str(row["contract_id"])
        if contract_id not in contract_id_to_entity_id_map:
            continue
        if str(contract_id_to_entity_id_map[contract_id]) != entity:
            continue
        if should_exclude_onboarding_contract_before_2025(contract_id, contract_id_to_status_map, contract_id_to_start_on_map):
            continue
        if exclude_recurring_variable_or_one_time_pay_supplement_already_considered_from_compensation_table(row):
            continue
        # Filter by country if country_code and contract_id_to_country_map are provided
        if country_code and contract_id_to_country_map and contract_id in contract_id_to_country_map:
            if contract_id_to_country_map[contract_id] != country_code:
                continue
        if row["status"] == "REVOKED":
            continue
        component_name = extract_component_name_for_pay_supplement(row)
        if component_name in component_names_to_ignore:
            log_exception(f"Skipping row for contract {contract_id} as component name {component_name} is in component_names_to_ignore", contract_id=contract_id, metadata=row)
            continue
        transformed_row = {
            "DO NOT MODIFY OR DELETE THIS ROW": str(""),
            "CONTRACT_ID": str(row["contract_id"]),
            "EMPLOYEE_FULL_NAME": str(""),
            "COMPONENT_NAME": str(str(extract_component_name_for_pay_supplement(row))),
            "CURRENCY": str(row["currency_code"]),
            "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
            "BILLING_RATE": str(row.get("amount", 0)),
            "BILLING_FREQUENCY": str("ONETIME"),
            "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_pay_supplement(row, pay_schedule_data_dictionary)),
            "IS_INSTALLMENT": str("Yes"),
            "START_DATE": str(extract_start_date(row, "PAY_SUPPLEMENT", {}, contract_id_to_start_on_map)),
            "END_DATE": str(""),
            "NUMBER_OF_INSTALLMENTS": str("1"),
            "REASON_CODE": str(""),
            "SOURCE_EFFECTIVE_DATE": str(extract_source_effective_date_for_pay_supplement(row, compensation_id_to_salary_review_map, contract_id_to_previous_payroll_config_map=contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map, contract_id_to_start_on_map=contract_id_to_start_on_map)),
            "IS_BACKFILL_FOR_REVISION": str("No"),
            "NOTES": extract_notes(row, "PAY_SUPPLEMENT")
        }
        if transformed_row["BILLING_RATE"] == 0 or transformed_row["BILLING_RATE"] == '0':
            continue
        transformed_rows.append(transformed_row)

    # Add RRSP contributions for Canadian contracts if applicable
    if country_code == "CAN" and contract_id_to_contributions_map is not None and contract_id_to_country_map is not None:
        transformed_rows = append_transformed_rows_with_contributions_for_CAN(
            transformed_rows,
            contract_id_to_contributions_map,
            contract_id_to_country_map
        )

    transformed_df = pd.DataFrame(transformed_rows)

    # Define metadata rows
    metadata_rows = [
        {"DO NOT MODIFY OR DELETE THIS ROW": "Field", "CONTRACT_ID": "Contract ID", "EMPLOYEE_FULL_NAME":"Employee Full Name", "COMPONENT_NAME":"Component Name", "CURRENCY":"Currency", "BILLING_RATE_TYPE":"Billing Rate Type", "BILLING_RATE":"Billing Rate", "BILLING_FREQUENCY":"Billing Frequency", "PAY_SCHEDULE_NAME":"Pay Schedule Name", "IS_INSTALLMENT":"Is Installment?", "START_DATE":"Start Date", "END_DATE":"End Date", "NUMBER_OF_INSTALLMENTS":"Number of installments", "REASON_CODE":"Salary Change Reason Code", "SOURCE_EFFECTIVE_DATE":"Source Effective Date", "IS_BACKFILL_FOR_REVISION":"Is backfill for Revision", "NOTES":"Notes"},
        {"DO NOT MODIFY OR DELETE THIS ROW": "Field Description", "CONTRACT_ID": "Mandatory - Unique identifier for each employee", "EMPLOYEE_FULL_NAME": "Optional - Employee Full Name", "COMPONENT_NAME": "Mandatory - The name of the component.", "CURRENCY": "Mandatory - The currency of the entity.", "BILLING_RATE_TYPE": "Mandatory - Billing rate type.", "BILLING_RATE": "Mandatory - Billing Rate for compensation component.", "BILLING_FREQUENCY": "Mandatory - Billing Frequency for compensation component.", "PAY_SCHEDULE_NAME": "Mandatory - Payment schedule name.", "IS_INSTALLMENT": "Mandatory - Flag indicating whether component is an installment.", "START_DATE": "Mandatory - Start date of the component. For installment, it would be first installment start date", "END_DATE": "Optional - End date of the component. Leave it blank for indefinite end date.", "NUMBER_OF_INSTALLMENTS": "Optional - Number of installments to be paid.", "REASON_CODE":"Optional - Reason code for salary change.", "SOURCE_EFFECTIVE_DATE":"Mandatory - The component's effective date from older platform. Use the same date if it's in the past, or a relevant future date to reflect when it should take effect.", "IS_BACKFILL_FOR_REVISION":"Mandatory - Flag indicating whether component being back filled is a salary revision (If Yes) Or onboarding compensation (If No).", "NOTES":"Notes"}
    ]

    # Convert metadata rows to DataFrame
    metadata_df = pd.DataFrame(metadata_rows)

    # Concatenate metadata and transformed data
    final_df = pd.concat([metadata_df, transformed_df], ignore_index=True)

    return final_df

def build_pay_supplement_mapping_backfill_rows(compensation_data, pay_schedule_data_dictionary, salary_reviews_data, compensation_id_to_salary_review_map, entity, contract_id_to_entity_id_map, contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map, pay_supplement_mapping_backfill_all_transformed_rows, contract_id_to_ended_on_map, contract_id_to_status_map, contract_id_to_start_on_map, country_code=None, contract_id_to_country_map=None, contract_id_to_currency_map=None):
    """Collects rows for pay supplement mapping backfill where is_recurring_variable_compensation is true."""

    previous_contract_processed = None
    previous_start_date = None
    previous_comp_name = None

    # Process compensations data to find recurring variable compensation
    for _, row in compensation_data.iterrows():
        contract_id = str(row["contract_id"])
        if contract_id not in contract_id_to_entity_id_map:
            continue
        if str(contract_id_to_entity_id_map[contract_id]) != entity:
            continue
        if get_is_onboarding_contract(contract_id, contract_id_to_status_map):
            continue
        if should_exclude_onboarding_contract_before_2025(contract_id, contract_id_to_status_map, contract_id_to_start_on_map):
            continue
        # Filter by country if country_code and contract_id_to_country_map are provided
        if country_code and contract_id_to_country_map and contract_id in contract_id_to_country_map:
            if contract_id_to_country_map[contract_id] != country_code:
                continue
        if row["status"] != "ACTIVE" and row["status"] != "COMPLETED":
            continue
        if get_is_variable_contract_allowance_to_ignore(row):
            continue
        component_name = extract_component_name_for_compensation(row)
        if component_name in component_names_to_ignore:
            continue
        # Only include rows where is_recurring_variable_compensation is true
        if is_recurring_variable_compensation(row):
            if row["status"] == "ACTIVE":
                if extract_billing_frequency(row) == 'ONETIME':
                    curr_row_start_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                    transformed_row = {
                        "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                        "CONTRACT_ID": str(row["contract_id"]),
                        "EMPLOYEE_FULL_NAME": str(""),
                        "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                        "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                        "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                        "BILLING_RATE": str(""),
                        "BILLING_FREQUENCY": str("ONETIME"),
                        "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_pay_supplement(row, pay_schedule_data_dictionary)),
                        "IS_INSTALLMENT": str("Yes"),
                        "START_DATE": str(curr_row_start_date),
                        "END_DATE": str(""),
                        "NUMBER_OF_INSTALLMENTS": str("1"),
                        "PROCESSED_UNTIL_DATE": str(extract_processed_until_date(row, "PAY_SUPPLEMENT", {}, {}, start_date=curr_row_start_date, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map,pay_schedule_data_dictionary=pay_schedule_data_dictionary)),
                        "CORE_COMPENSATION_ID": str(row["id"])
                    }
                    previous_start_date = transformed_row["START_DATE"]
                    previous_comp_name = transformed_row["COMPONENT_NAME"]
                    previous_contract_processed = transformed_row["CONTRACT_ID"]
                    pay_supplement_mapping_backfill_all_transformed_rows.append(transformed_row)
                else:
                    curr_row_start_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                    curr_row_end_date = extract_end_date(row, "COMPENSATION", contract_id_to_ended_on_map, contract_id_to_status_map)
                    if curr_row_end_date <  curr_row_start_date and curr_row_end_date != "":
                        continue
                    transformed_row = {
                        "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                        "CONTRACT_ID": str(row["contract_id"]),
                        "EMPLOYEE_FULL_NAME": str(""),
                        "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                        "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                        "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                        "BILLING_RATE": str(""),
                        "BILLING_FREQUENCY": str(extract_billing_frequency(row)),
                        "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_compensation(row, pay_schedule_data_dictionary, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map)),
                        "IS_INSTALLMENT": str("No"),
                        "START_DATE": str(curr_row_start_date),
                        "END_DATE": str(curr_row_end_date),
                        "NUMBER_OF_INSTALLMENTS": str(""),
                        "PROCESSED_UNTIL_DATE": str(extract_processed_until_date(row, "COMPENSATION", contract_id_to_status_map, contract_id_to_ended_on_map, start_date=curr_row_start_date, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map,pay_schedule_data_dictionary=pay_schedule_data_dictionary)),
                        "CORE_COMPENSATION_ID": str(row["id"])
                    }
                    previous_start_date = transformed_row["START_DATE"]
                    previous_comp_name = transformed_row["COMPONENT_NAME"]
                    previous_contract_processed = transformed_row["CONTRACT_ID"]
                    pay_supplement_mapping_backfill_all_transformed_rows.append(transformed_row)
            elif row["status"] == "COMPLETED":
                if extract_billing_frequency(row) == 'ONETIME':
                    curr_comp_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                    transformed_row = {
                        "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                        "CONTRACT_ID": str(row["contract_id"]),
                        "EMPLOYEE_FULL_NAME": str(""),
                        "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                        "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                        "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                        "BILLING_RATE": str(""),
                        "BILLING_FREQUENCY": str("ONETIME"),
                        "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_pay_supplement(row, pay_schedule_data_dictionary)),
                        "IS_INSTALLMENT": str("Yes"),
                        "START_DATE": str(curr_comp_date),
                        "END_DATE": str(""),
                        "NUMBER_OF_INSTALLMENTS": str("1"),
                        "PROCESSED_UNTIL_DATE": str(extract_processed_until_date(row, "PAY_SUPPLEMENT", {}, {}, start_date=curr_comp_date, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map,pay_schedule_data_dictionary=pay_schedule_data_dictionary)),
                        "CORE_COMPENSATION_ID": str(row["id"])
                    }
                    previous_start_date = transformed_row["START_DATE"]
                    previous_comp_name = transformed_row["COMPONENT_NAME"]
                    previous_contract_processed = transformed_row["CONTRACT_ID"]
                    pay_supplement_mapping_backfill_all_transformed_rows.append(transformed_row)
                else:
                    curr_start_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                    curr_end_date = extract_end_date_for_old_comp(row, "COMPENSATION")
                    if str(previous_comp_name) == str(extract_component_name_for_compensation(row)) and str(previous_contract_processed) == str(row["contract_id"]):
                        curr_end_date_as_date = pd.to_datetime(previous_start_date, format='%Y-%m-%d', errors='coerce') - pd.Timedelta(days=1)
                        curr_end_date = curr_end_date_as_date.strftime('%Y-%m-%d')
                    # Handle edge case for duplicated data.
                    if curr_end_date < curr_start_date and curr_end_date != "":
                        continue

                    transformed_row = {
                        "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                        "CONTRACT_ID": str(row["contract_id"]),
                        "EMPLOYEE_FULL_NAME": str(""),
                        "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                        "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                        "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                        "BILLING_RATE": str(""),
                        "BILLING_FREQUENCY": str(extract_billing_frequency(row)),
                        "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_compensation(row, pay_schedule_data_dictionary, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map)),
                        "IS_INSTALLMENT": str("No"),
                        "START_DATE": str(curr_start_date),
                        "END_DATE": str(curr_end_date),
                        "NUMBER_OF_INSTALLMENTS": str(""),
                        "PROCESSED_UNTIL_DATE": str(extract_processed_until_date(row, "COMPENSATION", contract_id_to_status_map, contract_id_to_ended_on_map, start_date=curr_start_date, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map,pay_schedule_data_dictionary=pay_schedule_data_dictionary)),
                        "CORE_COMPENSATION_ID": str(row["id"])
                    }
                    previous_start_date = transformed_row["START_DATE"]
                    previous_comp_name = transformed_row["COMPONENT_NAME"]
                    previous_contract_processed = transformed_row["CONTRACT_ID"]
                    pay_supplement_mapping_backfill_all_transformed_rows.append(transformed_row)

def build_revision_mapping_backfill_rows(compensation_data, pay_schedule_data_dictionary, salary_reviews_data, compensation_id_to_salary_review_map, entity, contract_id_to_entity_id_map, contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map, revision_mapping_backfill_all_transformed_rows, contract_id_to_ended_on_map, contract_id_to_status_map, contract_id_to_start_on_map, country_code=None, contract_id_to_country_map=None, contract_id_to_currency_map=None):
    """Transforms the data from the old schema to the new schema revision backfill."""

    compensation_id_to_compensation_map = {}

    previous_contract_processed = None
    previous_start_date = None
    previous_comp_name = None
    # Process compensations data.
    for _, row in compensation_data.iterrows():
        contract_id = str(row["contract_id"])
        if contract_id not in contract_id_to_entity_id_map:
            continue
        if str(contract_id_to_entity_id_map[contract_id]) != entity:
            continue
        if should_exclude_onboarding_contract_before_2025(contract_id, contract_id_to_status_map, contract_id_to_start_on_map):
            continue
        # Filter by country if country_code and contract_id_to_country_map are provided
        if country_code and contract_id_to_country_map and contract_id in contract_id_to_country_map:
            if contract_id_to_country_map[contract_id] != country_code:
                continue
        if row["status"] != "ACTIVE" and row["status"] != "COMPLETED":
            continue
        if get_is_variable_contract_allowance_to_ignore(row):
            continue
        compensation_id_to_compensation_map[str(row["id"])] = row.to_dict()
        component_name = extract_component_name_for_compensation(row)
        if component_name in component_names_to_ignore:
            continue
        transformed_row = {}
        if row["status"] == "ACTIVE":
            if extract_billing_frequency(row) == 'ONETIME':
                billing_rate_resolved = row.get("amount", 0)
                if is_recurring_variable_compensation(row) and billing_rate_resolved == 0:
                    billing_rate_resolved = ''
                curr_row_start_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                transformed_row = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "CONTRACT_ID": str(row["contract_id"]),
                    "EMPLOYEE_FULL_NAME": str(""),
                    "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                    "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                    "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                    "BILLING_RATE": str(billing_rate_resolved),
                    "BILLING_FREQUENCY": str("ONETIME"),
                    "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_pay_supplement(row, pay_schedule_data_dictionary)),
                    "IS_INSTALLMENT": str("Yes"),
                    "START_DATE": str(curr_row_start_date),
                    "END_DATE": str(""),
                    "NUMBER_OF_INSTALLMENTS": str("1"),
                    "REASON_CODE": str(""),
                    "SOURCE_EFFECTIVE_DATE": str(extract_source_effective_date_for_compensation(row, compensation_id_to_salary_review_map, contract_id_to_previous_payroll_config_map=contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map, contract_id_to_start_on_map=contract_id_to_start_on_map)),
                    "IS_BACKFILL_FOR_REVISION": str(extract_is_backfill_for_revision(row, compensation_id_to_salary_review_map)),
                    "SALARY_REVISION_ID": str(extract_revision_id_or_empty(row, compensation_id_to_salary_review_map))
                }
                previous_start_date = transformed_row["START_DATE"]
                previous_comp_name = transformed_row["COMPONENT_NAME"]
                previous_contract_processed = transformed_row["CONTRACT_ID"]
            else:
                billing_rate_resolved = row.get("amount", 0)
                if is_recurring_variable_compensation(row) and billing_rate_resolved == 0:
                    billing_rate_resolved = ''
                curr_row_start_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                curr_row_end_date = extract_end_date(row, "COMPENSATION", contract_id_to_ended_on_map, contract_id_to_status_map)
                if curr_row_end_date < curr_row_start_date and curr_row_end_date != "":
                    continue
                transformed_row = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "CONTRACT_ID": str(row["contract_id"]),
                    "EMPLOYEE_FULL_NAME": str(""),
                    "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                    "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                    "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                    "BILLING_RATE": str(billing_rate_resolved),
                    "BILLING_FREQUENCY": str(extract_billing_frequency(row)),
                    "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_compensation(row, pay_schedule_data_dictionary, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map)),
                    "IS_INSTALLMENT": str("No"),
                    "START_DATE": str(curr_row_start_date),
                    "END_DATE": str(curr_row_end_date),
                    "NUMBER_OF_INSTALLMENTS": str(""),
                    "REASON_CODE": str(""),
                    "SOURCE_EFFECTIVE_DATE": str(extract_source_effective_date_for_compensation(row, compensation_id_to_salary_review_map, contract_id_to_previous_payroll_config_map=contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map, contract_id_to_start_on_map=contract_id_to_start_on_map)),
                    "IS_BACKFILL_FOR_REVISION": str(extract_is_backfill_for_revision(row, compensation_id_to_salary_review_map)),
                    "SALARY_REVISION_ID": str(extract_revision_id_or_empty(row, compensation_id_to_salary_review_map))
                }
                previous_start_date = transformed_row["START_DATE"]
                previous_comp_name = transformed_row["COMPONENT_NAME"]
                previous_contract_processed = transformed_row["CONTRACT_ID"]
        elif row["status"] == "COMPLETED":
            if extract_billing_frequency(row) == 'ONETIME':
                billing_rate_resolved = row.get("amount", 0)
                if is_recurring_variable_compensation(row) and billing_rate_resolved == 0:
                    billing_rate_resolved = ''
                curr_comp_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                transformed_row = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "CONTRACT_ID": str(row["contract_id"]),
                    "EMPLOYEE_FULL_NAME": str(""),
                    "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                    "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                    "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                    "BILLING_RATE": str(billing_rate_resolved),
                    "BILLING_FREQUENCY": str("ONETIME"),
                    "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_pay_supplement(row, pay_schedule_data_dictionary)),
                    "IS_INSTALLMENT": str("Yes"),
                    "START_DATE": str(curr_comp_date),
                    "END_DATE": str(""),
                    "NUMBER_OF_INSTALLMENTS": str("1"),
                    "REASON_CODE": str(""),
                    "SOURCE_EFFECTIVE_DATE": str(extract_source_effective_date_for_compensation(row, compensation_id_to_salary_review_map, contract_id_to_previous_payroll_config_map=contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map, contract_id_to_start_on_map=contract_id_to_start_on_map)),
                    "IS_BACKFILL_FOR_REVISION": str(extract_is_backfill_for_revision(row, compensation_id_to_salary_review_map)),
                    "SALARY_REVISION_ID": str(extract_revision_id_or_empty(row, compensation_id_to_salary_review_map))
                }
                previous_start_date = transformed_row["START_DATE"]
                previous_comp_name = transformed_row["COMPONENT_NAME"]
                previous_contract_processed = transformed_row["CONTRACT_ID"]
            else:
                curr_start_date = extract_start_date(row, "COMPENSATION", compensation_id_to_salary_review_map, contract_id_to_start_on_map)
                curr_end_date = extract_end_date_for_old_comp(row, "COMPENSATION")
                if str(previous_comp_name) == str(extract_component_name_for_compensation(row)) and str(previous_contract_processed) == str(row["contract_id"]):
                    curr_end_date_as_date = pd.to_datetime(previous_start_date, format='%Y-%m-%d', errors='coerce') - pd.Timedelta(days=1)
                    curr_end_date = curr_end_date_as_date.strftime('%Y-%m-%d')
                # Handle edge case for duplicated data.
                if curr_end_date < curr_start_date and curr_end_date != "":
                    continue
                billing_rate_resolved = row.get("amount", 0)
                if is_recurring_variable_compensation(row) and billing_rate_resolved == 0:
                    billing_rate_resolved = ''
                transformed_row = {
                    "DO NOT MODIFY OR DELETE THIS ROW": str(""),
                    "CONTRACT_ID": str(row["contract_id"]),
                    "EMPLOYEE_FULL_NAME": str(""),
                    "COMPONENT_NAME": str(str(extract_component_name_for_compensation(row))),
                    "CURRENCY": str(extract_currency(row["currency"], contract_id_to_currency_map.get(contract_id, ""))),
                    "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
                    "BILLING_RATE": str(billing_rate_resolved),
                    "BILLING_FREQUENCY": str(extract_billing_frequency(row)),
                    "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_compensation(row, pay_schedule_data_dictionary, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map)),
                    "IS_INSTALLMENT": str("No"),
                    "START_DATE": str(curr_start_date),
                    "END_DATE": str(curr_end_date),
                    "NUMBER_OF_INSTALLMENTS": str(""),
                    "REASON_CODE": str(""),
                    "SOURCE_EFFECTIVE_DATE": str(extract_source_effective_date_for_compensation(row, compensation_id_to_salary_review_map, contract_id_to_previous_payroll_config_map=contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map, contract_id_to_start_on_map=contract_id_to_start_on_map)),
                    "IS_BACKFILL_FOR_REVISION": str(extract_is_backfill_for_revision(row, compensation_id_to_salary_review_map)),
                    "SALARY_REVISION_ID": str(extract_revision_id_or_empty(row, compensation_id_to_salary_review_map))
                }
                previous_start_date = transformed_row["START_DATE"]
                previous_comp_name = transformed_row["COMPONENT_NAME"]
                previous_contract_processed = transformed_row["CONTRACT_ID"]
        if transformed_row["BILLING_RATE"] == 0 or transformed_row["BILLING_RATE"] == '0':
            continue
        if transformed_row["IS_BACKFILL_FOR_REVISION"] == "Yes":
            revision_mapping_backfill_all_transformed_rows.append(transformed_row)

    # Process pay supplements data.
    for _, row in pay_supplements_data.iterrows():
        contract_id = str(row["contract_id"])
        if contract_id not in contract_id_to_entity_id_map:
            continue
        if str(contract_id_to_entity_id_map[contract_id]) != entity:
            continue
        if should_exclude_onboarding_contract_before_2025(contract_id, contract_id_to_status_map, contract_id_to_start_on_map):
            continue
        if exclude_recurring_variable_or_one_time_pay_supplement_already_considered_from_compensation_table(row):
            continue
        # Filter by country if country_code and contract_id_to_country_map are provided
        if country_code and contract_id_to_country_map and contract_id in contract_id_to_country_map:
            if contract_id_to_country_map[contract_id] != country_code:
                continue
        if row["status"] == "REVOKED":
            continue
        component_name = extract_component_name_for_pay_supplement(row)
        if component_name in component_names_to_ignore:
            continue
        transformed_row = {
            "DO NOT MODIFY OR DELETE THIS ROW": str(""),
            "CONTRACT_ID": str(row["contract_id"]),
            "EMPLOYEE_FULL_NAME": str(""),
            "COMPONENT_NAME": str(str(extract_component_name_for_pay_supplement(row))),
            "CURRENCY": str(row["currency_code"]),
            "BILLING_RATE_TYPE": str(extract_billing_rate_type(row)),
            "BILLING_RATE": str(row.get("amount", 0)),
            "BILLING_FREQUENCY": str("ONETIME"),
            "PAY_SCHEDULE_NAME": str(extract_pay_schedule_name_for_pay_supplement(row, pay_schedule_data_dictionary)),
            "IS_INSTALLMENT": str("Yes"),
            "START_DATE": str(extract_start_date(row, "PAY_SUPPLEMENT", {}, contract_id_to_start_on_map)),
            "END_DATE": str(""),
            "NUMBER_OF_INSTALLMENTS": str("1"),
            "REASON_CODE": str(""),
            "SOURCE_EFFECTIVE_DATE": str(extract_source_effective_date_for_pay_supplement(row, compensation_id_to_salary_review_map, contract_id_to_previous_payroll_config_map=contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map=contract_id_to_new_payroll_config_map, contract_id_to_start_on_map=contract_id_to_start_on_map)),
            "IS_BACKFILL_FOR_REVISION": str("No"),
            "SALARY_REVISION_ID": str(extract_revision_id_or_empty(row, compensation_id_to_salary_review_map))
        }
        if transformed_row["BILLING_RATE"] == 0 or transformed_row["BILLING_RATE"] == '0':
            continue
        if transformed_row["IS_BACKFILL_FOR_REVISION"] == "Yes":
            revision_mapping_backfill_all_transformed_rows.append(transformed_row)

def transform_data_for_pay_supplement_mapping_backfill(pay_supplement_mapping_backfill_all_transformed_rows):
    transformed_df = pd.DataFrame(pay_supplement_mapping_backfill_all_transformed_rows)

    # Define metadata rows
    metadata_rows = [
        {"DO NOT MODIFY OR DELETE THIS ROW": "Field", "CONTRACT_ID": "Contract ID", "EMPLOYEE_FULL_NAME":"Employee Full Name", "COMPONENT_NAME":"Component Name", "CURRENCY":"Currency", "BILLING_RATE_TYPE":"Billing Rate Type", "BILLING_RATE":"Billing Rate", "BILLING_FREQUENCY":"Billing Frequency", "PAY_SCHEDULE_NAME":"Pay Schedule Name", "IS_INSTALLMENT":"Is Installment?", "START_DATE":"Start Date", "END_DATE":"End Date", "NUMBER_OF_INSTALLMENTS":"Number of installments", "PROCESSED_UNTIL_DATE":"Processed Until Date", "CORE_COMPENSATION_ID":"Core Compensation ID"},
        {"DO NOT MODIFY OR DELETE THIS ROW": "Field Description", "CONTRACT_ID": "Mandatory - Unique identifier for each employee", "EMPLOYEE_FULL_NAME": "Optional - Employee Full Name", "COMPONENT_NAME": "Mandatory - The name of the component.", "CURRENCY": "Mandatory - The currency of the entity.", "BILLING_RATE_TYPE": "Mandatory - Billing rate type.", "BILLING_RATE": "Mandatory - Billing Rate for compensation component.", "BILLING_FREQUENCY": "Mandatory - Billing Frequency for compensation component.", "PAY_SCHEDULE_NAME": "Mandatory - Payment schedule name.", "IS_INSTALLMENT": "Mandatory - Flag indicating whether component is an installment.", "START_DATE": "Mandatory - Start date of the component. For installment, it would be first installment start date", "END_DATE": "Optional - End date of the component. Leave it blank for indefinite end date.", "NUMBER_OF_INSTALLMENTS": "Optional - Number of installments to be paid.", "PROCESSED_UNTIL_DATE": "Optional - Last processed date of the component. Leave it blank create compensation items for all.", "CORE_COMPENSATION_ID":"Mandatory - ID of the original compensation record from which this pay supplement is created."}
    ]

    # Convert metadata rows to DataFrame
    metadata_df = pd.DataFrame(metadata_rows)

    # Resolve duplicates in the transformed data
    if not transformed_df.empty:
        print(f"Resolving duplicates in pay supplement mapping backfill data")
        transformed_df, stats = resolve_duplicates_in_dataframe(transformed_df, BASE_DUPLICATE_KEY_COLUMNS)
        print(f"Resolved duplicates: {stats['duplicate_groups']} duplicate groups, {stats['rows_renamed']} rows renamed")

    # Concatenate metadata and transformed data
    final_df = pd.concat([metadata_df, transformed_df], ignore_index=True)

    return final_df

def transform_data_for_revision_mapping_backfill(revision_mapping_backfill_all_transformed_rows):
    transformed_df = pd.DataFrame(revision_mapping_backfill_all_transformed_rows)

    # Define metadata rows
    metadata_rows = [
        {"DO NOT MODIFY OR DELETE THIS ROW": "Field", "CONTRACT_ID": "CONTRACT_ID", "EMPLOYEE_FULL_NAME":"EMPLOYEE_FULL_NAME", "COMPONENT_NAME":"COMPONENT_NAME", "CURRENCY":"CURRENCY", "BILLING_RATE_TYPE":"BILLING_RATE_TYPE", "BILLING_RATE":"BILLING_RATE", "BILLING_FREQUENCY":"BILLING_FREQUENCY", "PAY_SCHEDULE_NAME":"PAY_SCHEDULE_NAME", "IS_INSTALLMENT":"IS_INSTALLMENT", "START_DATE":"START_DATE", "END_DATE":"END_DATE", "NUMBER_OF_INSTALLMENTS":"NUMBER_OF_INSTALLMENTS", "REASON_CODE":"REASON_CODE", "SOURCE_EFFECTIVE_DATE":"SOURCE_EFFECTIVE_DATE", "IS_BACKFILL_FOR_REVISION":"IS_BACKFILL_FOR_REVISION", "SALARY_REVISION_ID":"SALARY_REVISION_ID"},
        {"DO NOT MODIFY OR DELETE THIS ROW": "Field", "CONTRACT_ID": "CONTRACT_ID", "EMPLOYEE_FULL_NAME":"EMPLOYEE_FULL_NAME", "COMPONENT_NAME":"COMPONENT_NAME", "CURRENCY":"CURRENCY", "BILLING_RATE_TYPE":"BILLING_RATE_TYPE", "BILLING_RATE":"BILLING_RATE", "BILLING_FREQUENCY":"BILLING_FREQUENCY", "PAY_SCHEDULE_NAME":"PAY_SCHEDULE_NAME", "IS_INSTALLMENT":"IS_INSTALLMENT", "START_DATE":"START_DATE", "END_DATE":"END_DATE", "NUMBER_OF_INSTALLMENTS":"NUMBER_OF_INSTALLMENTS", "REASON_CODE":"REASON_CODE", "SOURCE_EFFECTIVE_DATE":"SOURCE_EFFECTIVE_DATE", "IS_BACKFILL_FOR_REVISION":"IS_BACKFILL_FOR_REVISION", "SALARY_REVISION_ID":"SALARY_REVISION_ID"}
    ]

    # Convert metadata rows to DataFrame
    metadata_df = pd.DataFrame(metadata_rows)

    # Resolve duplicates in the transformed data
    if not transformed_df.empty:
        print(f"Resolving duplicates in revision mapping backfill data")
        transformed_df, stats = resolve_duplicates_in_dataframe(transformed_df, BASE_DUPLICATE_KEY_COLUMNS)
        print(f"Resolved duplicates: {stats['duplicate_groups']} duplicate groups, {stats['rows_renamed']} rows renamed")

    # Concatenate metadata and transformed data
    final_df = pd.concat([metadata_df, transformed_df], ignore_index=True)

    return final_df

# ===================================================================================================
# FILE OPERATIONS FUNCTIONS
# ===================================================================================================

def clean_dataframe(df):
    """Remove empty rows from the DataFrame.

    Args:
        df: The DataFrame to clean

    Returns:
        DataFrame: The cleaned DataFrame
    """
    return df.dropna(how='all')

def write_excel_for_schema(output_file, transformed_data):
    """Writes the transformed DataFrame to an Excel file with text formatting for Google Sheets.

    Args:
        output_file: The path to the output Excel file
        transformed_data: The transformed data to write
    """
    try:
        cleaned_data = clean_dataframe(transformed_data)
        # Ensure only the necessary columns are included
        if new_schema_format_enabled:
            schema_columns = [
                "DO NOT MODIFY OR DELETE THIS ROW", "SCHEMA_NAME", "COMPONENT_NAME",
                "CATEGORY_KEY", "IS_TAXABLE", "IS_FIXED", "IS_PRORATED",
                "IS_MANDATORY", "IS_PART_OF_BASE_PAY", "COUNTRY_CODE", "TAGS", "LABEL",
                "ITEM_TYPE", "VALIDATION", "CALCULATION", "BILLING_RATE_TYPE", "IS_OVERTIME_ELIGIBLE",
                "COMPONENT_DESCRIPTION", "BILLING_FREQUENCY", "PAY_SCHEDULE_NAME", "CURRENCY", "SCHEMA_DESCRIPTION", "IS_PART_OF_CTC"
            ]
        else:
            schema_columns = [
                "DO NOT MODIFY OR DELETE THIS ROW", "SCHEMA_NAME", "COMPONENT_NAME",
                "CATEGORY_KEY", "IS_TAXABLE", "IS_FIXED", "IS_PRORATED",
                "IS_MANDATORY", "IS_PART_OF_BASE_PAY", "COUNTRY_CODE", "TAGS", "LABEL"
            ]
        # Only keep columns that exist in the DataFrame
        columns_to_keep = [col for col in schema_columns if col in cleaned_data.columns]
        cleaned_data = cleaned_data[columns_to_keep]

        # First write the Excel file
        cleaned_data.to_excel(output_file, index=False, engine='openpyxl', sheet_name='COMPENSATION_SCHEMA')

        # Then open it to apply text formatting to all cells
        from openpyxl import load_workbook
        from openpyxl.styles import numbers

        wb = load_workbook(output_file)
        ws = wb.active

        # Apply text format to all cells
        for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
            for cell in row:
                cell.number_format = numbers.FORMAT_TEXT

        # Save the workbook with formatting
        wb.save(output_file)
    except Exception as e:
        log_exception(f"Error writing Excel file: {e}")
        sys.exit(1)

def write_excel_for_pay_schedule(output_file, transformed_data):
    """Writes the transformed DataFrame to an Excel file with text formatting for Google Sheets."""
    try:
        cleaned_data = clean_dataframe(transformed_data)
        # Ensure only the necessary columns are included
        pay_schedule_columns = [
            "DO NOT MODIFY OR DELETE THIS ROW", "PAY_SCHEDULE_NAME", "PAY_SCHEDULE_FREQUENCY",
            "REFERENCE_PERIOD_START_DATE", "REFERENCE_PERIOD_END_DATE", "PAY_DATE_RELATIVE_DAYS",
            "PAY_DATE_REFERENCE_TYPE", "IS_INSTALLMENT", "COUNTRY_CODE", "LABEL"
        ]

        # Only keep columns that exist in the DataFrame
        columns_to_keep = [col for col in pay_schedule_columns if col in cleaned_data.columns]
        cleaned_data = cleaned_data[columns_to_keep]

        # First write the Excel file
        cleaned_data.to_excel(output_file, index=False, engine='openpyxl', sheet_name='PAY_SCHEDULE')

        # Then open it to apply text formatting to all cells
        from openpyxl import load_workbook
        from openpyxl.styles import numbers

        wb = load_workbook(output_file)
        ws = wb.active

        # Apply text format to all cells
        for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
            for cell in row:
                cell.number_format = numbers.FORMAT_TEXT

        # Save the workbook with formatting
        wb.save(output_file)
    except Exception as e:
        log_exception(f"Error writing Excel file: {e}")
        sys.exit(1)

def write_excel_for_compensation_backfill(output_file, transformed_data):
    """Writes the transformed DataFrame to an Excel file with text formatting for Google Sheets."""
    try:
        cleaned_data = clean_dataframe(transformed_data)
        # Ensure only the necessary columns are included
        compensation_backfill_columns = [
            "DO NOT MODIFY OR DELETE THIS ROW", "CONTRACT_ID", "EMPLOYEE_FULL_NAME",
            "COMPONENT_NAME", "CURRENCY", "BILLING_RATE_TYPE", "BILLING_RATE",
            "BILLING_FREQUENCY", "PAY_SCHEDULE_NAME", "IS_INSTALLMENT", "START_DATE",
            "END_DATE", "NUMBER_OF_INSTALLMENTS", "PROCESSED_UNTIL_DATE", "NOTES"
        ]
        # Only keep columns that exist in the DataFrame
        columns_to_keep = [col for col in compensation_backfill_columns if col in cleaned_data.columns]
        cleaned_data = cleaned_data[columns_to_keep]

        # First write the Excel file
        cleaned_data.to_excel(output_file, index=False, engine='openpyxl', sheet_name='COMPENSATION_BACKFILL')

        # Then open it to apply text formatting to all cells
        from openpyxl import load_workbook
        from openpyxl.styles import numbers

        wb = load_workbook(output_file)
        ws = wb.active

        # Apply text format to all cells
        for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
            for cell in row:
                cell.number_format = numbers.FORMAT_TEXT

        # Save the workbook with formatting
        wb.save(output_file)
    except Exception as e:
        log_exception(f"Error writing Excel file: {e}")
        sys.exit(1)

def write_excel_for_revision_backfill(output_file, transformed_data):
    """Writes the transformed DataFrame to an Excel file with text formatting for Google Sheets."""
    try:
        cleaned_data = clean_dataframe(transformed_data)
        # Ensure only the necessary columns are included
        revision_backfill_columns = [
            "DO NOT MODIFY OR DELETE THIS ROW", "CONTRACT_ID", "EMPLOYEE_FULL_NAME",
            "COMPONENT_NAME", "CURRENCY", "BILLING_RATE_TYPE", "BILLING_RATE",
            "BILLING_FREQUENCY", "PAY_SCHEDULE_NAME", "IS_INSTALLMENT", "START_DATE",
            "END_DATE", "NUMBER_OF_INSTALLMENTS", "REASON_CODE", "SOURCE_EFFECTIVE_DATE",
            "IS_BACKFILL_FOR_REVISION", "NOTES"
        ]
        # Only keep columns that exist in the DataFrame
        columns_to_keep = [col for col in revision_backfill_columns if col in cleaned_data.columns]
        cleaned_data = cleaned_data[columns_to_keep]

        # First write the Excel file
        cleaned_data.to_excel(output_file, index=False, engine='openpyxl', sheet_name='COMPENSATION_REVISION_BACKFILL')

        # Then open it to apply text formatting to all cells
        from openpyxl import load_workbook
        from openpyxl.styles import numbers

        wb = load_workbook(output_file)
        ws = wb.active

        # Apply text format to all cells
        for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
            for cell in row:
                cell.number_format = numbers.FORMAT_TEXT

        # Save the workbook with formatting
        wb.save(output_file)
    except Exception as e:
        log_exception(f"Error writing Excel file: {e}")
        sys.exit(1)

def write_excel_for_pay_supplement_mapping_backfill(output_file, transformed_data):
    """Writes the transformed DataFrame to an Excel file with text formatting for Google Sheets."""
    try:
        cleaned_data = clean_dataframe(transformed_data)
        # Ensure only the necessary columns are included
        pay_supplement_mapping_columns = [
            "DO NOT MODIFY OR DELETE THIS ROW", "CONTRACT_ID", "EMPLOYEE_FULL_NAME",
            "COMPONENT_NAME", "CURRENCY", "BILLING_RATE_TYPE", "BILLING_RATE",
            "BILLING_FREQUENCY", "PAY_SCHEDULE_NAME", "IS_INSTALLMENT", "START_DATE",
            "END_DATE", "NUMBER_OF_INSTALLMENTS", "PROCESSED_UNTIL_DATE",
            "CORE_COMPENSATION_ID"
        ]
        # Only keep columns that exist in the DataFrame
        columns_to_keep = [col for col in pay_supplement_mapping_columns if col in cleaned_data.columns]
        cleaned_data = cleaned_data[columns_to_keep]

        # First write the Excel file
        cleaned_data.to_excel(output_file, index=False, engine='openpyxl', sheet_name='PAY_SUPPLEMENT_MAPPING_BACKFILL')

        # Then open it to apply text formatting to all cells
        from openpyxl import load_workbook
        from openpyxl.styles import numbers

        wb = load_workbook(output_file)
        ws = wb.active

        # Apply text format to all cells
        for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
            for cell in row:
                cell.number_format = numbers.FORMAT_TEXT

        # Save the workbook with formatting
        wb.save(output_file)
    except Exception as e:
        log_exception(f"Error writing Excel file: {e}")
        sys.exit(1)

def write_excel_for_revision_mapping_backfill(output_file, transformed_data):
    """Writes the transformed DataFrame to an Excel file with text formatting for Google Sheets."""
    try:
        cleaned_data = clean_dataframe(transformed_data)
        # Ensure only the necessary columns are included
        revision_mapping_columns = [
            "DO NOT MODIFY OR DELETE THIS ROW", "CONTRACT_ID", "EMPLOYEE_FULL_NAME",
            "COMPONENT_NAME", "CURRENCY", "BILLING_RATE_TYPE", "BILLING_RATE",
            "BILLING_FREQUENCY", "PAY_SCHEDULE_NAME", "IS_INSTALLMENT", "START_DATE",
            "END_DATE", "NUMBER_OF_INSTALLMENTS", "REASON_CODE", "SOURCE_EFFECTIVE_DATE",
            "IS_BACKFILL_FOR_REVISION", "SALARY_REVISION_ID"
        ]
        # Only keep columns that exist in the DataFrame
        columns_to_keep = [col for col in revision_mapping_columns if col in cleaned_data.columns]
        cleaned_data = cleaned_data[columns_to_keep]

        # First write the Excel file
        cleaned_data.to_excel(output_file, index=False, engine='openpyxl', sheet_name='REVISION_MAPPING_BACKFILL')

        # Then open it to apply text formatting to all cells
        from openpyxl import load_workbook
        from openpyxl.styles import numbers

        wb = load_workbook(output_file)
        ws = wb.active

        # Apply text format to all cells
        for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
            for cell in row:
                cell.number_format = numbers.FORMAT_TEXT

        # Save the workbook with formatting
        wb.save(output_file)
    except Exception as e:
        log_exception(f"Error writing Excel file: {e}")
        sys.exit(1)


def write_text_file_for_custom_params(output_file_custom_params, country_code):
    """Writes the custom params to a txt file."""
    try:
        with open(output_file_custom_params, 'w') as file:
            file.write(f"COUNTRY_CODE = {country_code}\n")
            file.write("TAGS = MULTIPLIER_EOR_OPS_APPROVED\n")
            file.write("CONFIGURATION_SCOPE = COUNTRY\n")
            file.write("OFFERING_TYPE = EOR\n")
            file.write(f"SCHEMA_NAME = {get_compensation_schema_name(country_code)}\n")
    except Exception as e:
        log_exception(f"Error writing Excel file: {e}")
        sys.exit(1)

def apply_excel_formatting(output_file):
    """Applies specific formatting to the Excel file and removes extra columns."""
    wb = load_workbook(output_file)
    ws = wb.active

    # Example: Make the header bold
    for cell in ws[3]:  # Assuming the actual data headers start at row 3
        cell.font = Font(bold=True)

    # Find the last column with data
    max_col = 0
    for row in ws.iter_rows():
        for cell in row:
            if cell.value is not None and cell.column > max_col:
                max_col = cell.column

    # Delete extra columns (if any)
    if max_col < ws.max_column:
        # Delete from right to left to avoid index shifting issues
        ws.delete_cols(max_col + 1, ws.max_column - max_col)

    # Save the workbook
    wb.save(output_file)


# ===================================================================================================
# MAIN FUNCTION
# ===================================================================================================

if __name__ == "__main__":
    print("\n\n-------------------------------------------VALIDATE INPUTS--------------------------------------------------\n\n")
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        log_exception("Please use correct parameters, expected format:")
        log_exception("python3 generateBulkUploadSheets.py <input_files_path>")
        sys.exit(1)

    input_file_path = sys.argv[1]
    if len(sys.argv) == 3:
        output_folder_path = sys.argv[2]

    legal_entities_file = input_file_path + "legal_entities.xlsx"
    payroll_cycles_file = input_file_path + "payroll_cycles.xlsx"
    contracts_file = input_file_path + "contracts.xlsx"
    old_schema_compensation_file = input_file_path + "compensations.xlsx"
    pay_supplements_input_file = input_file_path + "pay_supplements.xlsx"
    salary_reviews_file = input_file_path + "salary_reviews.xlsx"
    salary_review_to_compensation_mapping_file = input_file_path + "salary_review_to_compensation.xlsx"
    performance_reviews_file = input_file_path + "performance_reviews.xlsx"
    allowances_comp_label_to_component_name_file = input_file_path + "allowances_mapping.xlsx"
    salary_review_approved_dates_file = input_file_path + "salary_review_approved_dates.xlsx"
    payroll_cycle_config_to_contract_file = input_file_path + "payroll_cycle_config_to_contract.xlsx"
    configs_and_their_cutoffs_file = input_file_path + "configs_and_their_cutoffs.xlsx"
    contributions_for_CAN_file = input_file_path + "Contributions Migration.xlsx"

    if not os.path.isfile(legal_entities_file):
        log_exception(f"[ERROR] File not found: {legal_entities_file}")
        sys.exit(1)

    if not os.path.isfile(contracts_file):
        log_exception(f"ERROR] File not found: {contracts_file}")
        sys.exit(1)

    if not os.path.isfile(old_schema_compensation_file):
        log_exception(f"ERROR] File not found: {old_schema_compensation_file}")
        sys.exit(1)

    if not os.path.isfile(pay_supplements_input_file):
        log_exception(f"ERROR] File not found: {pay_supplements_input_file}")
        sys.exit(1)

    if not os.path.isfile(salary_reviews_file):
        log_exception(f"ERROR] File not found: {salary_reviews_file}")
        sys.exit(1)

    if not os.path.isfile(salary_review_to_compensation_mapping_file):
        log_exception(f"ERROR] File not found: {salary_review_to_compensation_mapping_file}")
        sys.exit(1)

    if not os.path.isfile(performance_reviews_file):
        log_exception(f"ERROR] File not found: {performance_reviews_file}")
        sys.exit(1)

    if not os.path.isfile(allowances_comp_label_to_component_name_file):
        log_exception(f"ERROR] File not found: {allowances_comp_label_to_component_name_file}")
        sys.exit(1)

    if not os.path.isfile(salary_review_approved_dates_file):
        log_exception(f"ERROR] File not found: {salary_review_approved_dates_file}")
        sys.exit(1)

    # Make payroll cycle config files optional
    if not os.path.isfile(payroll_cycle_config_to_contract_file):
        log_exception(f"ERROR] File not found: {payroll_cycle_config_to_contract_file}")
        sys.exit(1)

    if not os.path.isfile(configs_and_their_cutoffs_file):
        log_exception(f"ERROR] File not found: {configs_and_their_cutoffs_file}")
        sys.exit(1)

    if not os.path.isfile(contributions_for_CAN_file) and 'CAN' in countries_to_migrate:
        log_exception(f"ERROR] File not found: {contributions_for_CAN_file} and CAN is supposed to be migrated")
        sys.exit(1)

    entity_id_to_company_map = get_entity_id_to_company_map(legal_entities_file)

    contract_id_to_employee_id_map = get_contract_id_to_employee_id_map(contracts_file)
    contract_id_to_entity_id_map = get_contract_id_to_entity_id_map(contracts_file)
    contract_id_to_start_on_map = get_contract_id_to_start_on_map(contracts_file)
    contract_id_to_ended_on_map = get_contract_id_to_ended_on_map(contracts_file)
    contract_id_to_status_map = get_contract_id_to_status_map(contracts_file)
    contract_id_to_country_map = get_contract_id_to_country_map(contracts_file)
    contract_id_to_currency_map = get_contract_id_to_currency_map(contracts_file)

    # Create payroll cycle configuration mappings if files exist
    contract_id_to_previous_payroll_config_map = {}
    country_to_payroll_dates_map = {}
    config_id_to_new_cycle_details_map = {}
    contract_id_to_new_payroll_config_map = {}
    if os.path.isfile(payroll_cycle_config_to_contract_file) and os.path.isfile(configs_and_their_cutoffs_file):
        print("Loading payroll cycle configuration mappings...")
        contract_id_to_config_id_map = get_contract_id_to_config_id_map(payroll_cycle_config_to_contract_file)
        config_id_to_previous_cycle_details_map = get_config_id_to_previous_cycle_details_map(configs_and_their_cutoffs_file)
        contract_id_to_previous_payroll_config_map = get_contract_id_to_previous_payroll_config_map(contract_id_to_config_id_map, config_id_to_previous_cycle_details_map=config_id_to_previous_cycle_details_map)
        print(f"Loaded payroll cycle configurations for {len(contract_id_to_previous_payroll_config_map)} contracts")

        config_id_to_new_cycle_details_map = get_config_id_to_new_cycle_details_map(configs_and_their_cutoffs_file)
        contract_id_to_new_payroll_config_map = get_contract_id_to_new_payroll_config_map(contract_id_to_config_id_map, config_id_to_new_cycle_details_map=config_id_to_new_cycle_details_map)
        print(f"Loaded payroll cycle configurations for {len(contract_id_to_new_payroll_config_map)} contracts")

        # Create country to payroll dates mapping for TO_MIGRATE category
        country_to_payroll_dates_map = get_country_to_payroll_dates_map(configs_and_their_cutoffs_file)
        print(f"Loaded payroll date configurations for {len(country_to_payroll_dates_map)} countries.")
    else:
        log_exception("Payroll cycle configuration files not found or incomplete. Skipping payroll cycle configuration mapping.")

    # Create the country + entity to contracts map
    country_entity_to_contracts_map = get_country_entity_to_contracts_map(contracts_file, countries_to_migrate)

    # Populate the old 'allowances' compensation's label to schema component names map
    populate_old_allowances_comp_label_to_component_name_map(allowances_comp_label_to_component_name_file)

    # Populate the salary review approved dates map
    populate_salary_review_approved_dates_map(salary_review_approved_dates_file)

    # Debug output
    print(f"Found {len(country_entity_to_contracts_map)} country-entity pairs")
    if len(country_entity_to_contracts_map) == 0:
        log_exception("No country-entity pairs found. Check if contracts have country_code and workplace_entity_id fields.")

    old_schema_compensation_data = read_excel(old_schema_compensation_file)
    pay_supplements_data = pd.DataFrame()
    salary_reviews_data = pd.DataFrame()
    compensation_id_to_salary_review_map = {}
    salary_review_id_to_performance_review_id_map = get_salary_review_id_to_performance_review_id_map(performance_reviews_file)
    performance_reviews_to_disable = set()

    if os.path.isfile(pay_supplements_input_file):
        pay_supplements_data = read_excel(pay_supplements_input_file)

    if os.path.isfile(salary_reviews_file) and os.path.isfile(salary_review_to_compensation_mapping_file):
        salary_reviews_data = read_excel(salary_reviews_file)
        compensation_id_to_salary_review_map = convert_salary_review_sheets_to_map(salary_reviews_file, salary_review_to_compensation_mapping_file)

    contributions_data_for_CAN = {}
    contract_id_to_contributions_map = {}
    if os.path.isfile(contributions_for_CAN_file):
        contract_id_to_contributions_map = get_contract_id_to_contributions_map(contributions_for_CAN_file)

    print("\n\n-------------------------------------------SCRIPT START-----------------------------------------------------\n\n")

    output_dir = f"outputs"
    if output_folder_path:
        output_dir = output_folder_path
    os.makedirs(output_dir, exist_ok=True)
    output_file_revision_mapping_backfill = output_folder_path + "/REVISION_MAPPING_BACKFILL.xlsx"
    output_file_pay_supplement_mapping_backfill = output_folder_path + "/PAY_SUPPLEMENT_MAPPING_BACKFILL.xlsx"
    revision_mapping_backfill_all_transformed_rows = []
    pay_supplement_mapping_backfill_all_transformed_rows = []

    # Initialize global variables for the final revision mapping
    global_pay_schedule_data_dictionary = {}
    last_entity = None
    last_country_code = None

    # Iterate over country_entity pairs
    for country_entity_key, contract_ids in country_entity_to_contracts_map.items():
        # Split the key to get country_code and entity
        country_code, entity = country_entity_key.split('_')
        print(f"\nMIGRATING COUNTRY: {country_code}, ENTITY: {entity}\n")

        # Update the last values for the final revision mapping
        last_entity = entity
        last_country_code = country_code

        # Try to read country-specific schema file from the "Schema" sheet
        schema_file_path = f"{input_file_path}schemas/schema_{country_code}.xlsx"
        if os.path.isfile(schema_file_path):
            try:
                default_schema = read_excel(schema_file_path, sheet_name="Schema")
            except Exception as e:
                # If "Schema" sheet doesn't exist, try reading the first sheet
                log_exception(f"[WARNING]: 'Schema' sheet not found in {schema_file_path}. Trying first sheet.", level='WARNING')
                default_schema = read_excel(schema_file_path)
        else:
            log_exception(f"[WARNING]: Country-specific schema file not found: {schema_file_path}. Using default schema file instead.", level='WARNING')
            try:
                default_schema = read_excel(input_file_path + "schema.xlsx", sheet_name="Schema")
            except Exception as e:
                # If "Schema" sheet doesn't exist, try reading the first sheet
                log_exception(f"[WARNING]: 'Schema' sheet not found in default schema file. Trying first sheet.", level='WARNING')
                default_schema = read_excel(input_file_path + "schema.xlsx")

        # Create country-specific directory
        country_dir = f"{output_folder_path}/{country_code}"
        os.makedirs(country_dir, exist_ok=True)

        # Create entity-specific directory within the country directory
        output_dir = f"{country_dir}/ENTITY_{entity}_COMPANY_{entity_id_to_company_map.get(entity, entity)}_COUNTRY_{country_code}"
        os.makedirs(output_dir, exist_ok=True)

        pay_schedule_data_dictionary = {}

        # Output schema file at country level
        output_file_schema = f"{country_dir}/COMPENSATION_SCHEMA_{country_code}.xlsx"
        transformed_data_schema = transform_data_for_schema(default_schema, old_schema_compensation_data, pay_supplements_data, entity, contract_id_to_entity_id_map, country_code)

        # Apply component replacement
        if enable_component_replacement:
            print(f"\nReplacing compensation components for schema {country_code}_{entity}...")
            transformed_data_schema = replace_compensation_components(
                transformed_data_schema,
                input_file_path + GLOBAL_PAY_SUPPLEMENTS_FILE,
                input_file_path + GLOBAL_CONTRACT_ALLOWANCES_FILE
            )
            print(f"Component replacement completed for schema {country_code}_{entity}")

        write_excel_for_schema(output_file_schema, transformed_data_schema)
        apply_excel_formatting(output_file_schema)

        # Output pay schedule file at country level
        output_file_pay_schedule = f"{country_dir}/PAY_SCHEDULE_{country_code}.xlsx"
        transformed_data_pay_schedule = transform_data_for_pay_schedule(pay_schedule_data_dictionary, country_code, country_to_payroll_dates_map)
        write_excel_for_pay_schedule(output_file_pay_schedule, transformed_data_pay_schedule)
        apply_excel_formatting(output_file_pay_schedule)

        # Update the global dictionary after pay schedule is generated
        global_pay_schedule_data_dictionary.update(pay_schedule_data_dictionary)

        # Transform data for compensation backfill and revision backfill
        transformed_data_compensation_backfill = transform_data_for_compensation_backfill(old_schema_compensation_data, pay_schedule_data_dictionary, pay_supplements_data, compensation_id_to_salary_review_map, entity, contract_id_to_entity_id_map, contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map, contract_id_to_ended_on_map, contract_id_to_status_map, contract_id_to_start_on_map, country_code, contract_id_to_country_map, contract_id_to_currency_map=contract_id_to_currency_map, contract_id_to_contributions_map=contract_id_to_contributions_map)

        transformed_data_revision_backfill = transform_data_for_revision_backfill(old_schema_compensation_data, pay_schedule_data_dictionary, salary_reviews_data, compensation_id_to_salary_review_map, entity, contract_id_to_entity_id_map, contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map, contract_id_to_ended_on_map, contract_id_to_status_map, contract_id_to_start_on_map, country_code, contract_id_to_country_map, contract_id_to_currency_map=contract_id_to_currency_map, contract_id_to_contributions_map=contract_id_to_contributions_map)

        # Extract the data rows (skip the metadata rows)
        if len(transformed_data_compensation_backfill) >= 2 and len(transformed_data_revision_backfill) > 2:
            backfill_data = transformed_data_compensation_backfill.iloc[2:].reset_index(drop=True)
            revision_data = transformed_data_revision_backfill.iloc[2:].reset_index(drop=True)

            # Process both DataFrames together for cross-sheet consistency
            print(f"Resolving duplicates across compensation and revision backfill for entity {entity}")
            result = resolve_duplicates_across_dataframes(backfill_data, revision_data)
            print(f"Resolved duplicates across both sheets: {result['duplicate_groups']} duplicate groups, {result['rows_renamed']} rows renamed")

            # Get the metadata rows
            backfill_metadata = transformed_data_compensation_backfill.iloc[:2]
            revision_metadata = transformed_data_revision_backfill.iloc[:2]

            # Combine metadata rows with processed data
            transformed_data_compensation_backfill = pd.concat([backfill_metadata, result['backfill_df']], ignore_index=True)
            transformed_data_revision_backfill = pd.concat([revision_metadata, result['revision_df']], ignore_index=True)

        # Write the transformed data to Excel files
        output_file_compensation_backfill = f"{output_dir}/COMPENSATION_BACKFILL_{country_code}_{entity}.xlsx"
        write_excel_for_compensation_backfill(output_file_compensation_backfill, transformed_data_compensation_backfill)
        apply_excel_formatting(output_file_compensation_backfill)

        output_file_revision_backfill = f"{output_dir}/COMPENSATION_REVISION_BACKFILL_{country_code}_{entity}.xlsx"
        write_excel_for_revision_backfill(output_file_revision_backfill, transformed_data_revision_backfill)
        apply_excel_formatting(output_file_revision_backfill)

        # Output custom params file at country level
        output_file_custom_params = f"{country_dir}/CUSTOM_PARAMS_{country_code}.txt"
        write_text_file_for_custom_params(output_file_custom_params, country_code)

        build_revision_mapping_backfill_rows(old_schema_compensation_data, pay_schedule_data_dictionary, salary_reviews_data, compensation_id_to_salary_review_map, entity, contract_id_to_entity_id_map, contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map, revision_mapping_backfill_all_transformed_rows, contract_id_to_ended_on_map, contract_id_to_status_map, contract_id_to_start_on_map, country_code, contract_id_to_country_map, contract_id_to_currency_map=contract_id_to_currency_map)

        # Build pay supplement mapping backfill rows
        build_pay_supplement_mapping_backfill_rows(old_schema_compensation_data, pay_schedule_data_dictionary, salary_reviews_data, compensation_id_to_salary_review_map, entity, contract_id_to_entity_id_map, contract_id_to_previous_payroll_config_map, contract_id_to_new_payroll_config_map, pay_supplement_mapping_backfill_all_transformed_rows, contract_id_to_ended_on_map, contract_id_to_status_map, contract_id_to_start_on_map, country_code, contract_id_to_country_map, contract_id_to_currency_map=contract_id_to_currency_map)

        print(f"\nMIGRATED COUNTRY: {country_code}, ENTITY: {entity}\n")

    # Use the last entity and country_code for the revision mapping backfill
    if last_entity is not None:
        # Process revision mapping backfill
        transformed_data_revision_mapping_backfill = transform_data_for_revision_mapping_backfill(revision_mapping_backfill_all_transformed_rows)
        write_excel_for_revision_mapping_backfill(output_file_revision_mapping_backfill, transformed_data_revision_mapping_backfill)
        apply_excel_formatting(output_file_revision_mapping_backfill)

        # Process pay supplement mapping backfill
        transformed_data_pay_supplement_mapping_backfill = transform_data_for_pay_supplement_mapping_backfill(pay_supplement_mapping_backfill_all_transformed_rows)
        write_excel_for_pay_supplement_mapping_backfill(output_file_pay_supplement_mapping_backfill, transformed_data_pay_supplement_mapping_backfill)
        apply_excel_formatting(output_file_pay_supplement_mapping_backfill)

        print(f"Generated PAY_SUPPLEMENT_MAPPING_BACKFILL.xlsx with {len(pay_supplement_mapping_backfill_all_transformed_rows)} rows")
    else:
        log_exception("[ERROR] No country-entity pairs were processed. Skipping revision mapping backfill and pay supplement mapping backfill.")

    # Clean up duplicate entity directories in the main outputs folder
    for item in os.listdir("outputs"):
        if item.startswith("ENTITY_"):
            entity_path = os.path.join("outputs", item)
            if os.path.isdir(entity_path):
                import shutil
                print(f"Removing duplicate directory: {entity_path}")
                shutil.rmtree(entity_path)

    # Write the exceptions observed to an Excel file
    if exceptions_observed:
        print(f"Writing {len(exceptions_observed)} exceptions to EXCEPTIONS_OBSERVED.xlsx")
        exceptions_df = pd.DataFrame(exceptions_observed)

        # First write the Excel file
        exceptions_file = f"{output_folder_path}/EXCEPTIONS_OBSERVED.xlsx"
        exceptions_df.to_excel(exceptions_file, index=False, engine='openpyxl')

        # Then open it to apply text formatting to all cells
        from openpyxl import load_workbook
        from openpyxl.styles import numbers

        wb = load_workbook(exceptions_file)
        ws = wb.active

        # Apply text format to all cells
        for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
            for cell in row:
                cell.number_format = numbers.FORMAT_TEXT

        # Save the workbook with formatting
        wb.save(exceptions_file)

    print("\n\n-------------------------------------------SCRIPT COMPLETE--------------------------------------------------\n\n")
