tasks.withType<Wrapper> {
    gradleVersion = "8.8"
}

group = "com.multiplier"
version = "unspecified"

val versionName by extra { generateVersionName() }
val codeArtifactAuthToken = providers.environmentVariable("CODEARTIFACT_AUTH_TOKEN").orElse(providers.gradleProperty("codeartifact.token"))
val codeArtifactDir = ".gradle/codeArtifact"
val codeArtifactTokenFile = file("$codeArtifactDir/token")
val codeArtifactToken by extra { codeArtifactAuthToken.orNull ?: codeArtifactTokenFromFile() }

plugins {
    alias(kt.plugins.jvm) apply false

    alias(libs.plugins.sonarqube)
    alias(libs.plugins.detekt)
    alias(libs.plugins.ktlint)

    id("jacoco-report-aggregation")
}

subprojects {
    apply<io.gitlab.arturbosch.detekt.DetektPlugin>()
    configure<io.gitlab.arturbosch.detekt.extensions.DetektExtension> {
        config.from("$rootDir/detekt.yml")
        buildUponDefaultConfig = true
        autoCorrect = true

        tasks.withType<io.gitlab.arturbosch.detekt.Detekt> {
            reports {
                html.required = false
                xml.required = false
                md.required = false
                sarif.required = false
            }

            exclude {
                it.file.absolutePath.contains("generated")
            }
        }
    }

    apply<org.jlleitschuh.gradle.ktlint.KtlintPlugin>()
    configure<org.jlleitschuh.gradle.ktlint.KtlintExtension> {
        version = "1.3.0"
        outputToConsole = true
        verbose = true
        coloredOutput = false

        filter {
            exclude("**/build/**")
        }

        reporters {
            reporter(org.jlleitschuh.gradle.ktlint.reporter.ReporterType.PLAIN)
        }
    }

    apply<JacocoPlugin>()
    tasks.withType<JacocoReport> {
        // tests are required to run before generating the report
        dependsOn(tasks.withType<Test>())

        reports {
            xml.required = true
            html.required = false
            csv.required = false
        }
    }
}

allprojects {
    apply<IdeaPlugin>()

    tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
        compilerOptions {
            jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_21
        }
        kotlinOptions {
            freeCompilerArgs += "-Xjsr305=strict"
        }
    }

    // TODO: Remove this once the library is upgraded in the platform-starter.
    configurations.all {
        exclude(group = "io.netty", module = "netty-common")
        exclude(group = "io.netty", module = "netty-handler")
        exclude(group = "net.minidev", module = "json-smart")
    }

    plugins.withType<JavaPlugin> {
        configure<JavaPluginExtension> {
            sourceCompatibility = JavaVersion.VERSION_21
            targetCompatibility = JavaVersion.VERSION_21
        }

        tasks.withType<Test> {
            useJUnitPlatform()
        }
    }

    plugins.withType<MavenPublishPlugin> {
        configure<PublishingExtension> {
            repositories {
                multiplierRepository()
            }
        }
    }

    repositories {
        mavenCentral()
        mavenLocal()
        multiplierRepository()
    }
}

tasks.withType<Jar> {
    enabled = false
}

tasks.register("downloadDependencies") {
    allprojects {
        dependsOn(tasks.named("dependencies"))
    }
}

configure<JacocoPluginExtension> {
    tasks.register<JacocoReport>("jacocoAggregateTestReport") {
        group = LifecycleBasePlugin.VERIFICATION_GROUP
        description = "Generates an aggregate test coverage report from all subprojects"

        subprojects {
            dependsOn(tasks.withType<JacocoReport>())

            sourceSets.named("main") {
                <EMAIL>(this)
            }

            val execFiles = layout.buildDirectory.files("jacoco/test.exec").filter { it.exists() }
            executionData(execFiles)
        }

        reports {
            xml.required = true
            xml.outputLocation = reportsDirectory.file("jacocoAggregateTestReport.xml")
            html.required = false
            csv.required = false
        }
    }
}

configure<org.sonarqube.gradle.SonarExtension> {
    tasks.withType<org.sonarqube.gradle.SonarTask> {
        subprojects {
            dependsOn(tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>())
        }
    }

    val exclusions = listOf(
        "**/testing/fixture/**",
    )

    val xmlReportPaths = fileTree(jacoco.reportsDirectory) { include("*.xml") }.files

    properties {
        property("sonar.host.url", "https://sonarcloud.io")
        property("sonar.projectKey", "Multiplier-Core_compensation-service")
        property("sonar.projectName", "compensation-service")
        property("sonar.organization", "multiplier")
        property("sonar.java.coveragePlugin", "jacoco")
        property("sonar.coverage.jacoco.xmlReportPaths", xmlReportPaths)
        property("sonar.coverage.exclusions", exclusions)
    }
}

fun RepositoryHandler.multiplierRepository() {
    maven(url = "https://multiplier-artifacts-778085304246.d.codeartifact.ap-southeast-1.amazonaws.com/maven/multiplier-artifacts/") {
        credentials {
            username = "aws"
            password = codeArtifactToken
        }
    }
}

fun generateVersionName(): String = if (project.hasProperty("publishVersion")) {
    "${project.properties["publishVersion"]}"
} else {
    java.io.ByteArrayOutputStream().use {
        exec {
            commandLine("git", "describe", "--tags", "--always")
            standardOutput = it
        }
        it.toString().trim().removePrefix("v")
    }
}

tasks.named(org.gradle.tooling.model.kotlin.dsl.KotlinDslModelsParameters.PREPARATION_TASK_NAME) {
    dependsOn(generateCodeArtifactToken)
}

fun codeArtifactTokenFromFile() = codeArtifactTokenFile.takeIf { it.exists() }?.readText()?.trim()

val codeArtifactExpirationFile = file("$codeArtifactDir/expiration")

val generateCodeArtifactToken = tasks.register("generateCodeArtifactToken") {
    group = "build setup"
    description = "Generate CodeArtifact token"

    dependsOn(awsLogin)

    onlyIf { !codeArtifactAuthToken.isPresent }

    inputs.property("profile", codeArtifactProfile)

    outputs.files(codeArtifactExpirationFile, codeArtifactTokenFile)
    outputs.upToDateWhen { codeArtifactTokenValid }

    doLast {
        logger.info("Generating CodeArtifact token")
        java.io.ByteArrayOutputStream().use {
            exec {
                commandLine(
                    "aws",
                    "codeartifact",
                    "get-authorization-token",
                    "--domain",
                    "multiplier-artifacts",
                    "--domain-owner",
                    "778085304246",
                    "--query",
                    "{expiration:expiration, token:authorizationToken}",
                    "--output",
                    "text",
                    "--profile",
                    codeArtifactProfile,
                )
                standardOutput = it
            }

            val (expiration, token) = it.toString().split(Regex("\\s"))

            codeArtifactExpirationFile.writeText(expiration)
            codeArtifactTokenFile.writeText(token)
        }
    }
}
val awsLogin = tasks.register("awsLogin") {
    group = "build setup"
    description = "Login to AWS SSO"

    onlyIf { !codeArtifactAuthToken.isPresent && !codeArtifactTokenValid && !awsLoggedIn }

    doLast {
        logger.info("Logging in to AWS")
        java.io.ByteArrayOutputStream().use {
            try {
                exec {
                    commandLine(
                        "aws",
                        "sso",
                        "login",
                        "--profile",
                        codeArtifactProfile,
                    )
                    errorOutput = it
                }
            } catch (e: Exception) {
                logger.error(it.toString())
                throw IllegalStateException("Could not login to AWS", e)
            }
        }
    }
}

val codeArtifactProfile by lazy {
    providers.environmentVariable("AWS_PROFILE")
        .orElse(providers.gradleProperty("codeartifact.profile"))
        .orElse("prod-codeartifact-token-profile")
        .get()
}

val codeArtifactTokenValid by lazy {
    codeArtifactExpirationFile.exists() &&
        java.time.OffsetDateTime.now() < java.time.OffsetDateTime.parse(codeArtifactExpirationFile.readText().trim())
}

val awsLoggedIn: Boolean by lazy {
    java.io.ByteArrayOutputStream().use {
        try {
            exec {
                commandLine(
                    "aws",
                    "sts",
                    "get-caller-identity",
                    "--query",
                    "Account",
                    "--output",
                    "text",
                    "--profile",
                    codeArtifactProfile,
                )
                standardOutput = it
                errorOutput = it
            }
            true
        } catch (e: Exception) {
            logger.info("Not logged in")
            logger.debug(it.toString(), e)
            false
        }
    }
}

configure<JacocoPluginExtension> {
    toolVersion = "0.8.10" // Specify the JaCoCo tool version
}

tasks.register<JacocoReport>("jacocoIntegrationTestReport") {
    group = "verification"
    description = "Generates an aggregate code coverage report for integration tests."

    subprojects.forEach { subproject ->
        subproject.tasks.findByName("integrationTest")?.let {
            dependsOn(it)
            executionData(subproject.layout.buildDirectory.file("jacoco/integrationTest.exec"))
        }
    }

    sourceDirectories.setFrom(
        files(
            subprojects.flatMap { subproject ->
                val sourceSets = subproject.extensions.findByType<SourceSetContainer>()
                sourceSets?.getByName("main")?.allSource?.srcDirs ?: emptyList()
            },
        ),
    )

    classDirectories.setFrom(
        files(
            subprojects.flatMap { subproject ->
                val sourceSets = subproject.extensions.findByType<SourceSetContainer>()
                sourceSets?.getByName("main")?.output ?: emptyList()
            },
        ),
    )

    reports {
        html.required.set(true)
        html.outputLocation.set(layout.buildDirectory.dir("reports/jacoco/integrationTest"))
        xml.required.set(true)
        xml.outputLocation.set(layout.buildDirectory.file("reports/jacoco/integrationTestReport.xml"))
    }
}
