package com.multiplier.compensation.database.mapper.compensationinput

import com.fasterxml.uuid.impl.TimeBasedEpochGenerator
import com.multiplier.compensation.database.mapper.compensation.common.toDatabase
import com.multiplier.compensation.database.mapper.compensation.common.toDomain
import com.multiplier.compensation.database.mapper.payschedule.common.toDomain
import com.multiplier.compensation.database.tables.records.CompensationInputAudRecord
import com.multiplier.compensation.database.tables.records.CompensationInputRecord
import com.multiplier.compensation.database.tables.references.COMPENSATION_INPUT
import com.multiplier.compensation.database.tables.references.COMPENSATION_SCHEMA_ITEM
import com.multiplier.compensation.database.tables.references.PAY_SCHEDULE
import com.multiplier.compensation.domain.common.PayrollStatus
import com.multiplier.compensation.domain.common.ReasonCode
import com.multiplier.compensation.domain.common.toReasonCodeName
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationinput.CompensationInput
import com.multiplier.compensation.domain.compensationinput.CompensationInputEnriched
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus
import com.multiplier.transaction.database.jooq.audit.Audit
import org.jooq.Record
import java.time.LocalDateTime

fun CompensationInput.toRecord() = CompensationInputRecord(
    id = this.id,
    companyId = this.companyId,
    entityId = this.entityId,
    contractId = this.contractId,
    schemaItemId = this.schemaItemId,
    category = this.category,
    currency = this.currency,
    billingRateType = this.billingRateType.toDatabase(),
    billingRate = this.billingRate,
    billingFrequency = this.billingFrequency.toDatabase(),
    payScheduleId = this.payScheduleId,
    startDate = this.startDate,
    endDate = this.endDate,
    isInstallment = this.isInstallment,
    noOfInstallments = this.noOfInstallments,
    reasonCode = this.reasonCode?.toString(),
    requestType = this.requestType.toString(),
    requestId = this.requestId,
    status = this.status.toString(),
    payrollCutOffDate = this.payrollCutoffDate,
    payrollStatus = this.payrollStatus?.toString(),
    notes = this.notes,
    lifecycle = this.lifecycle?.toDatabase(),
    sourceEffectiveDate = this.sourceEffectiveDate,
    createdOn = this.createdOn,
    createdBy = this.createdBy,
    updatedOn = this.updatedOn,
    updatedBy = this.updatedBy,
)

fun CompensationInputRecord.toDomain() = CompensationInput(
    id = this.id,
    companyId = this.companyId,
    entityId = this.entityId,
    contractId = this.contractId,
    schemaItemId = this.schemaItemId,
    category = this.category,
    currency = this.currency,
    billingRateType = this.billingRateType.toDomain(),
    billingRate = this.billingRate,
    billingFrequency = this.billingFrequency.toDomain(),
    payScheduleId = this.payScheduleId,
    startDate = this.startDate,
    endDate = this.endDate,
    isInstallment = this.isInstallment,
    noOfInstallments = this.noOfInstallments,
    requestType = RequestType.valueOf(this.requestType),
    requestId = this.requestId,
    status = CompensationInputStatus.valueOf(this.status),
    notes = this.notes,
    payrollStatus = this.payrollStatus?.let { PayrollStatus.entries.find { status -> status.name == it } },
    payrollCutoffDate = this.payrollCutOffDate,
    reasonCode = this.reasonCode?.let { ReasonCode.valueOf(it.toReasonCodeName()) },
    lifecycle = this.lifecycle?.toDomain(),
    sourceEffectiveDate = this.sourceEffectiveDate,
    createdOn = this.createdOn,
    createdBy = this.createdBy,
    updatedOn = this.updatedOn,
    updatedBy = this.updatedBy,
)

fun CompensationInputRecord.applyAudit(audit: Audit) = this.apply {
    this.createdBy = audit.userId
    this.createdOn = audit.time
    this.updatedBy = audit.userId
    this.updatedOn = audit.time
}

fun CompensationInputRecord.applyUpdateOnly() = this.apply {
    changed(COMPENSATION_INPUT.ID, false)
    changed(COMPENSATION_INPUT.ENTITY_ID, false)
    changed(COMPENSATION_INPUT.COMPANY_ID, false)
    changed(COMPENSATION_INPUT.CONTRACT_ID, false)
    changed(COMPENSATION_INPUT.SCHEMA_ITEM_ID, false)
    changed(COMPENSATION_INPUT.CATEGORY, false)
    changed(COMPENSATION_INPUT.CURRENCY, false)
    changed(COMPENSATION_INPUT.REQUEST_TYPE, false)
    changed(COMPENSATION_INPUT.CREATED_ON, false)
    changed(COMPENSATION_INPUT.CREATED_BY, false)
}

fun CompensationInputRecord.toAudit(audit: Audit) = CompensationInputAudRecord(
    id = this.id,
    rev = audit.revision,
    companyId = this.companyId,
    entityId = this.entityId,
    contractId = this.contractId,
    schemaItemId = this.schemaItemId,
    category = this.category,
    currency = this.currency,
    billingRateType = this.billingRateType,
    billingRate = this.billingRate,
    billingFrequency = this.billingFrequency,
    payScheduleId = this.payScheduleId,
    startDate = this.startDate,
    endDate = this.endDate,
    isInstallment = this.isInstallment,
    noOfInstallments = this.noOfInstallments,
    reasonCode = this.reasonCode,
    requestType = this.requestType,
    requestId = this.requestId,
    status = this.status,
    notes = this.notes,
    payrollCutOffDate = this.payrollCutOffDate,
    payrollStatus = this.payrollStatus,
    lifecycle = this.lifecycle,
    sourceEffectiveDate = this.sourceEffectiveDate,
    createdOn = this.createdOn,
    createdBy = this.createdBy,
    updatedOn = audit.time,
    updatedBy = audit.userId,
)

@Suppress("UnsafeCallOnNullableType")
fun Record.toCompensationInputEnriched(): CompensationInputEnriched = CompensationInputEnriched(
    id = this[COMPENSATION_INPUT.ID]!!,
    companyId = this[COMPENSATION_INPUT.COMPANY_ID]!!,
    entityId = this[COMPENSATION_INPUT.ENTITY_ID]!!,
    contractId = this[COMPENSATION_INPUT.CONTRACT_ID]!!,
    schemaItemId = this[COMPENSATION_INPUT.SCHEMA_ITEM_ID]!!,
    category = this[COMPENSATION_INPUT.CATEGORY]!!,
    currency = this[COMPENSATION_INPUT.CURRENCY]!!,
    billingRateType = this[COMPENSATION_INPUT.BILLING_RATE_TYPE]!!.toDomain(),
    billingRate = this[COMPENSATION_INPUT.BILLING_RATE],
    billingFrequency = this[COMPENSATION_INPUT.BILLING_FREQUENCY]!!.toDomain(),
    startDate = this[COMPENSATION_INPUT.START_DATE]!!,
    endDate = this[COMPENSATION_INPUT.END_DATE],
    isInstallment = this[COMPENSATION_INPUT.IS_INSTALLMENT]!!,
    noOfInstallments = this[COMPENSATION_INPUT.NO_OF_INSTALLMENTS],
    requestType = this[COMPENSATION_INPUT.REQUEST_TYPE]?.let { RequestType.valueOf(it) }!!,
    requestId = this[COMPENSATION_INPUT.REQUEST_ID]!!,
    status = this[COMPENSATION_INPUT.STATUS]?.let { CompensationInputStatus.valueOf(it) }!!,
    notes = this[COMPENSATION_INPUT.NOTES],
    createdOn = this[COMPENSATION_INPUT.CREATED_ON]!!,
    createdBy = this[COMPENSATION_INPUT.CREATED_BY]!!,
    updatedOn = this[COMPENSATION_INPUT.UPDATED_ON]!!,
    updatedBy = this[COMPENSATION_INPUT.UPDATED_BY]!!,
    // Enriched fields
    payScheduleName = this[PAY_SCHEDULE.PAY_SCHEDULE_NAME]!!,
    payScheduleFrequency = this[PAY_SCHEDULE.FREQUENCY]!!.toDomain(),
    schemaComponentName = this[COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME]!!,
    compensationName = this[COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME]!!,
    isTaxable = this[COMPENSATION_SCHEMA_ITEM.IS_TAXABLE]!!,
    isFixed = this[COMPENSATION_SCHEMA_ITEM.IS_FIXED]!!,
    isProrated = this[COMPENSATION_SCHEMA_ITEM.IS_PRORATED]!!,
    isMandatory = this[COMPENSATION_SCHEMA_ITEM.IS_MANDATORY]!!,
    isPartOfBasePay = this[COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY]!!,
    compensationCategory = this[COMPENSATION_SCHEMA_ITEM.CATEGORY]!!,
    lifecycle = this[COMPENSATION_INPUT.LIFECYCLE]?.toDomain(),
    sourceEffectiveDate = this[COMPENSATION_INPUT.SOURCE_EFFECTIVE_DATE],
    label = this[COMPENSATION_SCHEMA_ITEM.LABEL]!!,
    isOvertimeEligible = this[COMPENSATION_SCHEMA_ITEM.IS_OVERTIME_ELIGIBLE]!!,
    isPartOfCtc = this[COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC]!!,
)

fun CompensationInput.toCompensation(uuidGenerator: TimeBasedEpochGenerator): Compensation = Compensation(
    id = uuidGenerator.generate(),
    companyId = this.companyId,
    entityId = this.entityId,
    contractId = this.contractId,
    schemaItemId = this.schemaItemId,
    category = this.category,
    currency = this.currency,
    billingRateType = this.billingRateType,
    billingRate = this.billingRate,
    billingFrequency = this.billingFrequency,
    payScheduleId = this.payScheduleId,
    startDate = this.startDate,
    endDate = this.endDate,
    isInstallment = this.isInstallment,
    noOfInstallments = this.noOfInstallments,
    status = CompensationStatus.NEW,
    notes = this.notes,
    createdOn = LocalDateTime.now(),
    createdBy = this.createdBy,
    updatedOn = LocalDateTime.now(),
    updatedBy = this.updatedBy,
)
