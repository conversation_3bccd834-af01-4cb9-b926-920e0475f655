package com.multiplier.compensation.database.util

import com.multiplier.compensation.database.enums.Lifecycle
import com.multiplier.compensation.database.tables.references.COMPENSATION_INPUT
import com.multiplier.compensation.database.tables.references.COMPENSATION_SCHEMA_ITEM
import com.multiplier.compensation.domain.compensationinput.CompensationInputFilter
import com.multiplier.compensation.domain.compensationinput.CompensationWatermarkingInputFilter
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus
import com.multiplier.compensation.domain.compensationschema.derivedSchemaItemTypes
import org.jooq.Condition
import org.jooq.impl.DSL

object CompensationInputConditionBuilder {
    fun buildInputConditions(filter: CompensationInputFilter): List<Condition> {
        val conditions = mutableListOf<Condition>()

        if (filter.ids.isNotEmpty()) {
            conditions.add(COMPENSATION_INPUT.ID.`in`(filter.ids))
        }

        if (filter.includedCategories.isNotEmpty()) {
            conditions.add(COMPENSATION_INPUT.CATEGORY.`in`(filter.includedCategories))
        }

        if (filter.excludedCategories.isNotEmpty()) {
            conditions.add(COMPENSATION_INPUT.CATEGORY.notIn(filter.excludedCategories))
        }

        if (filter.updatedFromExclusive != null) {
            conditions.add(COMPENSATION_INPUT.UPDATED_ON.greaterThan(filter.updatedFromExclusive))
        }

        if (filter.updatedToInclusive != null) {
            conditions.add(COMPENSATION_INPUT.UPDATED_ON.lessOrEqual(filter.updatedToInclusive))
        }

        if (filter.contractIds.isNotEmpty()) {
            conditions.add(COMPENSATION_INPUT.CONTRACT_ID.`in`(filter.contractIds))
        }

        if (filter.requestId?.isNotEmpty() == true) {
            conditions.add(COMPENSATION_INPUT.REQUEST_ID.eq(filter.requestId))
        }

        if (filter.statuses.isNotEmpty()) {
            conditions.add(COMPENSATION_INPUT.STATUS.`in`(filter.statuses))
        }

        if (filter.excludeExtrapolatedInputs) {
            conditions.add(COMPENSATION_SCHEMA_ITEM.ITEM_TYPE.notIn(derivedSchemaItemTypes))
        }

        return conditions
    }

    /**
     * Builds conditions for fetching compensation inputs based on the provided watermarking input filter.
     * This method combines conditions for both new data (buildWatermarkingInputConditionsForNewData())
     * and back-filled source data (buildWatermarkingInputConditionsForBackFilledSourceData).
     *
     * @param requestFilter The filter criteria for fetching compensation inputs.
     * @return A list of conditions to be used in a query.
     */
    fun buildWatermarkingInputConditions(requestFilter: CompensationWatermarkingInputFilter): List<Condition> {
        val conditionsForNewData =
            buildWatermarkingInputConditionsForNewData(requestFilter)
        val conditionsForBackFilledSourceData =
            buildWatermarkingInputConditionsForBackFilledSourceData(requestFilter)
        return listOf(
            DSL.and(conditionsForNewData).or(
                DSL.and(conditionsForBackFilledSourceData),
            ),
        )
    }

    /**
     * Builds conditions for fetching compensation inputs which are created fresh in the new platform.
     * We specifically filter only those records which have lifecycle as NEW. (Meaning they were created on new platform)
     *      * conditions.add(COMPENSATION_INPUT.LIFECYCLE.eq(Lifecycle.NEW))
     * To include any back-filled inputs, we use the subsequent method - buildWatermarkingInputConditionsForBackFilledSourceData()
     */
    private fun buildWatermarkingInputConditionsForNewData(
        filter: CompensationWatermarkingInputFilter,
    ): List<Condition> {
        val conditions = mutableListOf<Condition>()

        conditions.add(COMPENSATION_INPUT.LIFECYCLE.eq(Lifecycle.NEW))

        if (filter.ids.isNotEmpty()) {
            conditions.add(COMPENSATION_INPUT.ID.`in`(filter.ids))
        }

        if (filter.includedCategories.isNotEmpty()) {
            conditions.add(COMPENSATION_INPUT.CATEGORY.`in`(filter.includedCategories))
        }

        if (filter.excludedCategories.isNotEmpty()) {
            conditions.add(COMPENSATION_INPUT.CATEGORY.notIn(filter.excludedCategories))
        }

        if (filter.updatedFromExclusive != null) {
            conditions.add(COMPENSATION_INPUT.UPDATED_ON.greaterThan(filter.updatedFromExclusive))
        }

        if (filter.updatedToInclusive != null) {
            conditions.add(COMPENSATION_INPUT.UPDATED_ON.lessOrEqual(filter.updatedToInclusive))
        }

        conditions.add(COMPENSATION_INPUT.STATUS.eq(CompensationInputStatus.ACTIVATED.toString()))

        return conditions
    }

    /**
     * Builds conditions for fetching future dated salary revisions/compensation inputs which are migrated/back-filled from older platform.
     * We specifically filter only those records which have lifecycle as MIGRATED. (Meaning they were back-filled from older platform)
     *      * conditions.add(COMPENSATION_INPUT.LIFECYCLE.eq(Lifecycle.MIGRATED))
     * We also filter the records based on their source effective date (As source effective date is set only for back-filled data
     * and is set with a specific timestamp/date value as to when the compensation inputs are supposed to be included in payroll input).
     *      * conditions.add(COMPENSATION_INPUT.SOURCE_EFFECTIVE_DATE.greaterThan(filter.updatedFromExclusive))
     *      * conditions.add(COMPENSATION_INPUT.SOURCE_EFFECTIVE_DATE.lessOrEqual(filter.updatedToInclusive))
     */
    private fun buildWatermarkingInputConditionsForBackFilledSourceData(
        filter: CompensationWatermarkingInputFilter,
    ): List<Condition> {
        val conditions = mutableListOf<Condition>()

        conditions.add(COMPENSATION_INPUT.LIFECYCLE.eq(Lifecycle.MIGRATED))

        if (filter.ids.isNotEmpty()) {
            conditions.add(COMPENSATION_INPUT.ID.`in`(filter.ids))
        }

        if (filter.includedCategories.isNotEmpty()) {
            conditions.add(COMPENSATION_INPUT.CATEGORY.`in`(filter.includedCategories))
        }

        if (filter.excludedCategories.isNotEmpty()) {
            conditions.add(COMPENSATION_INPUT.CATEGORY.notIn(filter.excludedCategories))
        }

        if (filter.updatedFromExclusive != null) {
            conditions.add(COMPENSATION_INPUT.SOURCE_EFFECTIVE_DATE.greaterThan(filter.updatedFromExclusive))
        }

        if (filter.updatedToInclusive != null) {
            conditions.add(COMPENSATION_INPUT.SOURCE_EFFECTIVE_DATE.lessOrEqual(filter.updatedToInclusive))
        }

        conditions.add(COMPENSATION_INPUT.STATUS.eq(CompensationInputStatus.ACTIVATED.toString()))

        return conditions
    }
}
