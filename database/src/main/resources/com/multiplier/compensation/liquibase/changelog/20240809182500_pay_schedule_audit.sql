-- liquibase formatted sql

-- changeset Vaibhav Srivastava:20240809182500_pay_schedule_audit

CREATE TABLE compensation.pay_schedule_aud (
        id                                  UUID                                    ,
        rev                                 UUID                                    ,
        entity_id                           BIGINT                                  NOT NULL,
        company_id                          BIGINT                                  NOT NULL,
        pay_schedule_name                   TEXT                                    NOT NULL,
        frequency                           compensation.pay_schedule_frequency     NOT NULL,
        start_date_reference                DATE                                    NOT NULL,
        end_date_reference                  DATE                                    NOT NULL,
        relative_pay_days                   BIGINT                                  NOT NULL,
        pay_date_reference_type             compensation.pay_date_reference         NOT NULL,
        is_installment                      BOOLEAN                                 NOT NULL,
        is_active                           BOOLEAN                                 NOT NULL,
        created_on                          TIMESTAMP                               NOT NULL,
        created_by                          BIGINT                                  NOT NULL,
        updated_on                          TIMESTAMP                               NOT NULL,
        updated_by                          BIGINT                                  NOT NULL,

        PRIMARY KEY (id, rev)
);
