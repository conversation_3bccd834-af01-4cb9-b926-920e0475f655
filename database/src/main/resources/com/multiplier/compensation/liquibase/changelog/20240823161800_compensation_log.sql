-- liquibase formatted sql

-- changeset <PERSON><PERSON><PERSON>:20240823161800_compensation_log

CREATE TABLE compensation.compensation_log (
    id                                  UUID                                PRIMARY KEY NOT NULL,
    company_id                          BIGINT                              ,
    entity_id                           BIGINT                              NOT NULL,
    contract_id                         BIGINT                              ,
    schema_item_id                      UUID                                ,
    category                            VARCHAR(255)                        ,
    currency                            VARCHAR(3)                          ,
    billing_rate_type                   compensation.billing_rate_type      ,
    billing_rate                        DOUBLE PRECISION                    ,
    billing_frequency                   compensation.billing_frequency      ,
    pay_schedule_id                     UUID                                ,
    start_date                          DATE                                ,
    end_date                            DATE                                ,
    is_installment                      BOOLEAN                             ,
    no_of_installments                  INTEGER                             ,
    reason_code                         VARCHAR(8)                          ,
    request_type                        VARCHAR(255)                        NOT NULL,
    request_id                          VARCHAR(255)                        NOT NULL,
    status                              VARCHAR(255)                        NOT NULL,
    created_on                          TIMESTAMP                           NOT NULL,
    created_by                          BIGINT                              NOT NULL,
    updated_on                          TIMESTAMP                           NOT NULL,
    updated_by                          BIGINT                              NOT NULL
);

CREATE INDEX compensation_log_created_on_idx ON compensation.compensation_log (created_on);
CREATE INDEX compensation_log_created_on_status_idx ON compensation.compensation_log (created_on, status);
