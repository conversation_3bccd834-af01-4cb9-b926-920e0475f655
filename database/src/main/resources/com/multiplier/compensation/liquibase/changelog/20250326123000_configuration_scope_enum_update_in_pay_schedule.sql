-- liquibase formatted sql

-- changeset <PERSON>endran S:20250326123000_configuration_scope_enum_update_in_pay_schedule.sql.sql

-- Add new column to the pay_schedule table
ALTER TABLE compensation.pay_schedule
ADD COLUMN configuration_scope compensation.configuration_scope;

-- Back-fill existing data with CLIENT enum value
UPDATE compensation.pay_schedule
SET configuration_scope = COALESCE(configuration_scope, 'COMPANY');

-- Ensure the new column is not nullable
ALTER TABLE compensation.pay_schedule
ALTER COLUMN configuration_scope SET NOT NULL;
