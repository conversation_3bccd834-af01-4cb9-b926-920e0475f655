-- liquibase formatted sql

-- changeset Vaibhav Srivastava:20241115132800_update_compensation_status_enum

ALTER TYPE compensation.compensation_status RENAME TO compensation_status_old;

CREATE TYPE compensation.compensation_status AS ENUM (
    'NEW',
    'PROCESSING',
    'COMPLETED',
    'DELETED',
    'ABORTED'
);

ALTER TABLE compensation.compensation_item
    ALTER COLUMN compensation_status TYPE compensation.compensation_status
    USING compensation_status::text::compensation.compensation_status;

ALTER TABLE compensation.compensation
ALTER COLUMN status TYPE compensation.compensation_status
    USING status::text::compensation.compensation_status;

ALTER TABLE compensation.compensation_aud
ALTER COLUMN status TYPE compensation.compensation_status
    USING status::text::compensation.compensation_status;

DROP TYPE compensation.compensation_status_old;
