-- liquibase formatted sql

-- changeset Abhishek Yadav:20250521173132_update_billing_rate_type_enum

ALTER TYPE compensation.billing_rate_type RENAME TO billing_rate_type_old;

CREATE TYPE compensation.billing_rate_type AS ENUM (
    'VALUE',
    'BASE_PAY_PERCENTAGE',
    'BASE_PAY_DAYS',
    'CTC_DAYS',
    'CTC_PERCENTAGE'
);

ALTER TABLE compensation.compensation
ALTER COLUMN billing_rate_type TYPE compensation.billing_rate_type
    USING billing_rate_type::text::compensation.billing_rate_type;

ALTER TABLE compensation.compensation_aud
ALTER COLUMN billing_rate_type TYPE compensation.billing_rate_type
    USING billing_rate_type::text::compensation.billing_rate_type;

ALTER TABLE compensation.compensation_input
ALTER COLUMN billing_rate_type TYPE compensation.billing_rate_type
    USING billing_rate_type::text::compensation.billing_rate_type;

ALTER TABLE compensation.compensation_input_aud
ALTER COLUMN billing_rate_type TYPE compensation.billing_rate_type
    USING billing_rate_type::text::compensation.billing_rate_type;

ALTER TABLE compensation.compensation_item
ALTER COLUMN billing_rate_type TYPE compensation.billing_rate_type
    USING billing_rate_type::text::compensation.billing_rate_type;

ALTER TABLE compensation.compensation_item_aud
ALTER COLUMN billing_rate_type TYPE compensation.billing_rate_type
    USING billing_rate_type::text::compensation.billing_rate_type;

ALTER TABLE compensation.compensation_log
ALTER COLUMN billing_rate_type TYPE compensation.billing_rate_type
    USING billing_rate_type::text::compensation.billing_rate_type;

DROP TYPE compensation.billing_rate_type_old;

