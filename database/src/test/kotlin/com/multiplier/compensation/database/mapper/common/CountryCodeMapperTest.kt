package com.multiplier.compensation.database.mapper.common

import com.multiplier.compensation.domain.common.CountryCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import com.multiplier.compensation.database.enums.CountryCode as DatabaseCountryCode

class CountryCodeMapperTest {
    @Test
    fun testToDatabaseConversion() {
        // Test a sample of mappings to ensure the conversion is correct
        assertEquals(DatabaseCountryCode.ABW, CountryCode.ABW.toDatabase())
        assertEquals(DatabaseCountryCode.IND, CountryCode.IND.toDatabase())
        assertEquals(DatabaseCountryCode.USA, CountryCode.USA.toDatabase())
        assertEquals(DatabaseCountryCode.ZAF, CountryCode.ZAF.toDatabase())
        assertEquals(DatabaseCountryCode.AFG, CountryCode.AFG.toDatabase())
    }

    @Test
    fun testToDomainConversion() {
        // Test a sample of mappings to ensure the conversion is correct
        assertEquals(CountryCode.ABW, DatabaseCountryCode.ABW.toDomain())
        assertEquals(CountryCode.IND, DatabaseCountryCode.IND.toDomain())
        assertEquals(CountryCode.USA, DatabaseCountryCode.USA.toDomain())
        assertEquals(CountryCode.ZAF, DatabaseCountryCode.ZAF.toDomain())
        assertEquals(CountryCode.AFG, DatabaseCountryCode.AFG.toDomain())
    }

    @Test
    fun testInvalidCountryCodeConversion() {
        assertThrows<IllegalArgumentException> {
            DatabaseCountryCode.valueOf("INVALID").toDomain()
        }
        assertThrows<IllegalArgumentException> {
            CountryCode.valueOf("INVALID").toDatabase()
        }
    }

    @Test
    fun testFullMappingToDatabase() {
        // Ensure that all CountryCodes map to their respective DatabaseCountryCodes
        for (countryCode in CountryCode.entries) {
            val dbCountryCode: DatabaseCountryCode = countryCode.toDatabase()
            assertNotNull(dbCountryCode, "Mapping for $countryCode is null")
            assertEquals(
                dbCountryCode.name,
                countryCode.name,
                "Mismatch between CountryCode and DatabaseCountryCode for $countryCode",
            )
        }
    }

    @Test
    fun testFullMappingToDomain() {
        // Ensure that all DatabaseCountryCodes map to their respective CountryCodes
        for (dbCountryCode in DatabaseCountryCode.entries) {
            val domainCountryCode: CountryCode = dbCountryCode.toDomain()
            assertNotNull(domainCountryCode, "Mapping for $dbCountryCode is null")
            assertEquals(
                domainCountryCode.name,
                dbCountryCode.name,
                "Mismatch between DatabaseCountryCode and CountryCode for $dbCountryCode",
            )
        }
    }
}
