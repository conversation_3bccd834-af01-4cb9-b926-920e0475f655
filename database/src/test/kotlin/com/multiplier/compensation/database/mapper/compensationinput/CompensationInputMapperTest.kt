package com.multiplier.compensation.database.mapper.compensationinput

import com.fasterxml.uuid.impl.TimeBasedEpochGenerator
import com.multiplier.compensation.database.enums.BillingFrequency
import com.multiplier.compensation.database.enums.BillingRateType
import com.multiplier.compensation.database.enums.Lifecycle
import com.multiplier.compensation.database.enums.PayScheduleFrequency
import com.multiplier.compensation.database.mapper.compensation.common.toDatabase
import com.multiplier.compensation.database.tables.records.CompensationInputRecord
import com.multiplier.compensation.database.tables.references.COMPENSATION_INPUT
import com.multiplier.compensation.database.tables.references.COMPENSATION_SCHEMA_ITEM
import com.multiplier.compensation.database.tables.references.PAY_SCHEDULE
import com.multiplier.compensation.domain.common.PayrollStatus
import com.multiplier.compensation.domain.common.ReasonCode
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationinput.CompensationInput
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus
import com.multiplier.compensation.domain.compensationinput.enums.CompensationLifecycle
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNull

class CompensationInputMapperTest {
    @Test
    fun `test toDomain maps all fields correctly`() {
        val inputRecord = CompensationInputRecord(
            id = UUID.randomUUID(),
            companyId = 1,
            entityId = 2,
            contractId = 3,
            schemaItemId = UUID.randomUUID(),
            category = "Base Pay",
            currency = "USD",
            billingRateType = BillingRateType.VALUE,
            billingRate = 100.0,
            billingFrequency = BillingFrequency.MONTHLY,
            payScheduleId = UUID.randomUUID(),
            startDate = LocalDate.of(2025, 1, 1),
            endDate = LocalDate.of(2025, 12, 31),
            isInstallment = true,
            noOfInstallments = 12,
            reasonCode = ReasonCode.SC001.name,
            requestType = RequestType.COMPENSATION_SETUP.name,
            requestId = UUID.randomUUID().toString(),
            status = CompensationInputStatus.ACTIVATED.name,
            payrollCutOffDate = LocalDate.of(2025, 6, 15),
            payrollStatus = "STAGED",
            createdOn = LocalDateTime.of(2025, 1, 1, 12, 0),
            createdBy = -1,
            updatedOn = LocalDateTime.of(2025, 6, 1, 12, 0),
            updatedBy = -1,
            lifecycle = Lifecycle.NEW,
            sourceEffectiveDate = LocalDateTime.of(2025, 1, 1, 12, 0),
        )

        val expectedDomain = CompensationInput(
            id = inputRecord.id,
            companyId = inputRecord.companyId,
            entityId = inputRecord.entityId,
            contractId = inputRecord.contractId,
            schemaItemId = inputRecord.schemaItemId,
            category = inputRecord.category,
            currency = inputRecord.currency,
            billingRateType = com.multiplier.compensation.domain.common.BillingRateType.VALUE,
            billingRate = inputRecord.billingRate,
            billingFrequency = com.multiplier.compensation.domain.common.BillingFrequency.MONTHLY,
            payScheduleId = inputRecord.payScheduleId,
            startDate = inputRecord.startDate,
            endDate = inputRecord.endDate,
            isInstallment = inputRecord.isInstallment,
            noOfInstallments = inputRecord.noOfInstallments,
            reasonCode = ReasonCode.SC001,
            requestType = RequestType.COMPENSATION_SETUP,
            requestId = inputRecord.requestId,
            status = CompensationInputStatus.ACTIVATED,
            payrollCutoffDate = inputRecord.payrollCutOffDate,
            payrollStatus = PayrollStatus.STAGED,
            createdOn = inputRecord.createdOn,
            createdBy = inputRecord.createdBy,
            updatedOn = inputRecord.updatedOn,
            updatedBy = inputRecord.updatedBy,
            lifecycle = CompensationLifecycle.NEW,
            sourceEffectiveDate = inputRecord.sourceEffectiveDate,
        )

        val actualDomain = inputRecord.toDomain()

        // Verify that all fields match
        assertEquals(expectedDomain, actualDomain, "The toDomain function mapped all fields.")
    }

    @Test
    fun `test toDomain throws exception for invalid enum values`() {
        val inputRecord = CompensationInputRecord(
            id = UUID.randomUUID(),
            companyId = 1,
            entityId = 2,
            contractId = 3,
            schemaItemId = UUID.randomUUID(),
            category = "Base Pay",
            currency = "USD",
            billingRateType = BillingRateType.VALUE,
            billingRate = 100.0,
            billingFrequency = BillingFrequency.MONTHLY,
            payScheduleId = UUID.randomUUID(),
            startDate = LocalDate.of(2025, 1, 1),
            isInstallment = false,
            requestType = RequestType.COMPENSATION_SETUP.name,
            requestId = UUID.randomUUID().toString(),
            status = "INVALID_STATUS",
            createdOn = LocalDateTime.of(2025, 1, 1, 12, 0),
            createdBy = -1,
            updatedOn = LocalDateTime.of(2025, 1, 1, 12, 0), // Null updatedOn
            updatedBy = -1,
        )

        assertThrows<IllegalArgumentException> { inputRecord.toDomain() }
    }

    @Test
    fun `test toDomain handles null optional fields`() {
        val inputRecord = CompensationInputRecord(
            id = UUID.randomUUID(),
            companyId = 1,
            entityId = 2,
            contractId = 3,
            schemaItemId = UUID.randomUUID(),
            category = "Base Pay",
            currency = "USD",
            billingRateType = BillingRateType.VALUE,
            billingRate = 100.0,
            billingFrequency = BillingFrequency.MONTHLY,
            payScheduleId = UUID.randomUUID(),
            startDate = LocalDate.of(2025, 1, 1),
            endDate = null,
            isInstallment = false,
            noOfInstallments = null,
            reasonCode = null,
            requestType = RequestType.COMPENSATION_SETUP.name,
            requestId = UUID.randomUUID().toString(),
            status = CompensationInputStatus.ACTIVATED.name,
            payrollCutOffDate = null,
            payrollStatus = null,
            createdOn = LocalDateTime.of(2025, 1, 1, 12, 0),
            createdBy = -1,
            updatedOn = LocalDateTime.of(2025, 1, 1, 12, 0), // Null updatedOn
            updatedBy = -1,
            lifecycle = null,
            sourceEffectiveDate = null,
        )

        val expectedDomain = CompensationInput(
            id = inputRecord.id,
            companyId = inputRecord.companyId,
            entityId = inputRecord.entityId,
            contractId = inputRecord.contractId,
            schemaItemId = inputRecord.schemaItemId,
            category = inputRecord.category,
            currency = inputRecord.currency,
            billingRateType = com.multiplier.compensation.domain.common.BillingRateType.VALUE,
            billingRate = inputRecord.billingRate,
            billingFrequency = com.multiplier.compensation.domain.common.BillingFrequency.MONTHLY,
            payScheduleId = inputRecord.payScheduleId,
            startDate = inputRecord.startDate,
            endDate = null,
            isInstallment = inputRecord.isInstallment,
            noOfInstallments = null,
            reasonCode = null,
            requestType = RequestType.COMPENSATION_SETUP,
            requestId = inputRecord.requestId,
            status = CompensationInputStatus.ACTIVATED,
            payrollStatus = null,
            createdOn = inputRecord.createdOn,
            createdBy = inputRecord.createdBy,
            updatedOn = inputRecord.updatedOn,
            updatedBy = inputRecord.updatedBy,
            lifecycle = null,
            sourceEffectiveDate = null,
        )

        val actualDomain = inputRecord.toDomain()

        // Verify that all fields match
        assertEquals(
            expectedDomain,
            actualDomain,
            "The toDomain function did not correctly handle null optional fields.",
        )
    }

    @Test
    fun `test toCompensationInputEnriched mapping with null fields`() {
        val record = mockk<org.jooq.Record>()
        every { record[COMPENSATION_INPUT.ID] } returns UUID.randomUUID()
        every { record[COMPENSATION_INPUT.COMPANY_ID] } returns 1L
        every { record[COMPENSATION_INPUT.ENTITY_ID] } returns 2L
        every { record[COMPENSATION_INPUT.CONTRACT_ID] } returns 3L
        every { record[COMPENSATION_INPUT.SCHEMA_ITEM_ID] } returns UUID.randomUUID()
        every { record[COMPENSATION_INPUT.CATEGORY] } returns "CONTRACT_BASE_PAY"
        every { record[COMPENSATION_INPUT.CURRENCY] } returns "USD"
        every { record[COMPENSATION_INPUT.BILLING_RATE_TYPE] } returns BillingRateType.VALUE
        every { record[COMPENSATION_INPUT.BILLING_RATE] } returns 0.0
        every { record[COMPENSATION_INPUT.BILLING_FREQUENCY] } returns BillingFrequency.MONTHLY
        every { record[COMPENSATION_INPUT.START_DATE] } returns LocalDate.of(2023, 1, 1)
        every { record[COMPENSATION_INPUT.END_DATE] } returns null
        every { record[COMPENSATION_INPUT.IS_INSTALLMENT] } returns false
        every { record[COMPENSATION_INPUT.NO_OF_INSTALLMENTS] } returns null
        every { record[COMPENSATION_INPUT.REQUEST_TYPE] } returns "COMPENSATION_SETUP"
        every { record[COMPENSATION_INPUT.REQUEST_ID] } returns UUID.randomUUID().toString()
        every { record[COMPENSATION_INPUT.STATUS] } returns "ACTIVATED"
        every { record[COMPENSATION_INPUT.CREATED_ON] } returns LocalDateTime.of(2023, 1, 1, 12, 0)
        every { record[COMPENSATION_INPUT.CREATED_BY] } returns -1
        every { record[COMPENSATION_INPUT.UPDATED_ON] } returns LocalDateTime.of(2023, 5, 1, 12, 0)
        every { record[COMPENSATION_INPUT.UPDATED_BY] } returns -1
        every { record[PAY_SCHEDULE.PAY_SCHEDULE_NAME] } returns "MONTHLY-01"
        every { record[PAY_SCHEDULE.FREQUENCY] } returns PayScheduleFrequency.MONTHLY
        every { record[COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME] } returns "Base Pay"
        every { record[COMPENSATION_SCHEMA_ITEM.IS_TAXABLE] } returns true

        every { record[COMPENSATION_SCHEMA_ITEM.IS_FIXED] } returns true
        every { record[COMPENSATION_SCHEMA_ITEM.IS_PRORATED] } returns true
        every { record[COMPENSATION_SCHEMA_ITEM.IS_MANDATORY] } returns false
        every { record[COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY] } returns true
        every { record[COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC] } returns true
        every { record[COMPENSATION_SCHEMA_ITEM.CATEGORY] } returns "CONTRACT_BASE_PAY"
        every { record[COMPENSATION_INPUT.LIFECYCLE] } returns null
        every { record[COMPENSATION_INPUT.SOURCE_EFFECTIVE_DATE] } returns null
        every { record[COMPENSATION_INPUT.NOTES] } returns null
        every { record[COMPENSATION_SCHEMA_ITEM.LABEL] } returns "Label"
        every { record[COMPENSATION_SCHEMA_ITEM.IS_OVERTIME_ELIGIBLE] } returns false

        val result = record.toCompensationInputEnriched()

        assertEquals("USD", result.currency)
        assertEquals(0.0, result.billingRate)
        assertEquals(false, result.isInstallment)
        assertEquals("CONTRACT_BASE_PAY", result.category)
        assertNull(result.endDate)
        assertNull(result.noOfInstallments)
        assertEquals("COMPENSATION_SETUP", result.requestType.name)
        assertEquals("ACTIVATED", result.status.name)
        assertEquals("MONTHLY-01", result.payScheduleName)
        assertEquals(
            com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency.MONTHLY,
            result.payScheduleFrequency,
        )
        assertEquals("Base Pay", result.compensationName)
        assertEquals(true, result.isTaxable)
        assertEquals(true, result.isFixed)
        assertEquals(true, result.isProrated)
        assertEquals(false, result.isMandatory)
        assertEquals(true, result.isPartOfBasePay)
        assertEquals("CONTRACT_BASE_PAY", result.compensationCategory)
        assertEquals("Label", result.label)
        assertNull(result.lifecycle)
        assertNull(result.sourceEffectiveDate)
        assertNull(result.notes)
    }

    @Test
    fun `test toCompensation with valid CompensationInput`() {
        val uuidGenerator = mockk<TimeBasedEpochGenerator>()
        val expectedUuid = UUID.randomUUID()
        every { uuidGenerator.generate() } returns expectedUuid

        val compensationInput = CompensationInput(
            id = UUID.randomUUID(),
            companyId = 1,
            entityId = 2,
            contractId = 3,
            schemaItemId = UUID.randomUUID(),
            category = "Base Pay",
            currency = "USD",
            billingRateType = com.multiplier.compensation.domain.common.BillingRateType.VALUE,
            billingRate = 100.0,
            billingFrequency = com.multiplier.compensation.domain.common.BillingFrequency.MONTHLY,
            payScheduleId = UUID.randomUUID(),
            startDate = LocalDate.of(2025, 1, 1),
            endDate = LocalDate.of(2025, 12, 31),
            isInstallment = true,
            noOfInstallments = 12,
            requestType = RequestType.COMPENSATION_SETUP,
            requestId = UUID.randomUUID().toString(),
            status = CompensationInputStatus.ACTIVATED,
            createdBy = -1,
            updatedBy = -1,
        )

        val result = compensationInput.toCompensation(uuidGenerator)

        assertEquals(expectedUuid, result.id)
        assertEquals(compensationInput.companyId, result.companyId)
        assertEquals(compensationInput.entityId, result.entityId)
        assertEquals(compensationInput.contractId, result.contractId)
        assertEquals(compensationInput.schemaItemId, result.schemaItemId)
        assertEquals(compensationInput.category, result.category)
        assertEquals(compensationInput.currency, result.currency)
        assertEquals(compensationInput.billingRateType, result.billingRateType)
        assertEquals(compensationInput.billingRate, result.billingRate)
        assertEquals(compensationInput.billingFrequency, result.billingFrequency)
        assertEquals(compensationInput.payScheduleId, result.payScheduleId)
        assertEquals(compensationInput.startDate, result.startDate)
        assertEquals(compensationInput.endDate, result.endDate)
        assertEquals(compensationInput.isInstallment, result.isInstallment)
        assertEquals(compensationInput.noOfInstallments, result.noOfInstallments)
        assertEquals(com.multiplier.compensation.domain.compensation.enums.CompensationStatus.NEW, result.status)
        assertEquals(compensationInput.createdBy, result.createdBy)
        assertEquals(compensationInput.updatedBy, result.updatedBy)
    }

    @Test
    fun `test toRecord with valid CompensationInput`() {
        val compensationInput = CompensationInput(
            id = UUID.randomUUID(),
            companyId = 1,
            entityId = 2,
            contractId = 3,
            schemaItemId = UUID.randomUUID(),
            category = "Base Pay",
            currency = "USD",
            billingRateType = com.multiplier.compensation.domain.common.BillingRateType.VALUE,
            billingRate = 100.0,
            billingFrequency = com.multiplier.compensation.domain.common.BillingFrequency.MONTHLY,
            payScheduleId = UUID.randomUUID(),
            startDate = LocalDate.of(2025, 1, 1),
            endDate = LocalDate.of(2025, 12, 31),
            isInstallment = true,
            noOfInstallments = 12,
            reasonCode = ReasonCode.SC001,
            requestType = RequestType.COMPENSATION_SETUP,
            requestId = UUID.randomUUID().toString(),
            status = CompensationInputStatus.ACTIVATED,
            payrollStatus = PayrollStatus.STAGED,
            createdBy = -1,
            updatedBy = -1,
        )

        val result = compensationInput.toRecord()

        assertEquals(compensationInput.id, result.id)
        assertEquals(compensationInput.companyId, result.companyId)
        assertEquals(compensationInput.entityId, result.entityId)
        assertEquals(compensationInput.contractId, result.contractId)
        assertEquals(compensationInput.schemaItemId, result.schemaItemId)
        assertEquals(compensationInput.category, result.category)
        assertEquals(compensationInput.currency, result.currency)
        assertEquals(compensationInput.billingRateType.toDatabase(), result.billingRateType)
        assertEquals(compensationInput.billingRate, result.billingRate)
        assertEquals(compensationInput.billingFrequency.toDatabase(), result.billingFrequency)
        assertEquals(compensationInput.payScheduleId, result.payScheduleId)
        assertEquals(compensationInput.startDate, result.startDate)
        assertEquals(compensationInput.endDate, result.endDate)
        assertEquals(compensationInput.isInstallment, result.isInstallment)
        assertEquals(compensationInput.noOfInstallments, result.noOfInstallments)
        assertEquals(compensationInput.requestType.toString(), result.requestType)
        assertEquals(compensationInput.requestId, result.requestId)
        assertEquals(compensationInput.status.toString(), result.status)
        assertEquals(compensationInput.createdOn, result.createdOn)
        assertEquals(compensationInput.createdBy, result.createdBy)
        assertEquals(compensationInput.updatedOn, result.updatedOn)
        assertEquals(compensationInput.updatedBy, result.updatedBy)
    }

    @Test
    fun `test toRecord with null fields`() {
        val compensationInput = CompensationInput(
            id = UUID.randomUUID(),
            companyId = 1,
            entityId = 2,
            contractId = 3,
            schemaItemId = UUID.randomUUID(),
            category = "Bonus Pay",
            currency = "EUR",
            billingRateType = com.multiplier.compensation.domain.common.BillingRateType.VALUE,
            billingRate = 200.0,
            billingFrequency = com.multiplier.compensation.domain.common.BillingFrequency.WEEKLY,
            payScheduleId = UUID.randomUUID(),
            startDate = LocalDate.of(2025, 2, 1),
            endDate = null,
            isInstallment = false,
            noOfInstallments = null,
            requestType = RequestType.COMPENSATION_SETUP,
            requestId = UUID.randomUUID().toString(),
            status = CompensationInputStatus.ACTIVATED,
            payrollCutoffDate = null,
            payrollStatus = null,
            lifecycle = null,
            sourceEffectiveDate = null,
            createdBy = -1,
            updatedBy = -1,
        )

        val result = compensationInput.toRecord()

        assertEquals(compensationInput.id, result.id)
        assertEquals(compensationInput.companyId, result.companyId)
        assertEquals(compensationInput.entityId, result.entityId)
        assertEquals(compensationInput.contractId, result.contractId)
        assertEquals(compensationInput.schemaItemId, result.schemaItemId)
        assertEquals(compensationInput.category, result.category)
        assertEquals(compensationInput.currency, result.currency)
        assertEquals(compensationInput.billingRateType.toDatabase(), result.billingRateType)
        assertEquals(compensationInput.billingRate, result.billingRate)
        assertEquals(compensationInput.billingFrequency.toDatabase(), result.billingFrequency)
        assertEquals(compensationInput.payScheduleId, result.payScheduleId)
        assertEquals(compensationInput.startDate, result.startDate)
        assertNull(result.endDate)
        assertEquals(compensationInput.isInstallment, result.isInstallment)
        assertNull(result.noOfInstallments)
        assertEquals(compensationInput.requestType.toString(), result.requestType)
        assertEquals(compensationInput.requestId, result.requestId)
        assertEquals(compensationInput.status.toString(), result.status)
        assertNull(result.payrollCutOffDate)
        assertNull(result.payrollStatus)
        assertNull(result.lifecycle)
        assertNull(result.sourceEffectiveDate)
        assertEquals(compensationInput.createdOn, result.createdOn)
        assertEquals(compensationInput.createdBy, result.createdBy)
        assertEquals(compensationInput.updatedOn, result.updatedOn)
        assertEquals(compensationInput.updatedBy, result.updatedBy)
    }
}
