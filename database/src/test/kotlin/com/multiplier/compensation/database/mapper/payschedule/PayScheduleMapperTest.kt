package com.multiplier.compensation.database.mapper.payschedule

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isFalse
import assertk.assertions.isNotNull
import assertk.assertions.isNull
import com.multiplier.compensation.database.tables.records.PayScheduleRecord
import com.multiplier.compensation.database.tables.references.PAY_SCHEDULE
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.transaction.database.jooq.audit.Audit
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import com.multiplier.compensation.database.enums.ConfigurationScope as DbConfigurationScope
import com.multiplier.compensation.database.enums.CountryCode as DbCountryCode
import com.multiplier.compensation.database.enums.PayDateReference as DbPayDateReferenceType
import com.multiplier.compensation.database.enums.PayScheduleFrequency as DbPayScheduleFrequency

class PayScheduleMapperTest {
    private val testUUID: UUID = UUID.fromString("123e4567-e89b-12d3-a456-************")
    private val testEntityId: Long = 1L
    private val testCompanyId: Long = 2L
    private val testName: String = "Monthly Test Schedule"
    private val testFrequency: PayScheduleFrequency = PayScheduleFrequency.MONTHLY
    private val testDbFrequency: DbPayScheduleFrequency = DbPayScheduleFrequency.MONTHLY
    private val testConfigurationScope: ConfigurationScope = ConfigurationScope.COMPANY
    private val testDbConfigurationScope: DbConfigurationScope = DbConfigurationScope.COMPANY
    private val testCountry: CountryCode = CountryCode.USA
    private val testDbCountry: DbCountryCode = DbCountryCode.USA
    private val testStartDate: LocalDate = LocalDate.of(2024, 1, 1)
    private val testEndDate: LocalDate = LocalDate.of(2024, 12, 31)
    private val testRelativePayDays: Long = -5L
    private val testPayDateReference: PayDateReference = PayDateReference.PAY_SCHEDULE_END_DATE
    private val testDbPayDateReference: DbPayDateReferenceType = DbPayDateReferenceType.PAY_SCHEDULE_END_DATE
    private val testIsInstallment: Boolean = false
    private val testIsActive: Boolean = true
    private val testLabel: String = "Test Label"
    private val testCreatedOn: LocalDateTime = LocalDateTime.of(2024, 1, 1, 10, 0)
    private val testCreatedBy: Long = 100L
    private val testUpdatedOn: LocalDateTime = LocalDateTime.of(2024, 2, 1, 11, 0)
    private val testUpdatedBy: Long = 101L

    private val mockAudit = Audit(
        userId = 99L,
        time = LocalDateTime.of(2024, 3, 1, 12, 0),
        revision = UUID.fromString("789e4567-e89b-12d3-a456-************"),
    )

    @Test
    fun `should map PaySchedule domain to PayScheduleRecord database`() {
        val domain = PaySchedule(
            id = testUUID,
            entityId = testEntityId,
            companyId = testCompanyId,
            name = testName,
            frequency = testFrequency,
            configurationScope = testConfigurationScope,
            country = testCountry,
            startDateReference = testStartDate,
            endDateReference = testEndDate,
            relativePayDays = testRelativePayDays,
            payDateReferenceType = testPayDateReference,
            isInstallment = testIsInstallment,
            isActive = testIsActive,
            label = testLabel,
            createdOn = testCreatedOn,
            createdBy = testCreatedBy,
            updatedOn = testUpdatedOn,
            updatedBy = testUpdatedBy,
        )

        val record = domain.toDatabase()

        assertThat(record.id).isEqualTo(testUUID)
        assertThat(record.entityId).isEqualTo(testEntityId)
        assertThat(record.companyId).isEqualTo(testCompanyId)
        assertThat(record.payScheduleName).isEqualTo(testName)
        assertThat(record.frequency).isEqualTo(testDbFrequency)
        assertThat(record.configurationScope).isEqualTo(testDbConfigurationScope)
        assertThat(record.country).isEqualTo(testDbCountry)
        assertThat(record.startDateReference).isEqualTo(testStartDate)
        assertThat(record.endDateReference).isEqualTo(testEndDate)
        assertThat(record.relativePayDays).isEqualTo(testRelativePayDays)
        assertThat(record.payDateReferenceType).isEqualTo(testDbPayDateReference)
        assertThat(record.isInstallment).isEqualTo(testIsInstallment)
        assertThat(record.isActive).isEqualTo(testIsActive)
        assertThat(record.label).isEqualTo(testLabel)
        assertThat(record.createdOn).isEqualTo(testCreatedOn)
        assertThat(record.createdBy).isEqualTo(testCreatedBy)
        assertThat(record.updatedOn).isEqualTo(testUpdatedOn)
        assertThat(record.updatedBy).isEqualTo(testUpdatedBy)
    }

    @Test
    fun `should map PaySchedule domain to PayScheduleRecord database with null country`() {
        val domain = PaySchedule(
            id = testUUID,
            entityId = testEntityId,
            companyId = testCompanyId,
            name = testName,
            frequency = testFrequency,
            configurationScope = testConfigurationScope,
            country = null, // Null country
            startDateReference = testStartDate,
            endDateReference = testEndDate,
            relativePayDays = testRelativePayDays,
            payDateReferenceType = testPayDateReference,
            isInstallment = testIsInstallment,
            isActive = testIsActive,
            label = testLabel,
            createdOn = testCreatedOn,
            createdBy = testCreatedBy,
            updatedOn = testUpdatedOn,
            updatedBy = testUpdatedBy,
        )

        val record = domain.toDatabase()

        assertThat(record.id).isEqualTo(testUUID)
        assertThat(record.entityId).isEqualTo(testEntityId)
        assertThat(record.companyId).isEqualTo(testCompanyId)
        assertThat(record.payScheduleName).isEqualTo(testName)
        assertThat(record.frequency).isEqualTo(testDbFrequency)
        assertThat(record.configurationScope).isEqualTo(testDbConfigurationScope)
        assertThat(record.country).isNull() // Verify null country
        assertThat(record.startDateReference).isEqualTo(testStartDate)
        assertThat(record.endDateReference).isEqualTo(testEndDate)
        assertThat(record.relativePayDays).isEqualTo(testRelativePayDays)
        assertThat(record.payDateReferenceType).isEqualTo(testDbPayDateReference)
        assertThat(record.isInstallment).isEqualTo(testIsInstallment)
        assertThat(record.isActive).isEqualTo(testIsActive)
        assertThat(record.label).isEqualTo(testLabel)
        assertThat(record.createdOn).isEqualTo(testCreatedOn)
        assertThat(record.createdBy).isEqualTo(testCreatedBy)
        assertThat(record.updatedOn).isEqualTo(testUpdatedOn)
        assertThat(record.updatedBy).isEqualTo(testUpdatedBy)
    }

    @Test
    fun `should map PayScheduleRecord database to PaySchedule domain`() {
        val record = PayScheduleRecord(
            id = testUUID,
            entityId = testEntityId,
            companyId = testCompanyId,
            payScheduleName = testName,
            frequency = testDbFrequency,
            configurationScope = testDbConfigurationScope,
            country = testDbCountry,
            startDateReference = testStartDate,
            endDateReference = testEndDate,
            relativePayDays = testRelativePayDays,
            payDateReferenceType = testDbPayDateReference,
            isInstallment = testIsInstallment,
            isActive = testIsActive,
            label = testLabel,
            createdOn = testCreatedOn,
            createdBy = testCreatedBy,
            updatedOn = testUpdatedOn,
            updatedBy = testUpdatedBy,
        )

        val domain = record.toDomain()

        assertThat(domain.id).isEqualTo(testUUID)
        assertThat(domain.entityId).isEqualTo(testEntityId)
        assertThat(domain.companyId).isEqualTo(testCompanyId)
        assertThat(domain.name).isEqualTo(testName)
        assertThat(domain.frequency).isEqualTo(testFrequency)
        assertThat(domain.configurationScope).isEqualTo(testConfigurationScope)
        assertThat(domain.country).isEqualTo(testCountry)
        assertThat(domain.startDateReference).isEqualTo(testStartDate)
        assertThat(domain.endDateReference).isEqualTo(testEndDate)
        assertThat(domain.relativePayDays).isEqualTo(testRelativePayDays)
        assertThat(domain.payDateReferenceType).isEqualTo(testPayDateReference)
        assertThat(domain.isInstallment).isEqualTo(testIsInstallment)
        assertThat(domain.isActive).isEqualTo(testIsActive)
        assertThat(domain.label).isEqualTo(testLabel)
        assertThat(domain.createdOn).isEqualTo(testCreatedOn)
        assertThat(domain.createdBy).isEqualTo(testCreatedBy)
        assertThat(domain.updatedOn).isEqualTo(testUpdatedOn)
        assertThat(domain.updatedBy).isEqualTo(testUpdatedBy)
    }

    @Test
    fun `should map PayScheduleRecord database to PaySchedule domain with null country`() {
        val record = PayScheduleRecord(
            id = testUUID,
            entityId = testEntityId,
            companyId = testCompanyId,
            payScheduleName = testName,
            frequency = testDbFrequency,
            configurationScope = testDbConfigurationScope,
            country = null, // Null country
            startDateReference = testStartDate,
            endDateReference = testEndDate,
            relativePayDays = testRelativePayDays,
            payDateReferenceType = testDbPayDateReference,
            isInstallment = testIsInstallment,
            isActive = testIsActive,
            label = testLabel,
            createdOn = testCreatedOn,
            createdBy = testCreatedBy,
            updatedOn = testUpdatedOn,
            updatedBy = testUpdatedBy,
        )

        val domain = record.toDomain()

        assertThat(domain.id).isEqualTo(testUUID)
        assertThat(domain.entityId).isEqualTo(testEntityId)
        assertThat(domain.companyId).isEqualTo(testCompanyId)
        assertThat(domain.name).isEqualTo(testName)
        assertThat(domain.frequency).isEqualTo(testFrequency)
        assertThat(domain.configurationScope).isEqualTo(testConfigurationScope)
        assertThat(domain.country).isNull() // Verify null country
        assertThat(domain.startDateReference).isEqualTo(testStartDate)
        assertThat(domain.endDateReference).isEqualTo(testEndDate)
        assertThat(domain.relativePayDays).isEqualTo(testRelativePayDays)
        assertThat(domain.payDateReferenceType).isEqualTo(testPayDateReference)
        assertThat(domain.isInstallment).isEqualTo(testIsInstallment)
        assertThat(domain.isActive).isEqualTo(testIsActive)
        assertThat(domain.label).isEqualTo(testLabel)
        assertThat(domain.createdOn).isEqualTo(testCreatedOn)
        assertThat(domain.createdBy).isEqualTo(testCreatedBy)
        assertThat(domain.updatedOn).isEqualTo(testUpdatedOn)
        assertThat(domain.updatedBy).isEqualTo(testUpdatedBy)
    }

    @Test
    fun `should map PayScheduleRecord to PayScheduleAudRecord`() {
        val record = PayScheduleRecord(
            id = testUUID,
            entityId = testEntityId,
            companyId = testCompanyId,
            payScheduleName = testName,
            frequency = testDbFrequency,
            configurationScope = testDbConfigurationScope, // Included in record, not in Aud
            country = testDbCountry, // Included in record, not in Aud
            startDateReference = testStartDate,
            endDateReference = testEndDate,
            relativePayDays = testRelativePayDays,
            payDateReferenceType = testDbPayDateReference,
            isInstallment = testIsInstallment,
            isActive = testIsActive,
            label = testLabel,
            createdOn = testCreatedOn,
            createdBy = testCreatedBy,
            updatedOn = testUpdatedOn, // Will be overwritten by audit time
            updatedBy = testUpdatedBy, // Will be overwritten by audit user ID
        )

        val auditRecord = record.toAudit(mockAudit)

        assertThat(auditRecord).isNotNull()
        assertThat(auditRecord.id).isEqualTo(testUUID)
        assertThat(auditRecord.rev).isEqualTo(mockAudit.revision) // From Audit
        assertThat(auditRecord.entityId).isEqualTo(testEntityId)
        assertThat(auditRecord.companyId).isEqualTo(testCompanyId)
        assertThat(auditRecord.payScheduleName).isEqualTo(testName)
        assertThat(auditRecord.frequency).isEqualTo(testDbFrequency)
        // configurationScope and country are not in PayScheduleAudRecord
        assertThat(auditRecord.startDateReference).isEqualTo(testStartDate)
        assertThat(auditRecord.endDateReference).isEqualTo(testEndDate)
        assertThat(auditRecord.relativePayDays).isEqualTo(testRelativePayDays)
        assertThat(auditRecord.payDateReferenceType).isEqualTo(testDbPayDateReference)
        assertThat(auditRecord.isInstallment).isEqualTo(testIsInstallment)
        assertThat(auditRecord.isActive).isEqualTo(testIsActive)
        assertThat(auditRecord.label).isEqualTo(testLabel)
        assertThat(auditRecord.createdOn).isEqualTo(testCreatedOn)
        assertThat(auditRecord.createdBy).isEqualTo(testCreatedBy)
        assertThat(auditRecord.updatedOn).isEqualTo(mockAudit.time) // From Audit
        assertThat(auditRecord.updatedBy).isEqualTo(mockAudit.userId) // From Audit
    }

    @Test
    fun `should apply audit details to PayScheduleRecord`() {
        val record = spyk(
            PayScheduleRecord(
                id = testUUID,
                entityId = testEntityId,
                companyId = testCompanyId,
                payScheduleName = testName,
                frequency = testDbFrequency,
                configurationScope = testDbConfigurationScope,
                country = testDbCountry,
                startDateReference = testStartDate,
                endDateReference = testEndDate,
                relativePayDays = testRelativePayDays,
                payDateReferenceType = testDbPayDateReference,
                isInstallment = testIsInstallment,
                isActive = testIsActive,
                label = testLabel,
                createdOn = testCreatedOn,
                createdBy = testCreatedBy,
                updatedOn = testUpdatedOn,
                updatedBy = testUpdatedBy,
            ),
        )

        val result = record.applyAudit(mockAudit)

        assertThat(result).isEqualTo(record) // Should return the same instance

        verify {
            record.createdBy = mockAudit.userId
            record.createdOn = mockAudit.time
            record.updatedBy = mockAudit.userId
            record.updatedOn = mockAudit.time
        }

        assertThat(record.createdBy).isEqualTo(mockAudit.userId)
        assertThat(record.createdOn).isEqualTo(mockAudit.time)
        assertThat(record.updatedBy).isEqualTo(mockAudit.userId)
        assertThat(record.updatedOn).isEqualTo(mockAudit.time)
    }

    @Test
    fun `should apply update only changes to PayScheduleRecord`() {
        val record = mockk<PayScheduleRecord>(relaxed = true)

        val result = record.applyUpdateOnly()

        assertThat(result).isEqualTo(record) // Should return the same instance

        verify { record.changed(PAY_SCHEDULE.ID, false) }
        verify { record.changed(PAY_SCHEDULE.ENTITY_ID, false) }
        verify { record.changed(PAY_SCHEDULE.COMPANY_ID, false) }
        verify { record.changed(PAY_SCHEDULE.COUNTRY, false) }
        verify { record.changed(PAY_SCHEDULE.CREATED_ON, false) }
        verify { record.changed(PAY_SCHEDULE.CREATED_BY, false) }
    }

    @Test
    fun `should apply delete changes (set isActive to false) to PayScheduleRecord`() {
        val record = spyk(
            PayScheduleRecord(
                id = testUUID,
                entityId = testEntityId,
                companyId = testCompanyId,
                payScheduleName = testName,
                frequency = testDbFrequency,
                configurationScope = testDbConfigurationScope,
                country = testDbCountry,
                startDateReference = testStartDate,
                endDateReference = testEndDate,
                relativePayDays = testRelativePayDays,
                payDateReferenceType = testDbPayDateReference,
                isInstallment = testIsInstallment,
                isActive = true, // Start as active
                label = testLabel,
                createdOn = testCreatedOn,
                createdBy = testCreatedBy,
                updatedOn = testUpdatedOn,
                updatedBy = testUpdatedBy,
            ),
        )

        val result = record.applyDelete()

        assertThat(result).isEqualTo(record) // Should return the same instance
        verify { record.isActive = false }
        assertThat(record.isActive).isFalse()
    }
}
