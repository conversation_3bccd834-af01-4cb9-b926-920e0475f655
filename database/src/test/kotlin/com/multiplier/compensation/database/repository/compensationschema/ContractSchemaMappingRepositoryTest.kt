package com.multiplier.compensation.database.repository.compensationschema

import com.fasterxml.uuid.Generators
import com.multiplier.compensation.database.mapper.compensationschema.toDomain
import com.multiplier.compensation.database.mapper.compensationschema.toRecord
import com.multiplier.compensation.database.tables.records.ContractSchemaMappingRecord
import com.multiplier.compensation.database.tables.references.CONTRACT_SCHEMA_MAPPING
import com.multiplier.compensation.domain.compensationschema.ContractSchemaMapping
import com.multiplier.compensation.domain.compensationschema.ContractSchemaMappingReasonCode
import com.multiplier.transaction.database.jooq.audit.Audit
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.jooq.DSLContext
import org.jooq.RecordMapper
import org.jooq.Result
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@ExtendWith(MockKExtension::class)
class ContractSchemaMappingRepositoryTest {
    private lateinit var audit: Audit
    private lateinit var dsl: DSLContext
    private lateinit var transactionContext: TransactionContext
    private lateinit var repository: ContractSchemaMappingRepository
    private val timeBasedEpochGenerator = Generators.timeBasedEpochGenerator()

    @BeforeEach
    fun setUp() {
        audit = mockk()
        dsl = mockk()
        transactionContext = mockk()
        repository = ContractSchemaMappingRepository(dsl)

        every { transactionContext.trx } returns dsl
        every { transactionContext.audit } returns audit
        every { audit.userId } returns -1
        every { audit.revision } returns timeBasedEpochGenerator.generate()
        every { audit.time } returns LocalDateTime.now()
    }

    @Test
    fun `should save all contract-schema mappings`() {
        val mappings = listOf(mockedContractSchemaMapping())

        every {
            transactionContext.trx.batchInsert(any<List<ContractSchemaMappingRecord>>()).execute()
        } returns intArrayOf(1)

        repository.saveAll(mappings, transactionContext)

        verify(exactly = 1) {
            transactionContext.trx.batchInsert(any<List<ContractSchemaMappingRecord>>()).execute()
        }
    }

    @Test
    fun `should return mapping for a given date`() {
        val contractId = 1L
        val date = LocalDate.of(2024, 3, 25)
        val expected = mockedContractSchemaMapping()
        val record = expected.toRecord()

        every {
            dsl.selectFrom(CONTRACT_SCHEMA_MAPPING)
                .where(CONTRACT_SCHEMA_MAPPING.CONTRACT_ID.eq(contractId))
                .and(CONTRACT_SCHEMA_MAPPING.EFFECTIVE_START_DATE.le(date))
                .and(
                    CONTRACT_SCHEMA_MAPPING.EFFECTIVE_END_DATE.isNull
                        .or(CONTRACT_SCHEMA_MAPPING.EFFECTIVE_END_DATE.ge(date)),
                )
                .fetchOne()
        } returns record

        val result = repository.findMappingForDate(contractId, date)

        assertEquals(expected, result)
    }

    @Test
    fun `should return null when mapping is not found for date`() {
        val contractId = 1L
        val date = LocalDate.of(2024, 3, 25)

        every {
            dsl.selectFrom(CONTRACT_SCHEMA_MAPPING)
                .where(CONTRACT_SCHEMA_MAPPING.CONTRACT_ID.eq(contractId))
                .and(CONTRACT_SCHEMA_MAPPING.EFFECTIVE_START_DATE.le(date))
                .and(
                    CONTRACT_SCHEMA_MAPPING.EFFECTIVE_END_DATE.isNull
                        .or(CONTRACT_SCHEMA_MAPPING.EFFECTIVE_END_DATE.ge(date)),
                )
                .fetchOne()
        } returns null

        val result = repository.findMappingForDate(contractId, date)

        assertNull(result)
    }

    @Test
    fun `should find overlapping mappings with explicit end date`() {
        val contractId = 1L
        val start = LocalDate.of(2024, 1, 1)
        val end = LocalDate.of(2024, 12, 31)

        val record = mockedContractSchemaMapping().toRecord()
        val expected = record.toDomain()

        val resultSet = mockk<Result<ContractSchemaMappingRecord>>()
        every {
            resultSet.map(match<RecordMapper<ContractSchemaMappingRecord, ContractSchemaMapping>> { true })
        } returns listOf(expected)

        every {
            dsl.selectFrom(CONTRACT_SCHEMA_MAPPING)
                .where(CONTRACT_SCHEMA_MAPPING.CONTRACT_ID.eq(contractId))
                .and(CONTRACT_SCHEMA_MAPPING.IS_DELETED.isFalse)
                .and(
                    CONTRACT_SCHEMA_MAPPING.EFFECTIVE_START_DATE.le(end)
                        .and(
                            CONTRACT_SCHEMA_MAPPING.EFFECTIVE_END_DATE.isNull
                                .or(CONTRACT_SCHEMA_MAPPING.EFFECTIVE_END_DATE.ge(start)),
                        ),
                )
                .fetch()
        } returns resultSet

        val result = repository.findMappingsForPeriod(contractId, start, end, transactionContext)

        assertEquals(1, result.size)
        assertEquals(expected, result.first())
    }

    @Test
    fun `should find overlapping mappings with null end date`() {
        val contractId = 1L
        val start = LocalDate.of(2024, 1, 1)
        val end: LocalDate? = null

        val record = mockedContractSchemaMapping().toRecord()
        val expected = record.toDomain()

        val resultSet = mockk<Result<ContractSchemaMappingRecord>>()
        every {
            resultSet.map(match<RecordMapper<ContractSchemaMappingRecord, ContractSchemaMapping>> { true })
        } returns listOf(expected)

        every {
            dsl.selectFrom(CONTRACT_SCHEMA_MAPPING)
                .where(CONTRACT_SCHEMA_MAPPING.CONTRACT_ID.eq(contractId))
                .and(CONTRACT_SCHEMA_MAPPING.IS_DELETED.isFalse)
                .and(
                    CONTRACT_SCHEMA_MAPPING.EFFECTIVE_START_DATE.le(LocalDate.of(9999, 12, 31))
                        .and(
                            CONTRACT_SCHEMA_MAPPING.EFFECTIVE_END_DATE.isNull
                                .or(CONTRACT_SCHEMA_MAPPING.EFFECTIVE_END_DATE.ge(start)),
                        ),
                )
                .fetch()
        } returns resultSet

        val result = repository.findMappingsForPeriod(contractId, start, end, transactionContext)

        assertEquals(1, result.size)
        assertEquals(expected, result.first())
    }

    @Test
    fun `should find mappings for a period`() {
        val contractId = 1L
        val start = LocalDate.of(2024, 1, 1)
        val end = LocalDate.of(2024, 12, 31)

        val record = mockedContractSchemaMapping().toRecord()
        val expected = record.toDomain()

        val resultSet = mockk<Result<ContractSchemaMappingRecord>>()
        every {
            resultSet.map(match<RecordMapper<ContractSchemaMappingRecord, ContractSchemaMapping>> { true })
        } returns listOf(expected)

        every {
            dsl.selectFrom(CONTRACT_SCHEMA_MAPPING)
                .where(CONTRACT_SCHEMA_MAPPING.CONTRACT_ID.eq(contractId))
                .and(CONTRACT_SCHEMA_MAPPING.IS_DELETED.isFalse)
                .and(
                    CONTRACT_SCHEMA_MAPPING.EFFECTIVE_START_DATE.le(end)
                        .and(
                            CONTRACT_SCHEMA_MAPPING.EFFECTIVE_END_DATE.isNull
                                .or(CONTRACT_SCHEMA_MAPPING.EFFECTIVE_END_DATE.ge(start)),
                        ),
                )
                .fetch()
        } returns resultSet

        val result = repository.findMappingsForPeriod(contractId, start, end, transactionContext)

        assertEquals(1, result.size)
        assertEquals(expected, result.first())
    }

    private fun mockedContractSchemaMapping(): ContractSchemaMapping = ContractSchemaMapping(
        id = timeBasedEpochGenerator.generate(),
        contractId = 1L,
        schemaId = UUID.randomUUID(),
        effectiveStartDate = LocalDate.of(2024, 1, 1),
        effectiveEndDate = LocalDate.of(2024, 12, 31),
        reasonCode = ContractSchemaMappingReasonCode.SC001,
        isDeleted = false,
        createdOn = LocalDateTime.now(),
        createdBy = 1L,
        updatedOn = LocalDateTime.now(),
        updatedBy = 1L,
    )
}
