package com.multiplier.compensation.database.repository.notification

import com.multiplier.compensation.database.tables.records.VariablePayNotificationLogRecord
import com.multiplier.compensation.database.tables.references.VARIABLE_PAY_NOTIFICATION_LOG
import com.multiplier.compensation.domain.notification.VariablePayNotificationLogDetails
import com.multiplier.transaction.database.jooq.audit.Audit
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.jooq.Batch
import org.jooq.DSLContext
import org.jooq.Record1
import org.jooq.Result
import org.jooq.SelectConditionStep
import org.jooq.SelectJoinStep
import org.jooq.SelectSelectStep
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import java.time.LocalDateTime

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class VariablePayNotificationLogRepositoryTest {
    private lateinit var repository: VariablePayNotificationLogRepository
    private lateinit var mockDsl: DSLContext
    private lateinit var mockTrx: DSLContext
    private lateinit var transactionContext: TransactionContext
    private lateinit var mockAudit: Audit

    @BeforeEach
    fun setup() {
        mockDsl = mockk(relaxed = true)
        mockTrx = mockk(relaxed = true)
        mockAudit = mockk(relaxed = true)

        transactionContext = TransactionContext(
            trx = mockTrx,
            audit = mockAudit,
        )

        repository = VariablePayNotificationLogRepository(mockDsl)
    }

    @Test
    @Suppress("IgnoredReturnValue")
    fun `findCompaniesNotifiedOnDate should return companies notified on the given date`() {
        // Given
        val date = LocalDate.now()
        val inputCompanyIds = listOf(1L, 2L, 3L)
        val expectedCompanyIds = listOf(1L, 2L) // Only some of the companies were notified on this date

        // Mock the query chain
        val selectStep = mockk<SelectSelectStep<Record1<Long?>>>()
        val fromStep = mockk<SelectJoinStep<Record1<Long?>>>()
        val whereStep = mockk<SelectConditionStep<Record1<Long?>>>()
        val mockResult = mockk<Result<Record1<Long?>>>()

        // Set up the mocks
        coEvery { mockDsl.select(VARIABLE_PAY_NOTIFICATION_LOG.COMPANY_ID) } returns selectStep
        coEvery { selectStep.from(VARIABLE_PAY_NOTIFICATION_LOG) } returns fromStep
        coEvery {
            fromStep.where(
                VARIABLE_PAY_NOTIFICATION_LOG.LAST_NOTIFICATION_DATE.eq(date)
                    .and(VARIABLE_PAY_NOTIFICATION_LOG.COMPANY_ID.`in`(inputCompanyIds)),
            )
        } returns whereStep
        coEvery { whereStep.fetch() } returns mockResult

        // Mock the map function to return the expected company IDs
        coEvery { mockResult.map<Long>(any()) } returns expectedCompanyIds

        // When
        val result = repository.findCompaniesNotifiedOnDate(inputCompanyIds, date)

        // Then
        assertEquals(expectedCompanyIds, result)

        // Verify
        verify {
            mockDsl.select(VARIABLE_PAY_NOTIFICATION_LOG.COMPANY_ID)
            selectStep.from(VARIABLE_PAY_NOTIFICATION_LOG)
            fromStep.where(
                VARIABLE_PAY_NOTIFICATION_LOG.LAST_NOTIFICATION_DATE.eq(date)
                    .and(VARIABLE_PAY_NOTIFICATION_LOG.COMPANY_ID.`in`(inputCompanyIds)),
            )
            whereStep.fetch()
            mockResult.map<Long>(any())
        }
    }

    @Test
    @Suppress("IgnoredReturnValue")
    fun `saveLog should insert a record with audit fields`() {
        // Given
        val now = LocalDateTime.now()
        val userId = 123L
        val notificationTrackingDetail = createNotificationTracking(1L, LocalDate.now())

        // Setup audit
        coEvery { mockAudit.userId } returns userId
        coEvery { mockAudit.time } returns now

        // Capture the record being set
        val recordSlot = slot<VariablePayNotificationLogRecord>()

        // Mock the insert operation
        val insertStep =
            mockk<org.jooq.InsertSetStep<VariablePayNotificationLogRecord>>()
        val insertSetMoreStep =
            mockk<org.jooq.InsertSetMoreStep<VariablePayNotificationLogRecord>>()

        coEvery { mockTrx.insertInto(VARIABLE_PAY_NOTIFICATION_LOG) } returns insertStep
        coEvery {
            insertStep.set(capture(recordSlot))
        } returns insertSetMoreStep
        coEvery { insertSetMoreStep.execute() } returns 1

        // When
        repository.saveLog(notificationTrackingDetail, transactionContext)

        // Then
        verify {
            mockTrx.insertInto(VARIABLE_PAY_NOTIFICATION_LOG)
            insertStep.set(any<VariablePayNotificationLogRecord>())
            insertSetMoreStep.execute()
        }

        // Verify the record has the correct audit fields
        val capturedRecord = recordSlot.captured
        assertEquals(notificationTrackingDetail.companyId, capturedRecord.companyId)
        assertEquals(notificationTrackingDetail.lastNotificationDate, capturedRecord.lastNotificationDate)
        assertEquals(userId, capturedRecord.createdBy)
        assertEquals(now, capturedRecord.createdOn)
        assertEquals(userId, capturedRecord.updatedBy)
        assertEquals(now, capturedRecord.updatedOn)
    }

    @Test
    @Suppress("IgnoredReturnValue")
    fun `updateLog should update a record with audit fields`() {
        // Given
        val now = LocalDateTime.now()
        val userId = 123L
        val createdOn = LocalDateTime.now().minusDays(1)
        val createdBy = 456L
        val notificationTrackingDetail = createNotificationTracking(
            companyId = 1L,
            lastNotificationDate = LocalDate.now(),
            createdBy = createdBy,
            createdOn = createdOn,
            updatedBy = 789L,
            updatedOn = LocalDateTime.now().minusHours(1),
        )

        // Setup audit
        coEvery { mockAudit.userId } returns userId
        coEvery { mockAudit.time } returns now

        // Capture the record being updated
        val recordSlot = slot<VariablePayNotificationLogRecord>()

        // Setup batch update
        val mockBatch = mockk<Batch>()
        coEvery {
            mockTrx.batchUpdate(
                capture(recordSlot),
            )
        } returns mockBatch
        coEvery { mockBatch.execute() } returns intArrayOf(1)

        // When
        repository.updateLog(notificationTrackingDetail, transactionContext)

        // Then
        verify {
            mockTrx.batchUpdate(
                any<VariablePayNotificationLogRecord>(),
            )
            mockBatch.execute()
        }

        // Verify the record has the correct audit fields
        val capturedRecord = recordSlot.captured
        assertEquals(notificationTrackingDetail.companyId, capturedRecord.companyId)
        assertEquals(notificationTrackingDetail.lastNotificationDate, capturedRecord.lastNotificationDate)
        // For update operations, the createdBy and createdOn fields should remain unchanged
        assertEquals(notificationTrackingDetail.createdBy, capturedRecord.createdBy)
        assertEquals(notificationTrackingDetail.createdOn, capturedRecord.createdOn)
        // Only the updatedBy and updatedOn fields should be updated
        assertEquals(userId, capturedRecord.updatedBy)
        assertEquals(now, capturedRecord.updatedOn)
    }

    @Test
    @Suppress("IgnoredReturnValue")
    fun `isCompanyLogsExist should return true when logs exist for the company`() {
        // Given
        val companyId = 123L
        val selectOneStep = mockk<SelectSelectStep<Record1<Int>>>()
        val fromStep = mockk<SelectJoinStep<Record1<Int>>>()
        val whereStep = mockk<SelectConditionStep<Record1<Int>>>()

        // Set up the mocks
        coEvery { mockDsl.selectOne() } returns selectOneStep
        coEvery { selectOneStep.from(VARIABLE_PAY_NOTIFICATION_LOG) } returns fromStep
        coEvery { fromStep.where(VARIABLE_PAY_NOTIFICATION_LOG.COMPANY_ID.eq(companyId)) } returns whereStep
        coEvery { mockDsl.fetchExists(whereStep) } returns true

        // When
        val result = repository.isCompanyLogsExist(companyId)

        // Then
        assertEquals(true, result)

        // Verify
        verify {
            mockDsl.selectOne()
            selectOneStep.from(VARIABLE_PAY_NOTIFICATION_LOG)
            fromStep.where(VARIABLE_PAY_NOTIFICATION_LOG.COMPANY_ID.eq(companyId))
            mockDsl.fetchExists(whereStep)
        }
    }

    @Test
    @Suppress("IgnoredReturnValue")
    fun `isCompanyLogsExist should return false when no logs exist for the company`() {
        // Given
        val companyId = 456L
        val selectOneStep = mockk<SelectSelectStep<Record1<Int>>>()
        val fromStep = mockk<SelectJoinStep<Record1<Int>>>()
        val whereStep = mockk<SelectConditionStep<Record1<Int>>>()

        // Set up the mocks
        coEvery { mockDsl.selectOne() } returns selectOneStep
        coEvery { selectOneStep.from(VARIABLE_PAY_NOTIFICATION_LOG) } returns fromStep
        coEvery { fromStep.where(VARIABLE_PAY_NOTIFICATION_LOG.COMPANY_ID.eq(companyId)) } returns whereStep
        coEvery { mockDsl.fetchExists(whereStep) } returns false

        // When
        val result = repository.isCompanyLogsExist(companyId)

        // Then
        assertEquals(false, result)

        // Verify
        verify {
            mockDsl.selectOne()
            selectOneStep.from(VARIABLE_PAY_NOTIFICATION_LOG)
            fromStep.where(VARIABLE_PAY_NOTIFICATION_LOG.COMPANY_ID.eq(companyId))
            mockDsl.fetchExists(whereStep)
        }
    }

    private fun createNotificationTracking(
        companyId: Long,
        lastNotificationDate: LocalDate,
        createdBy: Long = -1L,
        createdOn: LocalDateTime = LocalDateTime.now(),
        updatedBy: Long? = null,
        updatedOn: LocalDateTime? = null,
    ) = VariablePayNotificationLogDetails(
        companyId = companyId,
        lastNotificationDate = lastNotificationDate,
        createdBy = createdBy,
        createdOn = createdOn,
        updatedBy = updatedBy,
        updatedOn = updatedOn,
    )
}
