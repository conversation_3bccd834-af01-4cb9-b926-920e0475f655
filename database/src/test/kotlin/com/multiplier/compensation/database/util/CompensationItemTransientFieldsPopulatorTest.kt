package com.multiplier.compensation.database.util

import com.multiplier.compensation.domain.compensationitem.CompensationItemEnriched
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemState
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class CompensationItemTransientFieldsPopulatorTest {
    @Nested
    inner class CalculateStateTests {
        @Test
        fun `calculateState returns state when status is REVOKED`() {
            val compensationItem = mockCompensationItem(status = CompensationItemStatus.REVOKED)

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.REVOKED, result)
        }

        @Test
        fun `calculateState returns state when status is ABORTED`() {
            val compensationItem = mockCompensationItem(status = CompensationItemStatus.ABORTED)

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.ABORTED, result)
        }

        @Test
        fun `calculateState returns PENDING when cutOffDate is null`() {
            val compensationItem = mockCompensationItem(cutOffDate = null)

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.PENDING, result)
        }

        @Test
        fun `calculateState returns PENDING for fixed item when cutOffDate is in future`() {
            val today = LocalDate.now()
            val cutOffDate = today.plusDays(1)
            val compensationItem = mockCompensationItem(
                cutOffDate = cutOffDate,
            )

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.PENDING, result)
        }

        @Test
        fun `calculateState returns PROCESSING for fixed item when cutOffDate is passed but payDate is in future`() {
            val today = LocalDate.now()
            val cutOffDate = today.minusDays(1)
            val payDate = today.plusDays(1)
            val compensationItem = mockCompensationItem(
                cutOffDate = cutOffDate,
                payDate = payDate,
            )

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.PROCESSING, result)
        }

        @Test
        fun `calculateState returns COMPLETED for fixed item when payDate and cutOffDate are in past`() {
            val today = LocalDate.now()
            val cutOffDate = today.minusDays(2)
            val payDate = today.minusDays(1)
            val compensationItem = mockCompensationItem(
                cutOffDate = cutOffDate,
                payDate = payDate,
            )

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.COMPLETED, result)
        }

        @Test
        fun `calculateState returns DRAFT for variable item when status is DRAFT`() {
            val compensationItem = mockCompensationItem(
                status = CompensationItemStatus.DRAFT,
            )

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.DRAFT, result)
        }

        @Test
        fun `calculateState returns PENDING for variable item when cutOffDate is in future`() {
            val today = LocalDate.now()
            val cutOffDate = today.plusDays(1)
            val compensationItem = mockCompensationItem(
                cutOffDate = cutOffDate,
            )

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.PENDING, result)
        }

        @Test
        fun `calculateState returns PROCESSING for variable item when cutOffDate is passed but payDate is in future`() {
            val today = LocalDate.now()
            val cutOffDate = today.minusDays(1)
            val payDate = today.plusDays(1)
            val compensationItem = mockCompensationItem(
                cutOffDate = cutOffDate,
                payDate = payDate,
            )

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.PROCESSING, result)
        }

        @Test
        fun `calculateState returns COMPLETED for variable item when cutOffDate and payDate are in past`() {
            val today = LocalDate.now()
            val cutOffDate = today.minusDays(2)
            val payDate = today.minusDays(1)
            val compensationItem = mockCompensationItem(
                cutOffDate = cutOffDate,
                payDate = payDate,
            )

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.COMPLETED, result)
        }

        @Test
        fun `calculateState returns PENDING when cutOffDate is null for NEW status`() {
            val compensationItem = mockCompensationItem(
                status = CompensationItemStatus.NEW,
                cutOffDate = null,
                payDate = null,
            )

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.PENDING, result)
        }

        // Note: The else branch in calculateState is not reachable with the current logic
        // The method has exhaustive conditions that cover all possible combinations
        // of cutOffDate, payDate, and today. The else branch is a fallback mechanism
        // that would only be triggered if there were logical gaps in the conditions,
        // which there aren't.

        @Test
        fun `calculateState returns PROCESSING when cutOffDate is passed but payDate is null`() {
            val today = LocalDate.now()
            val cutOffDate = today.minusDays(1)
            val compensationItem = mockCompensationItem(
                cutOffDate = cutOffDate,
                payDate = null,
            )

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.PROCESSING, result)
        }

        @Test
        fun `calculateState returns PROCESSING when cutOffDate is passed but payDate is today`() {
            val today = LocalDate.now()
            val cutOffDate = today.minusDays(1)
            val compensationItem = mockCompensationItem(
                cutOffDate = cutOffDate,
                payDate = today,
            )

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.PROCESSING, result)
        }

        @Test
        fun `calculateState returns PROCESSING when cutOffDate and payDate is passed`() {
            val today = LocalDate.now()
            val cutOffDate = today.minusDays(10)
            val payDate = today.minusDays(1)
            val compensationItem = mockCompensationItem(
                cutOffDate = cutOffDate,
                payDate = payDate,
            )

            val result = compensationItem.calculateState()

            assertEquals(CompensationItemState.COMPLETED, result)
        }
    }

    @Nested
    inner class CalculateIsDeletableTests {
        @Test
        fun `calculateIsDeletable returns true when cutOffDate is null`() {
            val compensationItem = mockCompensationItem(cutOffDate = null)

            val result = compensationItem.calculateIsDeletable()

            assertTrue(result)
        }

        @Test
        fun `calculateIsDeletable returns false when cutOffDate is passed`() {
            val today = LocalDate.now()
            val cutOffDate = today.minusDays(1)
            val compensationItem = mockCompensationItem(cutOffDate = cutOffDate)

            val result = compensationItem.calculateIsDeletable()

            assertFalse(result)
        }

        @Test
        fun `calculateIsDeletable returns true when cutOffDate is in future`() {
            val today = LocalDate.now()
            val cutOffDate = today.plusDays(1)
            val compensationItem = mockCompensationItem(cutOffDate = cutOffDate)

            val result = compensationItem.calculateIsDeletable()

            assertTrue(result)
        }

        @Test
        fun `calculateIsDeletable returns true when status is DRAFT`() {
            val compensationItem = mockCompensationItem(
                status = CompensationItemStatus.DRAFT,
                cutOffDate = null, // Drafts don't have cutOffDate
            )

            val result = compensationItem.calculateIsDeletable()

            assertTrue(result)
        }

        @Test
        fun `calculateIsDeletable returns true when cutOffDate is today`() {
            val today = LocalDate.now()
            val compensationItem = mockCompensationItem(
                status = CompensationItemStatus.PROCESSING,
                cutOffDate = today,
            )

            val result = compensationItem.calculateIsDeletable()

            assertTrue(result)
        }

        @Test
        fun `calculateIsDeletable returns false when status is ABORTED`() {
            val compensationItem = mockCompensationItem(
                status = CompensationItemStatus.ABORTED,
            )

            val result = compensationItem.calculateIsDeletable()

            assertFalse(result)
        }

        @Test
        fun `calculateIsDeletable returns false when status is REVOKED`() {
            val compensationItem = mockCompensationItem(
                status = CompensationItemStatus.REVOKED,
            )

            val result = compensationItem.calculateIsDeletable()

            assertFalse(result)
        }
    }

    @Nested
    inner class CalculateIsEditableTests {
        @Test
        fun `calculateIsEditable returns false for fixed item when cutOffDate is null`() {
            // Given - isDeletable would be true in this scenario
            val compensationItem = mockCompensationItem(
                isFixed = true,
                cutOffDate = null,
                status = CompensationItemStatus.NEW,
            )

            // Setup to use calculateIsDeletable instead of mocked isDeletable
            every { compensationItem.isDeletable } answers { compensationItem.calculateIsDeletable() }

            // When
            val result = compensationItem.calculateIsEditable()

            // Then
            assertFalse(result)
            // Verify isDeletable would be true in this scenario
            assertTrue(compensationItem.calculateIsDeletable())
        }

        @Test
        fun `calculateIsEditable returns false for fixed item when cutOffDate is in future`() {
            // Given - isDeletable would be true in this scenario
            val today = LocalDate.now()
            val cutOffDate = today.plusDays(1)
            val compensationItem = mockCompensationItem(
                isFixed = true,
                cutOffDate = cutOffDate,
                status = CompensationItemStatus.NEW,
            )

            // Setup to use calculateIsDeletable instead of mocked isDeletable
            every { compensationItem.isDeletable } answers { compensationItem.calculateIsDeletable() }

            // When
            val result = compensationItem.calculateIsEditable()

            // Then
            assertFalse(result)
            // Verify isDeletable would be true in this scenario
            assertTrue(compensationItem.calculateIsDeletable())
        }

        @Test
        fun `calculateIsEditable returns false for fixed item when cutOffDate is passed`() {
            // Given - isDeletable would be false in this scenario
            val today = LocalDate.now()
            val cutOffDate = today.minusDays(1)
            val compensationItem = mockCompensationItem(
                isFixed = true,
                cutOffDate = cutOffDate,
                status = CompensationItemStatus.NEW,
            )

            // Setup to use calculateIsDeletable instead of mocked isDeletable
            every { compensationItem.isDeletable } answers { compensationItem.calculateIsDeletable() }

            // When
            val result = compensationItem.calculateIsEditable()

            // Then
            assertFalse(result)
            // Verify isDeletable would be false in this scenario
            assertFalse(compensationItem.calculateIsDeletable())
        }

        @Test
        fun `calculateIsEditable returns true for non-fixed item when cutOffDate is null`() {
            // Given - isDeletable would be true in this scenario
            val compensationItem = mockCompensationItem(
                isFixed = false,
                cutOffDate = null,
                status = CompensationItemStatus.NEW,
            )

            // Setup to use calculateIsDeletable instead of mocked isDeletable
            every { compensationItem.isDeletable } answers { compensationItem.calculateIsDeletable() }

            // When
            val result = compensationItem.calculateIsEditable()

            // Then
            assertTrue(result)
            // Verify isDeletable would be true in this scenario
            assertTrue(compensationItem.calculateIsDeletable())
        }

        @Test
        fun `calculateIsEditable returns true for non-fixed item when cutOffDate is in future`() {
            // Given - isDeletable would be true in this scenario
            val today = LocalDate.now()
            val cutOffDate = today.plusDays(1)
            val compensationItem = mockCompensationItem(
                isFixed = false,
                cutOffDate = cutOffDate,
                status = CompensationItemStatus.NEW,
            )

            // Setup to use calculateIsDeletable instead of mocked isDeletable
            every { compensationItem.isDeletable } answers { compensationItem.calculateIsDeletable() }

            // When
            val result = compensationItem.calculateIsEditable()

            // Then
            assertTrue(result)
            // Verify isDeletable would be true in this scenario
            assertTrue(compensationItem.calculateIsDeletable())
        }

        @Test
        fun `calculateIsEditable returns false for non-fixed item when cutOffDate is passed`() {
            // Given - isDeletable would be false in this scenario
            val today = LocalDate.now()
            val cutOffDate = today.minusDays(1)
            val compensationItem = mockCompensationItem(
                isFixed = false,
                cutOffDate = cutOffDate,
                status = CompensationItemStatus.NEW,
            )

            // Setup to use calculateIsDeletable instead of mocked isDeletable
            every { compensationItem.isDeletable } answers { compensationItem.calculateIsDeletable() }

            // When
            val result = compensationItem.calculateIsEditable()

            // Then
            assertFalse(result)
            // Verify isDeletable would be false in this scenario
            assertFalse(compensationItem.calculateIsDeletable())
        }

        @Test
        fun `calculateIsEditable returns false for non-fixed item with REVOKED status`() {
            // Given - isDeletable would be false in this scenario
            val compensationItem = mockCompensationItem(
                isFixed = false,
                status = CompensationItemStatus.REVOKED,
            )

            // Setup to use calculateIsDeletable instead of mocked isDeletable
            every { compensationItem.isDeletable } answers { compensationItem.calculateIsDeletable() }

            // When
            val result = compensationItem.calculateIsEditable()

            // Then
            assertFalse(result)
            // Verify isDeletable would be false in this scenario
            assertFalse(compensationItem.calculateIsDeletable())
        }

        @Test
        fun `calculateIsEditable returns false for non-fixed item with ABORTED status`() {
            // Given - isDeletable would be false in this scenario
            val compensationItem = mockCompensationItem(
                isFixed = false,
                status = CompensationItemStatus.ABORTED,
            )

            // Setup to use calculateIsDeletable instead of mocked isDeletable
            every { compensationItem.isDeletable } answers { compensationItem.calculateIsDeletable() }

            // When
            val result = compensationItem.calculateIsEditable()

            // Then
            assertFalse(result)
            // Verify isDeletable would be false in this scenario
            assertFalse(compensationItem.calculateIsDeletable())
        }
    }

    private fun mockCompensationItem(
        status: CompensationItemStatus = CompensationItemStatus.NEW,
        cutOffDate: LocalDate? = LocalDate.now(),
        payDate: LocalDate? = LocalDate.now().plusDays(10),
        isFixed: Boolean = false,
        state: CompensationItemState = CompensationItemState.PENDING,
        isDeletable: Boolean = false,
    ): CompensationItemEnriched {
        val compensationItem = mockk<CompensationItemEnriched>()
        every { compensationItem.status } returns status
        every { compensationItem.cutOffDate } returns cutOffDate
        every { compensationItem.payDate } returns payDate
        every { compensationItem.isFixed } returns isFixed
        every { compensationItem.state } returns state
        every { compensationItem.isDeletable } returns isDeletable
        return compensationItem
    }
}
