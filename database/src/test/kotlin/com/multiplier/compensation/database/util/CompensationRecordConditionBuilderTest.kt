package com.multiplier.compensation.database.util

import com.multiplier.compensation.database.enums.CompensationStatus
import com.multiplier.compensation.database.tables.references.COMPENSATION_
import com.multiplier.compensation.domain.compensation.CompensationRecordsFilter
import com.multiplier.compensation.domain.compensation.CompensationRecordsGrpcFilter
import com.multiplier.compensation.domain.compensation.enums.CompensationState
import org.jooq.Condition
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.util.UUID

class CompensationRecordConditionBuilderTest {
    private val testEntityId = 123L
    private val testCompanyId = 456L
    private val testContractId1 = 789L
    private val testContractId2 = 987L
    private val testCategory1 = "SALARY"
    private val testCategory2 = "BONUS"
    private val testStartDate = LocalDate.of(2024, 1, 1)
    private val testEndDate = LocalDate.of(2024, 12, 31)
    private val testActiveAsOnDate = LocalDate.of(2024, 6, 15)
    private val testUuid1 = UUID.randomUUID().toString()
    private val testUuid2 = UUID.randomUUID().toString()
    private val today = LocalDate.now()
    private val todayStartOfDay = today.atStartOfDay()

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - empty filter - returns empty list`() {
        val filter = CompensationRecordsFilter()
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertTrue(conditions.isEmpty())
    }

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - with entityId - adds entityId condition`() {
        val filter = CompensationRecordsFilter(entityId = testEntityId)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        assertConditionEquals(conditions[0], COMPENSATION_.ENTITY_ID.eq(testEntityId))
    }

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - with companyId - adds companyId condition`() {
        val filter = CompensationRecordsFilter(companyId = testCompanyId)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        assertConditionEquals(conditions[0], COMPENSATION_.COMPANY_ID.eq(testCompanyId))
    }

    @Test
    fun `buildCompensationRecordFilterCondition - with non-empty contractIds - adds contractIds condition`() {
        val contractIds = listOf(testContractId1, testContractId2)
        val filter = CompensationRecordsFilter(contractIds = contractIds)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        assertConditionEquals(conditions[0], COMPENSATION_.CONTRACT_ID.`in`(contractIds))
    }

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - with empty contractIds - adds no contractIds condition`() {
        val filter = CompensationRecordsFilter(contractIds = emptyList())
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertTrue(conditions.none { it.toString().contains(COMPENSATION_.CONTRACT_ID.name) })
    }

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - with null contractIds - adds no contractIds condition`() {
        val filter = CompensationRecordsFilter(contractIds = null)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertTrue(conditions.none { it.toString().contains(COMPENSATION_.CONTRACT_ID.name) })
    }

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - with non-empty categories - adds categories condition`() {
        val categories = listOf(testCategory1, testCategory2)
        val filter = CompensationRecordsFilter(categories = categories)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        assertConditionEquals(conditions[0], COMPENSATION_.CATEGORY.`in`(categories))
    }

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - with empty categories - adds no categories condition`() {
        val filter = CompensationRecordsFilter(categories = emptyList())
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertTrue(conditions.none { it.toString().contains(COMPENSATION_.CATEGORY.name) })
    }

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - with null categories - adds no categories condition`() {
        val filter = CompensationRecordsFilter(categories = null)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertTrue(conditions.none { it.toString().contains(COMPENSATION_.CATEGORY.name) })
    }

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - with startDate - adds startDate condition`() {
        val filter = CompensationRecordsFilter(startDate = testStartDate)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        assertConditionEquals(conditions[0], COMPENSATION_.START_DATE.ge(testStartDate))
    }

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - with endDate - adds endDate condition`() {
        val filter = CompensationRecordsFilter(endDate = testEndDate)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        assertConditionEquals(conditions[0], COMPENSATION_.END_DATE.le(testEndDate))
    }

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - with state COMPLETED - adds completed condition`() {
        val filter = CompensationRecordsFilter(state = CompensationState.COMPLETED)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        val expected = COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED).and(
            COMPENSATION_.PROCESSING_TO.isNotNull.and(
                COMPENSATION_.PROCESSING_TO.lessThan(todayStartOfDay),
            ),
        )
        assertConditionEquals(conditions[0], expected)
    }

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - with state UPCOMING - adds upcoming condition`() {
        val filter = CompensationRecordsFilter(state = CompensationState.UPCOMING)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        val expected = COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED).and(
            COMPENSATION_.PROCESSING_FROM.isNull.or(
                COMPENSATION_.PROCESSING_FROM.greaterOrEqual(todayStartOfDay),
            ),
        )
        assertConditionEquals(conditions[0], expected)
    }

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - with state PROCESSING - adds processing condition`() {
        val filter = CompensationRecordsFilter(state = CompensationState.PROCESSING)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        val expected = COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED).and(
            COMPENSATION_.PROCESSING_FROM.isNotNull.and(
                COMPENSATION_.PROCESSING_FROM.lessThan(todayStartOfDay),
            )
                .and(
                    COMPENSATION_.PROCESSING_TO.isNull.or(
                        COMPENSATION_.PROCESSING_TO.greaterOrEqual(todayStartOfDay),
                    ),
                ),
        )
        assertConditionEquals(conditions[0], expected)
    }

    @Test
    fun `buildCompensationRecordFilterCondition - with state ACTIVE specific date - adds active conditions`() {
        val filter = CompensationRecordsFilter(state = CompensationState.ACTIVE, activeAsOn = testActiveAsOnDate)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(2, conditions.size)

        // Check base active condition uses the specific date
        val expectedBase = COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED)
            .and(
                COMPENSATION_.PROCESSING_TO.isNull.or(
                    COMPENSATION_.PROCESSING_TO.greaterOrEqual(testActiveAsOnDate.atStartOfDay()),
                ),
            )
        assertConditionEquals(conditions[0], expectedBase)

        // Check unique schema item condition uses the specific date
        assertConditionContains(conditions[1], "\"COMPENSATION\".\"COMPENSATION\".\"ID\" in")
    }

    @Test
    fun `buildCompensationRecordFilterCondition(RecFilter) - with multiple fields - adds all conditions`() {
        val contractIds = listOf(testContractId1)
        val categories = listOf(testCategory1)
        val filter = CompensationRecordsFilter(
            entityId = testEntityId,
            companyId = testCompanyId,
            contractIds = contractIds,
            categories = categories,
            startDate = testStartDate,
            endDate = testEndDate,
            state = CompensationState.UPCOMING, // Add a state to ensure it mixes
        )
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(7, conditions.size)
        assertConditionEquals(conditions[0], COMPENSATION_.ENTITY_ID.eq(testEntityId))
        assertConditionEquals(conditions[1], COMPENSATION_.COMPANY_ID.eq(testCompanyId))
        assertConditionEquals(conditions[2], COMPENSATION_.CONTRACT_ID.`in`(contractIds))
        assertConditionEquals(conditions[3], COMPENSATION_.CATEGORY.`in`(categories))
        // State condition comes next
        val expectedState = COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED).and(
            COMPENSATION_.PROCESSING_FROM.isNull.or(
                COMPENSATION_.PROCESSING_FROM.greaterOrEqual(todayStartOfDay),
            ),
        )
        assertConditionEquals(conditions[4], expectedState)
        assertConditionEquals(conditions[5], COMPENSATION_.START_DATE.ge(testStartDate))
        assertConditionEquals(conditions[6], COMPENSATION_.END_DATE.le(testEndDate))
    }

    // === Tests for buildCompensationRecordFilterCondition(CompensationRecordsGrpcFilter) ===

    @Test
    fun `buildCompensationRecordFilterCondition(GrpcFilter) - empty filter - returns empty list`() {
        val filter = CompensationRecordsGrpcFilter()
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertTrue(conditions.isEmpty())
    }

    @Test
    fun `buildCompensationRecordFilterCondition(GrpcFilter) - with non-empty ids - adds ids condition`() {
        val ids = listOf(testUuid1, testUuid2)
        val filter = CompensationRecordsGrpcFilter(ids = ids)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        assertConditionEquals(conditions[0], COMPENSATION_.ID.`in`(ids))
    }

    @Test
    fun `buildCompensationRecordFilterCondition(GrpcFilter) - with empty ids - adds no ids condition`() {
        val filter = CompensationRecordsGrpcFilter(ids = emptyList())
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertTrue(conditions.none { it.toString().contains(COMPENSATION_.ID.name) })
    }

    @Test
    fun `buildCompensationRecordFilterCondition(GrpcFilter) - with non-zero entityId - adds entityId condition`() {
        val filter = CompensationRecordsGrpcFilter(entityId = testEntityId)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        assertConditionEquals(conditions[0], COMPENSATION_.ENTITY_ID.eq(testEntityId))
    }

    @Test
    fun `buildCompensationRecordFilterCondition(GrpcFilter) - with zero entityId - adds no entityId condition`() {
        val filter = CompensationRecordsGrpcFilter(entityId = 0L)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertTrue(conditions.none { it.toString().contains(COMPENSATION_.ENTITY_ID.name) })
    }

    @Test
    fun `buildCompensationRecordFilterCondition(GrpcFilter) - with null entityId - adds no entityId condition`() {
        val filter = CompensationRecordsGrpcFilter(entityId = null)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertTrue(conditions.none { it.toString().contains(COMPENSATION_.ENTITY_ID.name) })
    }

    @Test
    fun `buildCompensationRecordFilterCondition(Grpc) - with non-empty contractIds- adds contractIds condition`() {
        val contractIds = listOf(testContractId1, testContractId2)
        val filter = CompensationRecordsGrpcFilter(contractIds = contractIds)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        assertConditionEquals(conditions[0], COMPENSATION_.CONTRACT_ID.`in`(contractIds))
    }

    @Test
    fun `buildCompensationRecordFilterCondition(Grpc) - with empty contractIds - adds no contractIds condition`() {
        val filter = CompensationRecordsGrpcFilter(contractIds = emptyList())
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertTrue(conditions.none { it.toString().contains(COMPENSATION_.CONTRACT_ID.name) })
    }

    @Test
    fun `buildCompensationRecordFilterCondition(Grpc) - with non-empty categories - adds categories condition`() {
        val categories = listOf(testCategory1, testCategory2)
        val filter = CompensationRecordsGrpcFilter(categories = categories)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        assertConditionEquals(conditions[0], COMPENSATION_.CATEGORY.`in`(categories))
    }

    @Test
    fun `buildCompensationRecordFilterCondition(Grpc) - with empty categories - adds no categories condition`() {
        val filter = CompensationRecordsGrpcFilter(categories = emptyList())
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertTrue(conditions.none { it.toString().contains(COMPENSATION_.CATEGORY.name) })
    }

    @Test
    fun `buildCompensationRecordFilterCondition(Grpc) - with activeTo - adds startDate le activeTo condition`() {
        val filter = CompensationRecordsGrpcFilter(activeTo = testEndDate)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        assertConditionEquals(conditions[0], COMPENSATION_.START_DATE.lessOrEqual(testEndDate))
    }

    @Test
    fun `buildCompensationRecordFilterCondition(Grpc) - with activeFrom - adds endDate condition`() {
        val filter = CompensationRecordsGrpcFilter(activeFrom = testStartDate)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        val expected = (COMPENSATION_.END_DATE.isNull).or(COMPENSATION_.END_DATE.greaterOrEqual(testStartDate))
        assertConditionEquals(conditions[0], expected)
    }

    @Test
    fun `buildCompensationRecordFilterCondition(Grpc) - with state COMPLETED - adds completed condition`() {
        val filter = CompensationRecordsGrpcFilter(state = CompensationState.COMPLETED)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size) // State adds its condition
        val expected = COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED).and(
            COMPENSATION_.PROCESSING_TO.isNotNull.and(
                COMPENSATION_.PROCESSING_TO.lessThan(todayStartOfDay),
            ),
        )
        assertConditionEquals(conditions[0], expected)
    }

    @Test
    fun `buildCompensationRecordFilterCondition(Grpc) - with state UPCOMING - adds upcoming condition`() {
        val filter = CompensationRecordsGrpcFilter(state = CompensationState.UPCOMING)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        val expected = COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED).and(
            COMPENSATION_.PROCESSING_FROM.isNull.or(
                COMPENSATION_.PROCESSING_FROM.greaterOrEqual(todayStartOfDay),
            ),
        )
        assertConditionEquals(conditions[0], expected)
    }

    @Test
    fun `buildCompensationRecordFilterCondition(Grpc) - with state PROCESSING - adds processing condition`() {
        val filter = CompensationRecordsGrpcFilter(state = CompensationState.PROCESSING)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(1, conditions.size)
        val expected = COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED).and(
            COMPENSATION_.PROCESSING_FROM.isNotNull.and(
                COMPENSATION_.PROCESSING_FROM.lessThan(todayStartOfDay),
            )
                .and(
                    COMPENSATION_.PROCESSING_TO.isNull.or(
                        COMPENSATION_.PROCESSING_TO.greaterOrEqual(todayStartOfDay),
                    ),
                ),
        )
        assertConditionEquals(conditions[0], expected)
    }

    @Test
    fun `buildCompensationRecordFilterCondition(Grpc) - with state ACTIVE default date - adds active conditions`() {
        val filter = CompensationRecordsGrpcFilter(state = CompensationState.ACTIVE)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(2, conditions.size) // Base active condition + unique schema item subquery

        // Check base active condition
        val expectedBase = COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED)
            .and(
                COMPENSATION_.PROCESSING_TO.isNull.or(
                    COMPENSATION_.PROCESSING_TO.greaterOrEqual(todayStartOfDay),
                ),
            )
        assertConditionEquals(conditions[0], expectedBase)

        // Check unique schema item condition (presence and basic structure)
        assertConditionContains(conditions[1], "\"COMPENSATION\".\"COMPENSATION\".\"ID\" in")
        assertConditionContains(conditions[1], "select non_duplicate_compensation_query.id")
    }

    @Test
    fun `buildCompensationRecordFilterCondition(Grpc) - with state ACTIVE specific date - adds active conditions`() {
        val filter = CompensationRecordsGrpcFilter(state = CompensationState.ACTIVE, activeAsOn = testActiveAsOnDate)
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(2, conditions.size)

        // Check base active condition uses the specific date
        val expectedBase = COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED)
            .and(
                COMPENSATION_.PROCESSING_TO.isNull.or(
                    COMPENSATION_.PROCESSING_TO.greaterOrEqual(testActiveAsOnDate.atStartOfDay()),
                ),
            )
        assertConditionEquals(conditions[0], expectedBase)

        // Check unique schema item condition uses the specific date
        assertConditionContains(conditions[1], "\"COMPENSATION\".\"COMPENSATION\".\"ID\" in")
    }

    @Test
    fun `buildCompensationRecordFilterCondition(Grpc) - with multiple fields - adds all conditions`() {
        val ids = listOf(testUuid1)
        val contractIds = listOf(testContractId1)
        val categories = listOf(testCategory1)
        val filter = CompensationRecordsGrpcFilter(
            ids = ids,
            entityId = testEntityId,
            contractIds = contractIds,
            categories = categories,
            activeFrom = testStartDate,
            activeTo = testEndDate,
            state = CompensationState.ACTIVE, // Test mixing with active state
            activeAsOn = testActiveAsOnDate, // Use specific date for active state
        )
        val conditions = CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition(filter)
        assertEquals(8, conditions.size) // ids, entityId, contractIds, categories, activeTo, activeFrom, state (2
        // parts) -> mistake, active state adds 2 conditions

        // Order: ids, entityId, contractIds, categories, activeTo, activeFrom, state (base), state (unique)
        assertEquals(8, conditions.size)

        assertConditionEquals(conditions[0], COMPENSATION_.ID.`in`(ids))
        assertConditionEquals(conditions[1], COMPENSATION_.ENTITY_ID.eq(testEntityId))
        assertConditionEquals(conditions[2], COMPENSATION_.CONTRACT_ID.`in`(contractIds))
        assertConditionEquals(conditions[3], COMPENSATION_.CATEGORY.`in`(categories))
        assertConditionEquals(conditions[4], COMPENSATION_.START_DATE.lessOrEqual(testEndDate)) // activeTo
        val expectedActiveFrom = (COMPENSATION_.END_DATE.isNull).or(
            COMPENSATION_.END_DATE.greaterOrEqual(testStartDate),
        )
        assertConditionEquals(conditions[5], expectedActiveFrom) // activeFrom

        // State conditions (ACTIVE with specific date)
        val expectedStateBase = COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED)
            .and(
                COMPENSATION_.PROCESSING_TO.isNull.or(
                    COMPENSATION_.PROCESSING_TO.greaterOrEqual(testActiveAsOnDate.atStartOfDay()),
                ),
            )
        assertConditionEquals(conditions[6], expectedStateBase) // state (base)
        assertConditionContains(conditions[7], "\"COMPENSATION\".\"COMPENSATION\".\"ID\" in") // state (unique)
    }

    // --- Helper Assertions ---

    // Use toString comparison as a pragmatic way to check generated conditions
    private fun assertConditionEquals(
        actual: Condition,
        expected: Condition,
    ) {
        assertEquals(
            expected.toString().replace(Regex("\\s+"), " "), // Normalize whitespace
            actual.toString().replace(Regex("\\s+"), " "), // Normalize whitespace
        )
    }

    // Use contains for checking parts, especially useful for complex subqueries
    private fun assertConditionContains(
        condition: Condition,
        expectedSubstring: String,
    ) {
        val conditionString = condition.toString()
        val normalizedCondition = conditionString.replace(Regex("\\s+"), " ") // Normalize whitespace
        val normalizedExpected = expectedSubstring.replace(Regex("\\s+"), " ") // Normalize whitespace

        assertTrue(
            normalizedCondition.contains(normalizedExpected),
            "Condition string '$normalizedCondition' did not contain expected substring '$normalizedExpected'",
        )
    }
}
