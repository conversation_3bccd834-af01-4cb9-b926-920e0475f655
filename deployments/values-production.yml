environments:
  SPRING_PROFILES_ACTIVE: prod
  awslogs-group: /prd/app/tech/compensationService/cloudwatchLogGroup
  awslogs-stream-prefix: ecs
kind: v2
name: compensationService
resources:
  cpu: 512
  memory: 2048
secrets:
  APM_SERVER_URL: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/apmServer/param
  APM_TOKEN: /monitoring/elasticsearch/apmToken/staging-release
  GROWTHBOOK_BASEURL: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/growthbook/param
  GROWTHBOOK_ENVKEY: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/growthbook/env/key/param
  GRPC_CLIENT_AUTHORITY_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/grpc/userService/param
  GRPC_CLIENT_COMPANYSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/grpc/companyService/param
  GRPC_CLIENT_CONTRACTSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/grpc/contractService/param
  GRPC_CLIENT_CONTRACT_OFF_BOARDING_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/grpc/contractOffboardingService/param
  GRPC_CLIENT_COUNTRYSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/grpc/countryService/param
  GRPC_CLIENT_PAYROLL_SCHEMA_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/grpc/payrollSchemaService/param
  GRPC_CLIENT_PIGEON_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/grpc/pigeonService/param
  GRPC_CLIENT_ORGMANAGEMENTSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/grpc/orgManagementService/param
  JWT_PUBLIC_KEY: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/employee/jwt/publicKey/param
  KAFKA_CONSUMER_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/kafka/bootstrapServers/param
  KAFKA_PRODUCER_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/kafka/bootstrapServers/param
  SPRING_DATASOURCE_PASSWORD: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/compensationService/db/user/password/param
  SPRING_DATASOURCE_URL: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/compensationService/db/url/param
  SPRING_DATASOURCE_USERNAME: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/compensationService/db/user/param
