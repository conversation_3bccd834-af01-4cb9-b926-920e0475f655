package com.multiplier.compensation.domain.common.exception

import com.multiplier.common.exception.ErrorCode
import com.multiplier.common.exception.MplSystemException

class DownstreamServiceException(
    errorCode: ErrorCode,
    message: String,
    exception: Throwable? = null,
    context: Map<String, Any> = emptyMap(),
) : MplSystemException(
        errorCode = errorCode,
        message = message,
        cause = exception,
        context = context,
    )
