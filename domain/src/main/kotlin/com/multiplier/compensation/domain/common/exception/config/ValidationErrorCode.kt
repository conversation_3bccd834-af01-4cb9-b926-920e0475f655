package com.multiplier.compensation.domain.common.exception.config

import com.multiplier.common.exception.ErrorCode

enum class ValidationErrorCode(
    override val message: String,
) : ErrorCode {
    CompensationItemNotFound("Compensation item not found"),
    CompensationNotFound("Compensation not found"),
    ConditionalCheckFailed("Conditional check failed"),
    ConstraintViolationException("Constraint violation exception"),

    EntityNotFound("Entity not found"),

    InvalidBooleanInput("Invalid boolean input"),
    InvalidCalculationFormula("Invalid formula for calculated compensation"),
    InvalidCancellationArrearCoverageForItem("Invalid cancellation arrear coverage for compensation item"),
    InvalidCompensationInput("Invalid compensation inputs"),
    InvalidCompensationInputFilter("Invalid filter provided for compensation inputs"),
    InvalidCompensationInputStatus("Invalid compensation input status"),
    InvalidCompensationItemStatus("Invalid compensation item status"),
    InvalidCompensationItemWatermarkingInputFilter("Invalid filter provided for compensation item watermarking inputs"),
    InvalidCompensationLogWatermarkingInputFilter("Invalid filter provided for compensation Log watermarking inputs"),
    InvalidCompensationRecordType("Invalid compensation record type"),
    InvalidCompensationRecordsFilter("Invalid filter provided for compensation records"),
    InvalidCompensationState("Invalid compensation state"),
    InvalidCompensationStatus("Invalid compensation status"),
    InvalidCompensationUpdateInputStatus("Invalid compensation update input status"),
    InvalidCompensationUseCase("Invalid compensation use case"),
    InvalidCompensationWatermarkingInputFilter("Invalid filter provided for compensation watermarking inputs"),
    InvalidContractOffboardingStatus("Invalid contract offboarding status"),
    InvalidContractSchemaMappingDateRange("Effective start date cannot be after effective end date"),
    InvalidContractSchemaMappingOverlap("Overlapping mapping found for contract and date range"),
    InvalidContractStatus("Invalid contract status"),
    InvalidCutoffDateChange("Invalid cutoff date change"),
    InvalidDateFormat("Invalid date format"),
    InvalidDerivedInputRow("Invalid derived input row"),
    InvalidEntityId("Invalid entity id"),
    InvalidExistingInstallmentArrear("Invalid existing cancellation arrear found for installment item"),
    InvalidExistingItemForConsolidation("Invalid existing item found during consolidation."),
    InvalidFixedAmount("Invalid amount for fixed type compensation"),
    InvalidFixedCalculatedInputs("Rows with type FIXED/CALCULATED are not acceptable in customer input"),
    InvalidFrequencyConversion("Invalid frequency conversion between schema and input"),
    InvalidItemAdjustmentRequest("Invalid item adjustment request"),
    InvalidOfferingCode("Invalid offering code"),
    InvalidOperationStatus("Invalid operation status"),
    InvalidPaySchedule("Invalid pay schedule"),
    InvalidPayScheduleDateRange("Invalid pay schedule date range"),
    InvalidPayScheduleFrequency("Invalid pay schedule frequency"),
    InvalidPayScheduleRequest("Invalid pay schedule request"),
    InvalidPayScheduleScope("Invalid pay schedule configuration scope"),
    InvalidSchemaRequest("Invalid schema request"),

    MissingCountryCodeInRequest("Missing country code in request"),
    MissingEndDate("Missing End Date in second latest Base Pay Record"),
    MultipleEntitiesFound("Multiple entities found"),
    MultipleUnprocessedItemsEligibleForConsolidation("Multiple eligible unprocessed items found during consolidation"),

    NonNullableFieldViolation("Non-nullable field violation"),
    NumberOfInstallmentsMismatch("Number of installments mismatch"),

    PayScheduleIntervalLengthNotConfigured("Interval length not supported"),
    PayScheduleNotFound("Pay schedule not found"),

    SkeletonConfigurationNotFound("Skeleton configuration not found"),

    UnsupportedBillingFrequency("Unsupported billing frequency for conversion"),
    UnsupportedUseCase("Unsupported use case"),
}
