package com.multiplier.compensation.domain.compensation

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.compensation.enums.CompensationState
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CompensationEnriched(
    val id: UUID,
    val companyId: Long,
    val entityId: Long,
    val contractId: Long,
    val category: String,
    val schemaComponentName: String,
    val currency: String,
    val billingRateType: BillingRateType,
    val billingRate: Double?,
    val billingFrequency: BillingFrequency? = null,
    val payScheduleName: String,
    val payScheduleFrequency: PayScheduleFrequency,
    val startDate: LocalDate,
    val endDate: LocalDate? = null,
    val processingFrom: LocalDate? = null,
    val processingTo: LocalDate? = null,
    val isInstallment: Boolean,
    val noOfInstallments: Int?,
    val generatedInstallments: Int?,
    var isTaxable: Boolean,
    var isFixed: Boolean,
    var isProrated: Boolean,
    var isMandatory: Boolean,
    var isPartOfBasePay: Boolean,
    val recordType: CompensationRecordType,
    val status: CompensationRecordStatus,
    val notes: String? = null,
    val label: String,
    val isPartOfCtc: Boolean,
    val isOvertimeEligible: Boolean,
    val createdOn: LocalDateTime = LocalDateTime.now(),
    val createdBy: Long = -1L,
    val updatedOn: LocalDateTime = LocalDateTime.now(),
    val updatedBy: Long = -1L,
    // transient fields
    var state: CompensationState? = null,
)

enum class CompensationRecordStatus {
    NEW,
    PROCESSING,
    COMPLETED,
    ABORTED,
    DELETED,
    ONBOARDING_DRAFT,
}
