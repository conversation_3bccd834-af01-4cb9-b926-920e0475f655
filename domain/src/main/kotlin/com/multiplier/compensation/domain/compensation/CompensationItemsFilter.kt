package com.multiplier.compensation.domain.compensation

import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemState
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import java.time.LocalDateTime
import java.util.UUID

data class CompensationItemsFilter(
    val ids: Set<UUID> = emptySet(),
    val compensationIds: Set<UUID> = emptySet(),
    val contractIds: Set<Long> = emptySet(),
    val statuses: Set<CompensationItemStatus> = emptySet(),
    val includedCategories: Set<String> = emptySet(),
    val excludedCategories: Set<String> = emptySet(),
    val updatedFromExclusive: LocalDateTime? = null,
    val updatedToInclusive: LocalDateTime? = null,
    val states: Set<CompensationItemState> = emptySet(),
)
