package com.multiplier.compensation.domain.compensationinput

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.ReasonCode
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus
import com.multiplier.compensation.domain.compensationinput.enums.CompensationLifecycle
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CompensationInputEnriched(
    val id: UUID,
    val companyId: Long,
    val entityId: Long,
    val contractId: Long,
    val schemaItemId: UUID,
    val category: String,
    val currency: String,
    val billingRateType: BillingRateType,
    val billingRate: Double?,
    val billingFrequency: BillingFrequency,
    val startDate: LocalDate,
    val endDate: LocalDate? = null,
    val isInstallment: Boolean,
    val noOfInstallments: Int?,
    val requestType: RequestType,
    val requestId: String,
    val reasonCode: ReasonCode? = null,
    val status: CompensationInputStatus,
    val notes: String? = null,
    val createdOn: LocalDateTime = LocalDateTime.now(),
    val createdBy: Long = -1L,
    val updatedOn: LocalDateTime = LocalDateTime.now(),
    val updatedBy: Long = -1L,
    // additional fields
    val payScheduleName: String,
    val payScheduleFrequency: PayScheduleFrequency,
    val schemaComponentName: String,
    val compensationName: String,
    val compensationCategory: String,
    val isTaxable: Boolean,
    val isFixed: Boolean,
    val isProrated: Boolean,
    val isMandatory: Boolean,
    val isPartOfBasePay: Boolean,
    val label: String,
    val isOvertimeEligible: Boolean,
    val isPartOfCtc: Boolean,
    /**
     * Below fields are for migrating older compensations to this new service.
     * These fields can be removed after GP and EOR migration completion.
     *
     * lifecycle - Representing whether this compensation is created on new system (CompensationLifecycle.NEW)
     *              or it was migrated from older schema (CompensationLifecycle.MIGRATED).
     * sourceEffectiveDate - This field would capture the effective date for this compensation as per the older schema.
     */
    val lifecycle: CompensationLifecycle? = CompensationLifecycle.NEW,
    val sourceEffectiveDate: LocalDateTime? = null,
)
