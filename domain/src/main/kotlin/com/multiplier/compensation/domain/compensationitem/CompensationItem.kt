package com.multiplier.compensation.domain.compensationitem

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CompensationItem(
    val id: UUID,
    val companyId: Long,
    val entityId: Long,
    val contractId: Long,
    val compensationId: UUID,
    val schemaItemId: UUID,
    val category: String?,
    val currency: String,
    val billingRateType: BillingRateType,
    val billingRate: Double?,
    val billingFrequency: BillingFrequency,
    val payScheduleId: UUID,
    val compensationStartDate: LocalDate,
    val compensationEndDate: LocalDate? = null,
    val compensationStatus: CompensationStatus,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val isInstallment: Boolean,
    val noOfInstallments: Int?,
    val currentInstallment: Int?,
    val cutOffDate: LocalDate? = null,
    val expectedPayDate: LocalDate,
    val calculatedAmount: Double?,
    val payDate: LocalDate? = null,
    val previousId: UUID? = null,
    val status: CompensationItemStatus,
    val isArrear: Boolean,
    val arrearOf: UUID? = null,
    val arrearTriggerReference: String? = null,
    val updateTriggerReference: String? = null,
    val createdOn: LocalDateTime = LocalDateTime.now(),
    val createdBy: Long = -1L,
    val updatedOn: LocalDateTime = LocalDateTime.now(),
    val updatedBy: Long = -1L,
)
