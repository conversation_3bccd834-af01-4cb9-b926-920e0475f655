package com.multiplier.compensation.domain.compensationitem

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemState
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CompensationItemEnriched(
    val id: UUID,
    val companyId: Long,
    val entityId: Long,
    val contractId: Long,
    val category: String,
    val schemaComponentName: String,
    val currency: String,
    val billingRateType: BillingRateType,
    val billingRate: Double?,
    val billingFrequency: BillingFrequency,
    val payScheduleName: String,
    val payScheduleFrequency: PayScheduleFrequency,
    val startDate: LocalDate,
    val endDate: LocalDate? = null,
    val isInstallment: Boolean,
    val noOfInstallments: Int?,
    val currentInstallment: Int?,
    var isTaxable: Boolean,
    var isFixed: Boolean,
    var isProrated: Boolean,
    var isMandatory: Boolean,
    var isPartOfBasePay: Boolean,
    val calculatedAmount: Double?,
    var cutOffDate: LocalDate? = null,
    val expectedPayDate: LocalDate,
    val payDate: LocalDate? = null,
    val previousId: UUID? = null,
    val status: CompensationItemStatus,
    val isArrear: Boolean,
    val arrearOf: UUID? = null,
    val arrearTriggerReference: String? = null,
    val updateTriggerReference: String? = null,
    val createdOn: LocalDateTime = LocalDateTime.now(),
    val createdBy: Long = -1L,
    val updatedOn: LocalDateTime = LocalDateTime.now(),
    val updatedBy: Long = -1L,
    var state: CompensationItemState? = null,
    var isDeletable: Boolean = false,
    var isEditable: Boolean = false,
    val label: String,
    val isOvertimeEligible: Boolean,
    val isPartOfCtc: Boolean,
    val notes: String? = null,
)
