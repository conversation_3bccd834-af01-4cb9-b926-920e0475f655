package com.multiplier.compensation.domain.payschedule

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.OfferingCode
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency

data class GetEligibleBillingFrequenciesRequest(
    val entityId: Long,
    val countryCode: CountryCode?,
    val offeringCode: OfferingCode,
    val state: String?,
    val grossSalaryPayScheduleFrequency: PayScheduleFrequency?,
    val compensationCategory: String?,
)
