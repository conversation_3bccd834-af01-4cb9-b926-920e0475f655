package com.multiplier.compensation.domain.common.contract

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ContractTypeTest {
    @Test
    fun `test contract type values`() {
        assertEquals(ContractType.CONTRACTOR, ContractType.valueOf("CONTRACTOR"))
        assertEquals(ContractType.FREELANCER, ContractType.valueOf("FREELANCER"))
        assertEquals(ContractType.HR_MEMBER, ContractType.valueOf("HR_MEMBER"))
        assertEquals(ContractType.EMPLOYEE, ContractType.valueOf("EMPLOYEE"))
    }
}
