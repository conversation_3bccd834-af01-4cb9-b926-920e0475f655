package com.multiplier.compensation.domain.compensationlog

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.ReasonCode
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationlog.enums.CompensationLogStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class CompensationLogTest {
    @Test
    fun `should create CompensationLog with all fields populated`() {
        // Given
        val id = UUID.randomUUID()
        val companyId = 1L
        val entityId = 2L
        val contractId = 3L
        val schemaItemId = UUID.randomUUID()
        val category = "CONTRACT_BASE_PAY"
        val currency = "USD"
        val billingRateType = BillingRateType.VALUE
        val billingRate = 5000.0
        val billingFrequency = BillingFrequency.MONTHLY
        val payScheduleId = UUID.randomUUID()
        val startDate = LocalDate.of(2025, 1, 1)
        val endDate = LocalDate.of(2025, 12, 31)
        val isInstallment = true
        val noOfInstallments = 12
        val requestType = RequestType.COMPENSATION_SETUP
        val requestId = "REQ-456"
        val status = CompensationLogStatus.PROCESSING
        val reasonCode = ReasonCode.SC001
        val notes = "This is a test note."
        val createdOn = LocalDateTime.of(2025, 1, 1, 10, 0)
        val createdBy = 999L
        val updatedOn = LocalDateTime.of(2025, 6, 1, 11, 0)
        val updatedBy = 888L

        // When
        val log = CompensationLog(
            id = id,
            companyId = companyId,
            entityId = entityId,
            contractId = contractId,
            schemaItemId = schemaItemId,
            category = category,
            currency = currency,
            billingRateType = billingRateType,
            billingRate = billingRate,
            billingFrequency = billingFrequency,
            payScheduleId = payScheduleId,
            startDate = startDate,
            endDate = endDate,
            isInstallment = isInstallment,
            noOfInstallments = noOfInstallments,
            requestType = requestType,
            requestId = requestId,
            status = status,
            reasonCode = reasonCode,
            notes = notes,
            createdOn = createdOn,
            createdBy = createdBy,
            updatedOn = updatedOn,
            updatedBy = updatedBy,
        )

        // Then
        assertEquals(id, log.id)
        assertEquals(companyId, log.companyId)
        assertEquals(entityId, log.entityId)
        assertEquals(contractId, log.contractId)
        assertEquals(schemaItemId, log.schemaItemId)
        assertEquals(category, log.category)
        assertEquals(currency, log.currency)
        assertEquals(billingRateType, log.billingRateType)
        assertEquals(billingRate, log.billingRate)
        assertEquals(billingFrequency, log.billingFrequency)
        assertEquals(payScheduleId, log.payScheduleId)
        assertEquals(startDate, log.startDate)
        assertEquals(endDate, log.endDate)
        assertEquals(isInstallment, log.isInstallment)
        assertEquals(noOfInstallments, log.noOfInstallments)
        assertEquals(requestType, log.requestType)
        assertEquals(requestId, log.requestId)
        assertEquals(status, log.status)
        assertEquals(reasonCode, log.reasonCode)
        assertEquals(notes, log.notes)
        assertEquals(createdOn, log.createdOn)
        assertEquals(createdBy, log.createdBy)
        assertEquals(updatedOn, log.updatedOn)
        assertEquals(updatedBy, log.updatedBy)
    }

    @Test
    fun `should handle null and optional fields`() {
        // Given
        val log = CompensationLog(
            id = UUID.randomUUID(),
            companyId = null,
            entityId = 42L,
            contractId = null,
            schemaItemId = null,
            category = null,
            currency = null,
            billingRateType = null,
            billingRate = null,
            billingFrequency = null,
            payScheduleId = null,
            startDate = null,
            endDate = null,
            isInstallment = null,
            noOfInstallments = null,
            requestType = RequestType.COMPENSATION_SETUP,
            requestId = "REQ-789",
            status = CompensationLogStatus.FAILED,
            reasonCode = null,
            notes = null,
        )

        // Then
        assertNull(log.companyId)
        assertNull(log.contractId)
        assertNull(log.schemaItemId)
        assertNull(log.category)
        assertNull(log.currency)
        assertNull(log.billingRateType)
        assertNull(log.billingRate)
        assertNull(log.billingFrequency)
        assertNull(log.payScheduleId)
        assertNull(log.startDate)
        assertNull(log.endDate)
        assertNull(log.isInstallment)
        assertNull(log.noOfInstallments)
        assertNull(log.reasonCode)
        assertNull(log.notes)
    }

    @Test
    fun `should initialize createdOn and updatedOn to default timestamps`() {
        // When
        val log = CompensationLog(
            id = UUID.randomUUID(),
            companyId = null,
            entityId = 123L,
            contractId = null,
            schemaItemId = null,
            category = null,
            currency = null,
            billingRateType = null,
            billingRate = null,
            billingFrequency = null,
            payScheduleId = null,
            startDate = null,
            isInstallment = null,
            noOfInstallments = null,
            requestType = RequestType.COMPENSATION_REVISION,
            requestId = "REQ-101",
            status = CompensationLogStatus.SUCCEEDED,
        )

        // Then
        assertNotNull(log.createdOn)
        assertNotNull(log.updatedOn)
        assertEquals(-1L, log.createdBy)
        assertEquals(-1L, log.updatedBy)
    }
}
