package com.multiplier.compensation.domain.compensationschema

import com.fasterxml.uuid.Generators
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class ContractSchemaMappingTest {
    private val timeBasedEpochGenerator = Generators.timeBasedEpochGenerator()

    @Test
    fun `test ContractSchemaMapping creation`() {
        // Given
        val id = timeBasedEpochGenerator.generate()
        val contractId = 101L
        val schemaId = UUID.randomUUID()
        val effectiveStartDate = LocalDate.of(2025, 4, 1)
        val reasonCode = ContractSchemaMappingReasonCode.SC001
        val createdBy = 1L
        val updatedBy = 2L

        // When
        val contractSchemaMapping = ContractSchemaMapping(
            id = id,
            contractId = contractId,
            schemaId = schemaId,
            effectiveStartDate = effectiveStartDate,
            effectiveEndDate = null,
            reasonCode = reasonCode,
            isDeleted = false,
            createdOn = LocalDateTime.now(),
            createdBy = createdBy,
            updatedOn = LocalDateTime.now(),
            updatedBy = updatedBy,
        )

        // Then
        assertEquals(id, contractSchemaMapping.id)
        assertEquals(contractId, contractSchemaMapping.contractId)
        assertEquals(schemaId, contractSchemaMapping.schemaId)
        assertEquals(effectiveStartDate, contractSchemaMapping.effectiveStartDate)
        assertNull(contractSchemaMapping.effectiveEndDate)
        assertEquals(reasonCode, contractSchemaMapping.reasonCode)
        assertFalse(contractSchemaMapping.isDeleted)
        assertEquals(createdBy, contractSchemaMapping.createdBy)
        assertEquals(updatedBy, contractSchemaMapping.updatedBy)
    }

    @Test
    fun `test ContractSchemaMapping creation with effectiveEndDate`() {
        // Given
        val id = timeBasedEpochGenerator.generate()
        val contractId = 101L
        val schemaId = UUID.randomUUID()
        val effectiveStartDate = LocalDate.of(2025, 4, 1)
        val effectiveEndDate = LocalDate.of(2025, 4, 30)
        val reasonCode = ContractSchemaMappingReasonCode.SC001
        val createdBy = 1L
        val updatedBy = 2L

        // When
        val contractSchemaMapping = ContractSchemaMapping(
            id = id,
            contractId = contractId,
            schemaId = schemaId,
            effectiveStartDate = effectiveStartDate,
            effectiveEndDate = effectiveEndDate,
            reasonCode = reasonCode,
            isDeleted = false,
            createdOn = LocalDateTime.now(),
            createdBy = createdBy,
            updatedOn = LocalDateTime.now(),
            updatedBy = updatedBy,
        )

        // Then
        assertEquals(id, contractSchemaMapping.id)
        assertEquals(contractId, contractSchemaMapping.contractId)
        assertEquals(schemaId, contractSchemaMapping.schemaId)
        assertEquals(effectiveStartDate, contractSchemaMapping.effectiveStartDate)
        assertEquals(effectiveEndDate, contractSchemaMapping.effectiveEndDate)
        assertEquals(reasonCode, contractSchemaMapping.reasonCode)
        assertFalse(contractSchemaMapping.isDeleted)
        assertEquals(createdBy, contractSchemaMapping.createdBy)
        assertEquals(updatedBy, contractSchemaMapping.updatedBy)
    }
}
