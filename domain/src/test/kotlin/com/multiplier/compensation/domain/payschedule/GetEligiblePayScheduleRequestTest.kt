package com.multiplier.compensation.domain.payschedule

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.OfferingCode
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class GetEligiblePayScheduleRequestTest {
    @Test
    fun `should create GetEligiblePayScheduleRequest with correct properties`() {
        val entityId = 123L
        val countryCode = CountryCode.USA
        val stateCode = "CA"

        val request = GetEligiblePayScheduleRequest(
            entityId = entityId,
            countryCode = countryCode,
            state = stateCode,
            offeringCode = OfferingCode.EOR,
            grossSalaryPayScheduleFrequency = PayScheduleFrequency.MONTHLY,
            billingFrequency = BillingFrequency.MONTHLY,
            compensationCategory = "CONTRACT_BASE_PAY",
        )

        assertEquals(entityId, request.entityId)
        assertEquals(countryCode, request.countryCode)
        assertEquals(stateCode, request.state)
        assertEquals(OfferingCode.EOR, request.offeringCode)
        assertEquals(PayScheduleFrequency.MONTHLY, request.grossSalaryPayScheduleFrequency)
        assertEquals(BillingFrequency.MONTHLY, request.billingFrequency)
        assertEquals("CONTRACT_BASE_PAY", request.compensationCategory)
    }
}
