package com.multiplier.compensation.domain.skeleton.enums

import com.multiplier.compensation.domain.common.CategoryConstants
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class SkeletonTypeTest {
    @Test
    fun `test PAY_SCHEDULE properties`() {
        val skeletonType = SkeletonType.PAY_SCHEDULE
        assertEquals(true, skeletonType.excludeInactiveConfigs)
        assertEquals(emptyList<String>(), skeletonType.compensationCategories)
    }

    @Test
    fun `test COMPENSATION_SCHEMA properties`() {
        val skeletonType = SkeletonType.COMPENSATION_SCHEMA
        assertEquals(true, skeletonType.excludeInactiveConfigs)
        assertEquals(
            listOf(
                CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_ADDITIONAL,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP,
                CategoryConstants.CATEGORY_MONTH_PAY_13TH_14TH,
                CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE,
                CategoryConstants.CATEGORY_EMPLOYER_CONTRIBUTION,
                CategoryConstants.CATEGORY_EMPLOYEE_CONTRIBUTION,
                CategoryConstants.CATEGORY_PAY_SUPPLEMENT,
                CategoryConstants.CATEGORY_EMPLOYEE_DEDUCTION,
                CategoryConstants.CATEGORY_EMPLOYER_DEDUCTION,
                CategoryConstants.CATEGORY_ARREARS_BASE_PAY_BREAKUP,
            ),
            skeletonType.compensationCategories,
        )
    }

    @Test
    fun `test COMPENSATION_SETUP properties`() {
        val skeletonType = SkeletonType.COMPENSATION_SETUP
        assertEquals(true, skeletonType.excludeInactiveConfigs)
        assertEquals(
            listOf(
                CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_ADDITIONAL,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP,
                CategoryConstants.CATEGORY_MONTH_PAY_13TH_14TH,
                CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE,
                CategoryConstants.CATEGORY_EMPLOYER_CONTRIBUTION,
                CategoryConstants.CATEGORY_EMPLOYEE_CONTRIBUTION,
                CategoryConstants.CATEGORY_ARREARS_BASE_PAY_BREAKUP,
            ),
            skeletonType.compensationCategories,
        )
    }

    @Test
    fun `test COMPENSATION_REVISION properties`() {
        val skeletonType = SkeletonType.COMPENSATION_REVISION
        assertEquals(false, skeletonType.excludeInactiveConfigs)
        assertEquals(
            listOf(
                CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_ADDITIONAL,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP,
                CategoryConstants.CATEGORY_MONTH_PAY_13TH_14TH,
                CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE,
                CategoryConstants.CATEGORY_EMPLOYER_CONTRIBUTION,
                CategoryConstants.CATEGORY_EMPLOYEE_CONTRIBUTION,
                CategoryConstants.CATEGORY_ARREARS_BASE_PAY_BREAKUP,
            ),
            skeletonType.compensationCategories,
        )
    }

    @Test
    fun `test PAY_SUPPLEMENT properties`() {
        val skeletonType = SkeletonType.PAY_SUPPLEMENT
        assertEquals(false, skeletonType.excludeInactiveConfigs)
        assertEquals(
            listOf(CategoryConstants.CATEGORY_PAY_SUPPLEMENT),
            skeletonType.compensationCategories,
        )
    }

    @Test
    fun `test DEDUCTION properties`() {
        val skeletonType = SkeletonType.DEDUCTION
        assertEquals(false, skeletonType.excludeInactiveConfigs)
        assertEquals(
            listOf(
                CategoryConstants.CATEGORY_EMPLOYEE_DEDUCTION,
                CategoryConstants.CATEGORY_EMPLOYER_DEDUCTION,
            ),
            skeletonType.compensationCategories,
        )
    }

    @Test
    fun `test COMPENSATION_BACKFILL properties`() {
        val skeletonType = SkeletonType.COMPENSATION_BACKFILL
        assertEquals(false, skeletonType.excludeInactiveConfigs)
        assertEquals(
            listOf(
                CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_ADDITIONAL,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP,
                CategoryConstants.CATEGORY_MONTH_PAY_13TH_14TH,
                CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE,
                CategoryConstants.CATEGORY_EMPLOYER_CONTRIBUTION,
                CategoryConstants.CATEGORY_EMPLOYEE_CONTRIBUTION,
                CategoryConstants.CATEGORY_EMPLOYEE_DEDUCTION,
                CategoryConstants.CATEGORY_EMPLOYER_DEDUCTION,
                CategoryConstants.CATEGORY_PAY_SUPPLEMENT,
                CategoryConstants.CATEGORY_ARREARS_BASE_PAY_BREAKUP,
            ),
            skeletonType.compensationCategories,
        )
    }

    @Test
    fun `test COMPENSATION_REVISION_BACKFILL properties`() {
        val skeletonType = SkeletonType.COMPENSATION_REVISION_BACKFILL
        assertEquals(false, skeletonType.excludeInactiveConfigs)
        assertEquals(
            listOf(
                CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_ADDITIONAL,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP,
                CategoryConstants.CATEGORY_MONTH_PAY_13TH_14TH,
                CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE,
                CategoryConstants.CATEGORY_EMPLOYER_CONTRIBUTION,
                CategoryConstants.CATEGORY_EMPLOYEE_CONTRIBUTION,
                CategoryConstants.CATEGORY_EMPLOYEE_DEDUCTION,
                CategoryConstants.CATEGORY_EMPLOYER_DEDUCTION,
                CategoryConstants.CATEGORY_PAY_SUPPLEMENT,
                CategoryConstants.CATEGORY_ARREARS_BASE_PAY_BREAKUP,
            ),
            skeletonType.compensationCategories,
        )
    }
}
