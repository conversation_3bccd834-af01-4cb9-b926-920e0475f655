plugins {
    alias(kt.plugins.jvm)
    alias(kt.plugins.spring)
}

dependencies {
    // Project
    implementation(projects.service)

    // Multiplier
    implementation(multiplier.transaction.graphql.dgs)
    implementation(multiplier.graphql.spring)
    implementation(multiplier.growthbook.sdk)
    implementation(multiplier.logging.keys)
    implementation(multiplier.common.transport.spring)
    implementation(multiplier.access.control.spring)

    implementation(multiplier.compensation.service.graph) {
        exclude("com.netflix.graphql.dgs", "graphql-dgs-platform-dependencies")
    }

    // Kotlin
    implementation(kt.coroutines.slf4j)
    implementation(kt.coroutines.core)

    // Spring
    implementation(spring.boot.starter)
    implementation(spring.boot.starter.security)

    // GraphQL
    implementation(libs.graphql.dgs.spring.boot.starter)
    implementation(libs.graphql.dgs.extended.scalars)

    // Logging
    implementation(kt.logging)
    testRuntimeOnly(libs.logback.classic)

    // Serialization
    implementation(libs.java.uuid.generator)

    // Test
    testImplementation(kt.test.junit5)
    testImplementation(test.assertk)
    testImplementation(test.mockk)
    testImplementation(test.spring.security.test)
}
