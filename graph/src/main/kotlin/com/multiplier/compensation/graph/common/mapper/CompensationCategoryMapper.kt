package com.multiplier.compensation.graph.common.mapper

import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.graph.types.CompensationCategory

fun CompensationCategory.toDomain(): String = when (this) {
    CompensationCategory.CONTRACT_ALLOWANCE -> CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE
    CompensationCategory.CONTRACT_BASE_PAY -> CategoryConstants.CATEGORY_CONTRACT_BASE_PAY
    CompensationCategory.CONTRACT_BASE_PAY_ADDITIONAL -> CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_ADDITIONAL
    CompensationCategory.CONTRACT_BASE_PAY_BREAKUP -> CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP
    CompensationCategory.MONTH_PAY_13TH_14TH -> CategoryConstants.CATEGORY_MONTH_PAY_13TH_14TH
    CompensationCategory.EMPLOYER_CONTRIBUTION -> CategoryConstants.CATEGORY_EMPLOYER_CONTRIBUTION
    CompensationCategory.EMPLOYEE_CONTRIBUTION -> CategoryConstants.CATEGORY_EMPLOYEE_CONTRIBUTION
    CompensationCategory.PAY_SUPPLEMENT -> CategoryConstants.CATEGORY_PAY_SUPPLEMENT
    CompensationCategory.EMPLOYEE_DEDUCTION -> CategoryConstants.CATEGORY_EMPLOYEE_DEDUCTION
    CompensationCategory.EMPLOYER_DEDUCTION -> CategoryConstants.CATEGORY_EMPLOYER_DEDUCTION
    CompensationCategory.TOTAL_COST_TO_COMPANY -> CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY
    CompensationCategory.ARREARS_BASE_PAY_BREAKUP -> CategoryConstants.CATEGORY_ARREARS_BASE_PAY_BREAKUP
}
