package com.multiplier.compensation.graph.common.mapper

import com.multiplier.compensation.domain.common.OfferingCode
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.graph.types.OfferingCode as OfferingCodeGraph

fun OfferingCodeGraph.toDomain(): OfferingCode = when (this) {
    OfferingCodeGraph.EOR -> OfferingCode.EOR
    OfferingCodeGraph.GLOBAL_PAYROLL -> OfferingCode.GLOBAL_PAYROLL
    else -> throw InvalidArgumentException(
        errorCode = ValidationErrorCode.InvalidOfferingCode,
        message = "Invalid offering code: $this",
        context = mapOf("offeringCode" to this),
    )
}

fun OfferingCodeGraph.toOfferingType(): OfferingType = when (this) {
    OfferingCodeGraph.EOR -> OfferingType.EOR
    OfferingCodeGraph.GLOBAL_PAYROLL -> OfferingType.GLOBAL_PAYROLL
    else -> throw InvalidArgumentException(
        errorCode = ValidationErrorCode.InvalidOfferingCode,
        message = "Invalid offering code: $this",
        context = mapOf("offeringCode" to this),
    )
}
