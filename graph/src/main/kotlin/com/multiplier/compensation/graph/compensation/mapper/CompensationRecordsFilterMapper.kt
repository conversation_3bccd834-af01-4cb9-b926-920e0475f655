package com.multiplier.compensation.graph.compensation.mapper

import com.multiplier.compensation.domain.compensation.CompensationRecordsFilter
import com.multiplier.graph.types.CompensationRecordsFilter as GraphCompensationRecordsFilter

fun GraphCompensationRecordsFilter.toDto() = CompensationRecordsFilter(
    entityId = this.entityId,
    companyId = this.companyId,
    contractIds = this.contractIds,
    startDate = this.startDate,
    endDate = this.endDate,
    categories = this.categories,
    state = this.status?.toDto(),
    activeAsOn = this.activeAsOn,
    recordTypes = this.recordTypes?.map { it.toDto() },
)
