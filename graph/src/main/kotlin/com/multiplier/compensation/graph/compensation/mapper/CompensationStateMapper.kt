package com.multiplier.compensation.graph.compensation.mapper

import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.compensation.enums.CompensationState
import com.multiplier.graph.types.CompensationState as GraphCompensationState

fun GraphCompensationState.toDto(): CompensationState = when (this) {
    GraphCompensationState.UPCOMING -> CompensationState.UPCOMING
    GraphCompensationState.PROCESSING -> CompensationState.PROCESSING
    GraphCompensationState.COMPLETED -> CompensationState.COMPLETED
    GraphCompensationState.ACTIVE -> CompensationState.ACTIVE
    else -> throw InvalidArgumentException(
        errorCode = ValidationErrorCode.InvalidCompensationState,
        message = "Unknown compensation state $this",
    )
}
