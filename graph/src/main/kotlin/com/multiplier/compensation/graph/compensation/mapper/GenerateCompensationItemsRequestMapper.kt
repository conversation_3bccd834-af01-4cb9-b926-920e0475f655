package com.multiplier.compensation.graph.compensation.mapper

import com.multiplier.compensation.domain.compensationitem.GenerateCompensationItemsForCompensationsRequest
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemGenerationResponse
import com.multiplier.graph.types.GenerateCompensationItemsRequest
import com.multiplier.graph.types.TaskResponse

fun GenerateCompensationItemsRequest.toDto() = GenerateCompensationItemsForCompensationsRequest(
    compensationIds = this.compensationIds,
    shouldPersist = true,
)

fun CompensationItemGenerationResponse.toGraph() = TaskResponse(
    success = this.isSuccessful,
    message = this.message,
)
