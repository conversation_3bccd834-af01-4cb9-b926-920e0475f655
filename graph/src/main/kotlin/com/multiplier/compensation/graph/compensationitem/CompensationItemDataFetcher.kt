package com.multiplier.compensation.graph.compensationitem

import com.multiplier.compensation.graph.compensationitem.mapper.toCompensationDetails
import com.multiplier.compensation.graph.compensationitem.mapper.toDomain
import com.multiplier.compensation.service.common.service.CompensationDomainAccessService
import com.multiplier.compensation.service.compensation.CompensationService
import com.multiplier.compensation.service.compensationitem.CompensationItemService
import com.multiplier.graph.types.CompensationItemsFilter
import com.multiplier.graph.types.CompensationItemsResponse
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment
import com.netflix.graphql.dgs.DgsQuery
import com.netflix.graphql.dgs.InputArgument
import io.github.oshai.kotlinlogging.KotlinLogging
import java.nio.file.AccessDeniedException

private val log = KotlinLogging.logger {}

@DgsComponent
class CompensationItemDataFetcher(
    private val compensationDomainAccessService: CompensationDomainAccessService,
    private val compensationItemService: CompensationItemService,
    private val compensationService: CompensationService,
) {
    @DgsQuery
    fun compensationItems(
        @InputArgument filter: CompensationItemsFilter,
        dfe: DgsDataFetchingEnvironment,
    ): CompensationItemsResponse {
        if (filter.contractIds.isEmpty() && filter.compensationIds.isEmpty()) {
            log.warn { "Contract IDs / Compensation IDs should be provided" }
            throw IllegalArgumentException("Contract IDs / Compensation IDs should be provided")
        }

        val (accessibleCompanyIds, accessibleContractIds) = getAccessibleCompaniesAndContracts(filter, dfe)

        if (accessibleCompanyIds.isEmpty() && accessibleContractIds.isEmpty()) {
            log.warn {
                "Neither company ID ${filter.companyId} " +
                    "nor contract IDs ${filter.contractIds} are accessible by the user"
            }
            throw AccessDeniedException("The company and / or contracts are not accessible by the user")
        }

        val compensationItemsByContract = compensationItemService.getCompensationItemsByContract(
            filter.toDomain(accessibleContractIds),
        )

        val compensationDetailsList = compensationItemsByContract.toCompensationDetails()

        return CompensationItemsResponse(compensationDetailsList)
    }

    private fun getAccessibleCompaniesAndContracts(
        filter: CompensationItemsFilter,
        dfe: DgsDataFetchingEnvironment,
    ): Pair<Set<Long>, Set<Long>> {
        val accessibleCompanyIds = filter.companyId?.let {
            compensationDomainAccessService.getCompensationDomainCompanyAccess(listOf(it), dfe)
        }.orEmpty().toSet()

        val contractIds = filter.contractIds + if (filter.compensationIds.isNotEmpty()) {
            val compensationsByIds = compensationService.getCompensationsByIds(filter.compensationIds)
            compensationsByIds.map { it.contractId }
        } else {
            emptyList()
        }

        val accessibleContractIds = compensationDomainAccessService.getCompensationDomainContractAccess(
            contractIds = contractIds.toSet(),
            dfe = dfe,
        ).toSet()

        return Pair(accessibleCompanyIds, accessibleContractIds)
    }
}
