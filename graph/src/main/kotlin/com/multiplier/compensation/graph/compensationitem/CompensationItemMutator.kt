package com.multiplier.compensation.graph.compensationitem

import com.multiplier.compensation.domain.common.exception.CompensationServiceException
import com.multiplier.compensation.domain.common.exception.config.CompensationServiceErrorCode
import com.multiplier.compensation.graph.compensationitem.mapper.toDomain
import com.multiplier.compensation.graph.compensationitem.mapper.toGraph
import com.multiplier.compensation.service.common.service.CompensationDomainAccessService
import com.multiplier.compensation.service.compensationitem.CompensationItemRevokeService
import com.multiplier.compensation.service.compensationitem.CompensationItemService
import com.multiplier.compensation.service.compensationitem.CompensationItemUserUpdateService
import com.multiplier.graph.types.RevokeCompensationItemsRequest
import com.multiplier.graph.types.RevokeCompensationItemsResponse
import com.multiplier.graph.types.UpdateCompensationItemsRequest
import com.multiplier.graph.types.UpdateCompensationItemsResponse
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment
import com.netflix.graphql.dgs.DgsMutation

@DgsComponent
class CompensationItemMutator(
    private val compensationItemService: CompensationItemService,
    private val updateService: CompensationItemUserUpdateService,
    private val revokeCompensationItemService: CompensationItemRevokeService,
    private val compensationDomainAccessService: CompensationDomainAccessService,
) {
    @DgsMutation
    fun updateCompensationItems(
        request: UpdateCompensationItemsRequest,
        dfe: DgsDataFetchingEnvironment,
    ): UpdateCompensationItemsResponse = try {
        val compensationItemIds = request.updates.map { it.compensationItemId }
        val compensationItemsByIds = compensationItemService.getCompensationItemsByIds(compensationItemIds)
        val contractIds = compensationItemsByIds.map { it.contractId }
        val accessibleContractIds = compensationDomainAccessService.getCompensationDomainContractAccess(
            contractIds,
            dfe,
        )

        updateService.update(request.toDomain(accessibleContractIds, compensationItemsByIds)).toGraph()
    } catch (ex: Exception) {
        throw CompensationServiceException(
            errorCode = CompensationServiceErrorCode.CompensationItemUpdateFailed,
            message = "Failed to update compensation items",
            context = mapOf("request" to request.toString()),
            exception = ex,
        )
    }

    @DgsMutation
    fun revokeCompensationItems(
        request: RevokeCompensationItemsRequest,
        dfe: DgsDataFetchingEnvironment,
    ): RevokeCompensationItemsResponse = try {
        val compensationItemIds = request.compensationItemIds
        val compensationItemsByIds = compensationItemService.getCompensationItemsByIds(compensationItemIds)
        val contractIds = compensationItemsByIds.map { it.contractId }
        val accessibleContractIds = compensationDomainAccessService.getCompensationDomainContractAccess(
            contractIds,
            dfe,
        )
        revokeCompensationItemService.revoke(request.toDomain(accessibleContractIds, compensationItemsByIds)).toGraph()
    } catch (ex: Exception) {
        throw CompensationServiceException(
            errorCode = CompensationServiceErrorCode.CompensationItemRevokeFailed,
            message = "Failed to revoke compensation items",
            context = mapOf("request" to request.toString()),
            exception = ex,
        )
    }
}
