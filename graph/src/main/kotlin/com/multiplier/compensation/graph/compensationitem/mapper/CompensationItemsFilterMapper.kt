package com.multiplier.compensation.graph.compensationitem.mapper

import com.multiplier.compensation.domain.compensation.CompensationItemsFilter
import com.multiplier.graph.types.CompensationItemsFilter as GraphCompensationItemsFilter

fun GraphCompensationItemsFilter.toDomain(filteredContractIds: Set<Long> = emptySet()) = CompensationItemsFilter(
    contractIds = filteredContractIds,
    compensationIds = this.compensationIds.toSet(),
    includedCategories = this.categories.toSet(),
    states = this.states.map { it.toDomain() }.toSet(),
)
