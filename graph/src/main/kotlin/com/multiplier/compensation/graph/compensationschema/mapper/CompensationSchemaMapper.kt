package com.multiplier.compensation.graph.compensationschema.mapper

import com.multiplier.compensation.domain.compensationschema.CompensationSchema
import com.multiplier.compensation.graph.common.mapper.toDomain
import com.multiplier.compensation.graph.common.mapper.toDto
import com.multiplier.compensation.graph.common.mapper.toGraphqlResult
import com.multiplier.compensation.graph.common.mapper.toGraphqlStatus
import com.multiplier.compensation.graph.common.mapper.toKeyValueMap
import com.multiplier.compensation.graph.common.mapper.toOfferingType
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaBulkUpsertRequest
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaBulkUpsertResponse
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaRequest
import com.multiplier.compensation.service.compensationschema.mapper.CompensationSchemaSkeletonField
import com.multiplier.graph.types.CompensationComponentDetails
import com.multiplier.graph.types.CompensationDomainBulkUpsertRequest
import com.multiplier.graph.types.CompensationDomainBulkUpsertResponse
import com.multiplier.graph.types.CompensationSchemaResponse
import com.multiplier.graph.types.FieldType
import com.multiplier.graph.types.RecordDetails
import java.util.UUID
import com.multiplier.graph.types.CompensationSchemaRequest as GraphCompensationSchemaRequest

fun mapToCompensationSchemaResponse(
    schema: CompensationSchema,
    payScheduleNamesMap: Map<UUID, String> = emptyMap(),
): CompensationSchemaResponse = CompensationSchemaResponse(
    entityId = schema.entityId,
    compensationSchemaRecordDetails = schema.schemaItems.map { schemaItem ->
        RecordDetails(
            id = schemaItem.id.toString(),
            componentDetails = CompensationSchemaSkeletonField.entries.map { field ->
                CompensationComponentDetails(
                    key = field.id,
                    value = when (field) {
                        CompensationSchemaSkeletonField.COMPONENT_NAME -> schemaItem.componentName
                        CompensationSchemaSkeletonField.CATEGORY -> schemaItem.category
                        CompensationSchemaSkeletonField.IS_TAXABLE -> schemaItem.isTaxable.toString()
                        CompensationSchemaSkeletonField.IS_PRORATED -> schemaItem.isProrated.toString()
                        CompensationSchemaSkeletonField.IS_FIXED -> schemaItem.isFixed.toString()
                        CompensationSchemaSkeletonField.IS_ACTIVE -> schemaItem.isActive.toString()
                        CompensationSchemaSkeletonField.IS_MANDATORY -> schemaItem.isMandatory.toString()
                        CompensationSchemaSkeletonField.IS_PART_OF_BASE_PAY,
                        -> schemaItem.isPartOfBasePay.toString()
                        CompensationSchemaSkeletonField.SCHEMA_DESCRIPTION -> schema.description
                        CompensationSchemaSkeletonField.COUNTRY_CODE -> schema.country.toString()
                        CompensationSchemaSkeletonField.SCHEMA_NAME -> schema.name
                        CompensationSchemaSkeletonField.TAGS -> schema.tags.joinToString(",")
                        CompensationSchemaSkeletonField.LABEL -> schemaItem.label
                        CompensationSchemaSkeletonField.ITEM_TYPE -> schemaItem.itemType.toString()
                        CompensationSchemaSkeletonField.VALIDATION -> schemaItem.validation
                        CompensationSchemaSkeletonField.CALCULATION -> schemaItem.calculation
                        CompensationSchemaSkeletonField.BILLING_RATE_TYPE -> schemaItem.billingRateType.toString()
                        CompensationSchemaSkeletonField.IS_OVERTIME_ELIGIBLE ->
                            schemaItem.isOvertimeEligible.toString()
                        CompensationSchemaSkeletonField.COMPONENT_DESCRIPTION -> schemaItem.description
                        CompensationSchemaSkeletonField.BILLING_FREQUENCY -> schemaItem.billingFrequency?.toString()
                        CompensationSchemaSkeletonField.PAY_SCHEDULE_NAME ->
                            payScheduleNamesMap[schemaItem.id]
                        CompensationSchemaSkeletonField.CURRENCY -> schemaItem.currency
                        CompensationSchemaSkeletonField.IS_PART_OF_CTC -> schemaItem.isPartOfCtc.toString()
                        CompensationSchemaSkeletonField.IS_DEFAULT -> schema.isDefault.toString()
                        CompensationSchemaSkeletonField.CONFIGURATION_SCOPE -> schema.configurationScope.toString()
                    },
                    type = when (field) {
                        CompensationSchemaSkeletonField.IS_TAXABLE,
                        CompensationSchemaSkeletonField.IS_PRORATED,
                        CompensationSchemaSkeletonField.IS_FIXED,
                        CompensationSchemaSkeletonField.IS_ACTIVE,
                        CompensationSchemaSkeletonField.IS_MANDATORY,
                        CompensationSchemaSkeletonField.IS_PART_OF_BASE_PAY,
                        CompensationSchemaSkeletonField.IS_OVERTIME_ELIGIBLE,
                        CompensationSchemaSkeletonField.IS_PART_OF_CTC,
                        CompensationSchemaSkeletonField.IS_DEFAULT,
                        -> FieldType.BOOLEAN

                        else -> FieldType.STRING
                    },
                )
            },
        )
    },
)

fun mapToCompensationSchemaResponseList(
    schemas: List<CompensationSchema>,
    payScheduleNamesMap: Map<UUID, String> = emptyMap(),
): List<CompensationSchemaResponse> =
    schemas.map { schema -> mapToCompensationSchemaResponse(schema, payScheduleNamesMap) }

fun CompensationDomainBulkUpsertRequest.toDto(): CompensationSchemaBulkUpsertRequest =
    CompensationSchemaBulkUpsertRequest(
        entityId = this.entityId,
        commitPartially = this.commitPartially,
        customParams = this.customParams.toKeyValueMap(),
        rowItems = this.bulkUpsertInput.map { it.toDto() },
    )

fun CompensationSchemaBulkUpsertResponse.toGraphqlResponse(): CompensationDomainBulkUpsertResponse =
    CompensationDomainBulkUpsertResponse(
        entityId = this.entityId,
        status = this.status.toGraphqlStatus(),
        recordValidationResults = this.rowValidationResults.map { it.toGraphqlResult() },
    )

fun GraphCompensationSchemaRequest.toDto(accessibleEntityIds: List<Long>): CompensationSchemaRequest =
    CompensationSchemaRequest(
        entityId = accessibleEntityIds.first(),
        offeringType = this.offeringType.toOfferingType(),
        country = this.country?.toDomain(),
        returnDefaultOnly = this.returnDefaultOnly ?: true,
    )
