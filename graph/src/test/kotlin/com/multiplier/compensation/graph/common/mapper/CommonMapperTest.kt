package com.multiplier.compensation.graph.common.mapper

import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.RowValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.graph.types.CompensationUpsertInput
import com.multiplier.graph.types.FieldType
import com.multiplier.graph.types.KeyValueInput
import com.multiplier.graph.types.OperationStatus
import com.multiplier.graph.types.ResultType
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import com.multiplier.compensation.service.common.dto.OperationStatus as DomainOperationStatus

class CommonMapperTest {
    @Test
    fun `test CompensationUpsertInput to RowItem mapping`() {
        val keyValueInput = KeyValueInput("key", "value")
        val compensationUpsertInput = CompensationUpsertInput("1", listOf(keyValueInput))

        val result = compensationUpsertInput.toDto()

        assertEquals("1", result.id)
        assertEquals(1, result.keyValuePairs.size)
        assertEquals("key", result.keyValuePairs[0].key)
        assertEquals("value", result.keyValuePairs[0].value)
    }

    @Test
    fun `test KeyValueInput to KeyValuePair mapping`() {
        val keyValueInput = KeyValueInput("key", "value")

        val result = keyValueInput.toDto()

        assertEquals("key", result.key)
        assertEquals("value", result.value)
    }

    @Test
    fun `test DomainOperationStatus to GraphQL Status`() {
        assertEquals(OperationStatus.SUCCESS, DomainOperationStatus.SUCCESS.toGraphqlStatus())
        assertEquals(OperationStatus.FAILURE, DomainOperationStatus.FAILURE.toGraphqlStatus())
        assertEquals(OperationStatus.PARTIAL_SUCCESS, DomainOperationStatus.PARTIAL_SUCCESS.toGraphqlStatus())

        assertFailsWith<IllegalArgumentException> {
            DomainOperationStatus.valueOf("").toGraphqlStatus() // Assuming UNKNOWN is not mapped
        }
    }

    @Test
    fun `test RowValidationResult to GraphQL RecordValidationResult`() {
        val cellValidationResult =
            CellValidationResult(KeyValuePair("field", "value"), ValidationResultType.ERROR, "Error message")
        val rowValidationResult = RowValidationResult("1", listOf(cellValidationResult))

        val result = rowValidationResult.toGraphqlResult()

        assertEquals("1", result.id)
        assertEquals(1, result.componentValidationResults.size)
        assertEquals(ResultType.ERROR, result.componentValidationResults[0].type)
        assertEquals("Error message", result.componentValidationResults[0].message)
        assertEquals("field", result.componentValidationResults[0].componentDetails.key)
        assertEquals("value", result.componentValidationResults[0].componentDetails.value)
    }

    @Test
    fun `test CellValidationResult to GraphQL ComponentValidationResult`() {
        val cellValidationResult =
            CellValidationResult(KeyValuePair("field", "value"), ValidationResultType.INFO, "Info message")

        val result = cellValidationResult.toGraphqlItemFieldInfo()

        assertEquals(ResultType.INFO, result.type)
        assertEquals("Info message", result.message)
        assertEquals("field", result.componentDetails.key)
        assertEquals("value", result.componentDetails.value)
    }

    @Test
    fun `test ValidationResultType to GraphQL ResultType`() {
        assertEquals(ResultType.INFO, ValidationResultType.INFO.toGraphqlValidationResultType())
        assertEquals(ResultType.WARN, ValidationResultType.WARN.toGraphqlValidationResultType())
        assertEquals(ResultType.ERROR, ValidationResultType.ERROR.toGraphqlValidationResultType())

        assertFailsWith<IllegalArgumentException> {
            ValidationResultType.valueOf("").toGraphqlValidationResultType() // Assuming UNKNOWN is not mapped
        }
    }

    @Test
    fun `test KeyValuePair to GraphQL CompensationComponentDetails`() {
        val keyValuePair = KeyValuePair("COMPONENT_NAME", "Base Salary")

        val result = keyValuePair.toGraphqlKeyValuePair()

        assertEquals("COMPONENT_NAME", result.key)
        assertEquals("Base Salary", result.value)
        assertEquals(FieldType.STRING, result.type) // Assuming COMPONENT_NAME is mapped to STRING
    }

    @Test
    fun `test getFieldType with boolean, date, and integer keys`() {
        val booleanKeyValue = KeyValuePair("IS_TAXABLE", "Yes")
        val dateKeyValue = KeyValuePair("START_DATE", "2024-01-01")
        val integerKeyValue = KeyValuePair("PAY_DATE_RELATIVE_DAYS", "10")
        val stringKeyValue = KeyValuePair("COMPONENT_NAME", "Base Salary")

        assertEquals(FieldType.BOOLEAN, booleanKeyValue.getFieldType())
        assertEquals(FieldType.DATE, dateKeyValue.getFieldType())
        assertEquals(FieldType.INTEGER, integerKeyValue.getFieldType())
        assertEquals(FieldType.STRING, stringKeyValue.getFieldType())
    }

    @Test
    fun `test getFieldType with default string type`() {
        val stringKeyValue = KeyValuePair("UNMAPPED_KEY", "Some Value")

        assertEquals(FieldType.STRING, stringKeyValue.getFieldType())
    }
}
