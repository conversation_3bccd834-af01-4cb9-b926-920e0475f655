package com.multiplier.compensation.graph.compensation

import assertk.all
import assertk.assertThat
import assertk.assertions.isEqualTo
import com.multiplier.compensation.domain.common.exception.CompensationServiceException
import com.multiplier.compensation.domain.common.exception.config.CompensationServiceErrorCode
import com.multiplier.compensation.service.common.dto.OperationStatus
import com.multiplier.compensation.service.common.dto.ResultType
import com.multiplier.compensation.service.compensation.termination.BulkCompensationTerminationService
import com.multiplier.compensation.service.compensationinput.dto.UpdateCompensationStatusBulkResponse
import com.multiplier.compensation.service.compensationinput.dto.UpdateCompensationStatusResult
import com.multiplier.graph.types.BulkCompensationStatusUpdateRequest
import com.multiplier.graph.types.CompensationInputStatus
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import com.multiplier.graph.types.OperationStatus as GraphOperationStatus
import com.multiplier.graph.types.ResultType as GraphResultType

class CompensationStatusMutatorTest {
    private lateinit var bulkTerminationService: BulkCompensationTerminationService
    private lateinit var mutator: CompensationStatusMutator

    @BeforeEach
    fun setUp() {
        bulkTerminationService = mockk()
        mutator = CompensationStatusMutator(bulkTerminationService)
    }

    @Test
    fun `should terminate compensations successfully`() {
        val contractIds = listOf(1L, 2L)
        val request = BulkCompensationStatusUpdateRequest(
            contractIds = contractIds,
            status = CompensationInputStatus.TERMINATE,
        )

        every { bulkTerminationService.terminate(contractIds.toSet()) } returns buildSuccessfulMockResponse()

        val response = mutator.bulkUpdateCompensationStatus(request)

        verify(atLeast = 1) { bulkTerminationService.terminate(contractIds.toSet()) }

        assertThat(response).all {
            transform { it.status }.isEqualTo(GraphOperationStatus.SUCCESS)

            transform { it.results[0].resultType }.isEqualTo(GraphResultType.INFO)
            transform { it.results[0].contractId }.isEqualTo(1L)
            transform { it.results[0].message }.isEqualTo("Compensations are terminated for contract [1L]")

            transform { it.results[1].resultType }.isEqualTo(GraphResultType.INFO)
            transform { it.results[1].contractId }.isEqualTo(2L)
            transform { it.results[1].message }.isEqualTo("Compensations are terminated for contract [2L]")
        }
    }

    @Test
    fun `should terminate compensations for valid contracts only and return partial success`() {
        val contractIds = listOf(1L, 2L)
        val request = BulkCompensationStatusUpdateRequest(
            contractIds = contractIds,
            status = CompensationInputStatus.TERMINATE,
        )

        every { bulkTerminationService.terminate(contractIds.toSet()) } returns buildPartialSuccessMockResponse()

        val response = mutator.bulkUpdateCompensationStatus(request)

        verify(atLeast = 1) { bulkTerminationService.terminate(contractIds.toSet()) }

        assertThat(response).all {
            transform { it.status }.isEqualTo(GraphOperationStatus.PARTIAL_SUCCESS)

            transform { it.results[0].resultType }.isEqualTo(GraphResultType.INFO)
            transform { it.results[0].contractId }.isEqualTo(1L)
            transform { it.results[0].message }.isEqualTo("Compensations are terminated for contract [1L]")

            transform { it.results[1].resultType }.isEqualTo(GraphResultType.ERROR)
            transform { it.results[1].contractId }.isEqualTo(2L)
            transform { it.results[1].message }.isEqualTo("Off boarding is not completed for contract [2L]")
        }
    }

    @Test
    fun `should throw CompensationServiceException for unsupported action`() {
        val contractIds = listOf(1L, 2L)
        val request = BulkCompensationStatusUpdateRequest(
            contractIds = contractIds,
            status = CompensationInputStatus.ACTIVATE,
        )

        val exception = assertThrows<CompensationServiceException> {
            mutator.bulkUpdateCompensationStatus(request)
        }

        assertEquals(CompensationServiceErrorCode.CompensationStatusUpdateFailed, exception.errorCode)
        assertEquals(
            "Error occurred during bulk update compensation status [BulkCompensationStatusUpdateRequest(contractIds=[1, 2], status=ACTIVATE)]",
            exception.message,
        )
    }

    private fun buildSuccessfulMockResponse() = UpdateCompensationStatusBulkResponse(
        status = OperationStatus.SUCCESS,
        results = listOf(
            UpdateCompensationStatusResult(
                contractId = 1L,
                result = ResultType.INFO,
                message = "Compensations are terminated for contract [1L]",
            ),
            UpdateCompensationStatusResult(
                contractId = 2L,
                result = ResultType.INFO,
                message = "Compensations are terminated for contract [2L]",
            ),
        ),
    )

    private fun buildPartialSuccessMockResponse() = UpdateCompensationStatusBulkResponse(
        status = OperationStatus.PARTIAL_SUCCESS,
        results = listOf(
            UpdateCompensationStatusResult(
                contractId = 1L,
                result = ResultType.INFO,
                message = "Compensations are terminated for contract [1L]",
            ),
            UpdateCompensationStatusResult(
                contractId = 2L,
                result = ResultType.ERROR,
                message = "Off boarding is not completed for contract [2L]",
            ),
        ),
    )
}
