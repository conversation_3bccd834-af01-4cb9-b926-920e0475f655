package com.multiplier.compensation.grpc.client

import com.multiplier.compensation.domain.common.contract.ContractOffboarding
import com.multiplier.compensation.domain.common.exception.DownstreamServiceException
import com.multiplier.compensation.domain.common.exception.config.DownstreamServiceErrorCode
import com.multiplier.compensation.grpc.client.adapter.ContractOffboardingServiceAdapter
import com.multiplier.compensation.grpc.client.mapper.toDomain
import com.multiplier.contract.offboarding.schema.ContractOffboardingServiceGrpcKt
import com.multiplier.contract.offboarding.schema.getContractOffboardingsRequest
import io.github.oshai.kotlinlogging.KotlinLogging
import io.grpc.StatusRuntimeException
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
@Primary
@Profile("!(test|local)")
class ContractOffboardingServiceGrpcClient(
    private val stub: ContractOffboardingServiceGrpcKt.ContractOffboardingServiceCoroutineStub,
) : ContractOffboardingServiceAdapter {
    override suspend fun getContractOffboardings(contractIds: Set<Long>): Set<ContractOffboarding> = try {
        log.info { "Calling gRPC to get offboardings for contracts $contractIds" }
        val request = getContractOffboardingsRequest {
            this.contractIds += contractIds
        }
        stub.getContractOffboardings(request).contractOffBoardingList.map { it.toDomain() }.toSet()
    } catch (exception: StatusRuntimeException) {
        throw DownstreamServiceException(
            errorCode = DownstreamServiceErrorCode.ContractOffBoardingServiceFailed,
            message = "Error while getting offboardings for contracts $contractIds",
            exception = exception,
            context = mapOf(
                "contractIds" to contractIds,
            ),
        )
    }
}
