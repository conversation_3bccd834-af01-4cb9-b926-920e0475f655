package com.multiplier.compensation.grpc.client

import com.multiplier.compensation.domain.common.exception.DownstreamServiceException
import com.multiplier.compensation.domain.common.exception.config.DownstreamServiceErrorCode
import com.multiplier.compensation.grpc.client.adapter.PayrollSchemaServiceAdapter
import com.multiplier.payroll.schema.grpc.schema.ComponentModule
import com.multiplier.payroll.schema.grpc.schema.GetComponentCategoriesByModulesRequest
import com.multiplier.payroll.schema.grpc.schema.PayrollSchemaServiceGrpc
import io.github.oshai.kotlinlogging.KotlinLogging
import io.grpc.StatusRuntimeException
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
@Primary
@Profile("!(test|local)")
class PayrollSchemaServiceGrpcClient(
    private val stub: PayrollSchemaServiceGrpc.PayrollSchemaServiceBlockingStub,
) : PayrollSchemaServiceAdapter {
    override suspend fun getCompensationCategories(): List<String> {
        try {
            log.info { "Fetching compensation categories" }
            val request = GetComponentCategoriesByModulesRequest.newBuilder()
                .addModules(ComponentModule.COMPENSATION)
                .addModules(ComponentModule.CONTRIBUTION)
                .build()
            return stub.getComponentCategoriesByModules(request).categoriesList.map { it.type }
        } catch (exception: StatusRuntimeException) {
            throw DownstreamServiceException(
                errorCode = DownstreamServiceErrorCode.PayrollSchemaServiceFailed,
                message = "Error while fetching compensation categories",
                exception = exception,
            )
        }
    }
}
