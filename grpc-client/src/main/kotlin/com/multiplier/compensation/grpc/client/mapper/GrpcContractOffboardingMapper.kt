package com.multiplier.compensation.grpc.client.mapper

import com.multiplier.compensation.domain.common.contract.ContractOffBoardingStatus
import com.multiplier.compensation.domain.common.contract.ContractOffboarding
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.contract.offboarding.schema.ContractOffboardingStatus
import com.multiplier.grpc.common.toLocalDateTime
import com.multiplier.contract.offboarding.schema.ContractOffboarding as GrpcContractOffBoarding
import com.multiplier.contract.offboarding.schema.ContractOffboardingStatus as GrpcContractOffBoardingStatus

fun GrpcContractOffBoarding.toDomain() = ContractOffboarding(
    offboardingId = id,
    contractId = contractId,
    offBoardingStatus = contractOffBoardingStatus.toDomain(),
    lastWorkingDay = lastWorkingDay(),
)

private fun GrpcContractOffBoarding.lastWorkingDay() = when {
    hasLastWorkingDay() -> lastWorkingDay.toLocalDateTime().toLocalDate()
    else -> null
}

private fun GrpcContractOffBoardingStatus.toDomain() = when (this) {
    GrpcContractOffBoardingStatus.INITIATED -> ContractOffBoardingStatus.INITIATED
    GrpcContractOffBoardingStatus.FNF_PENDING -> ContractOffBoardingStatus.FNF_PENDING
    GrpcContractOffBoardingStatus.COMPLETED -> ContractOffBoardingStatus.COMPLETED
    GrpcContractOffBoardingStatus.CANCELLED -> ContractOffBoardingStatus.CANCELLED
    ContractOffboardingStatus.UNRECOGNIZED -> throw InvalidArgumentException(
        errorCode = ValidationErrorCode.InvalidContractOffboardingStatus,
        message = "Invalid contract offboarding status [$this]",
        context = mapOf(
            "contractOffBoardingStatus" to this,
        ),
    )
}
