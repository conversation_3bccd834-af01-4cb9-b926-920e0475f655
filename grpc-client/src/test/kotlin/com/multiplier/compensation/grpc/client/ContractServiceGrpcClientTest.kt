package com.multiplier.compensation.grpc.client

import com.google.type.Date
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.grpc.client.mapper.map
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractServiceGrpc
import com.multiplier.contract.schema.currency.Currency
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import kotlin.test.assertEquals

@ExtendWith(MockKExtension::class)
class ContractServiceGrpcClientTest {
    private lateinit var grpcContract: ContractOuterClass.Contract

    private val stub: ContractServiceGrpc.ContractServiceBlockingStub = mockk()
    private val client = ContractServiceGrpcClient(stub)

    @BeforeEach
    fun setup() {
        grpcContract = ContractOuterClass.Contract.newBuilder()
            .setId(1L)
            .setCompanyId(2L)
            .setMemberId(3L)
            .setEmployeeId("EMP123")
            .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
            .setStartOn(
                Date.newBuilder()
                    .setYear(LocalDate.now().year)
                    .setMonth(LocalDate.now().monthValue)
                    .setDay(LocalDate.now().dayOfMonth)
                    .build(),
            )
            .setCurrency(Currency.CurrencyCode.USD)
            .setCountry("USA")
            .setWorkplaceEntityId(100L)
            .build()
    }

    @Test
    fun `getContractsByEntityIdAndContractIds should call contract service`() = runBlocking {
        val contractIds = setOf(1L)
        val request = ContractOuterClass.ContractFilters.newBuilder()
            .addAllContractIds(contractIds)
            .build()
        val response = mockk<ContractOuterClass.ListOfContractsResponse>()

        every { stub.getContracts(request) } returns response
        every { response.contractsList } returns listOf(grpcContract)

        val result: List<Contract> = client.getContractsByIds(contractIds)

        verify { stub.getContracts(request) }
        assertEquals(listOf(grpcContract.map()), result)
    }
}
