package com.multiplier.compensation.grpc.client

import com.multiplier.compensation.domain.notification.NotificationType
import com.multiplier.compensation.domain.notification.PigeonEmailNotificationRequest
import io.mockk.junit5.MockKExtension
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class MockPigeonServiceGrpcClientTest {
    private val mockPigeonServiceGrpcClient = MockPigeonServiceGrpcClient()

    @Test
    fun `send should return mock notification ID`() = runBlocking {
        // Arrange
        val emailRequest = PigeonEmailNotificationRequest(
            type = NotificationType.CompensationVariablePayComponentsReminder,
            to = "<EMAIL>",
            from = "<EMAIL>",
            subject = "Test Subject",
        )

        // Act
        val result = mockPigeonServiceGrpcClient.send(emailRequest)

        // Assert
        assertEquals("mock-notification-id", result)
    }

    @Test
    fun `send should handle empty strings`() = runBlocking {
        // Arrange
        val emailRequest = PigeonEmailNotificationRequest(
            type = NotificationType.CompensationVariablePayComponentsReminder,
            to = "",
            from = "",
            subject = "",
        )

        // Act
        val result = mockPigeonServiceGrpcClient.send(emailRequest)

        // Assert
        assertEquals("mock-notification-id", result)
    }

    @Test
    fun `send should handle attachments`() = runBlocking {
        // Arrange
        val emailRequest = PigeonEmailNotificationRequest(
            type = NotificationType.CompensationVariablePayComponentsReminder,
            to = "<EMAIL>",
            from = "<EMAIL>",
            subject = "Test Subject",
            attachments = listOf(
                com.multiplier.compensation.domain.notification.EmailAttachment.File(
                    name = "test.pdf",
                    contentType = "application/pdf",
                    blob = byteArrayOf(1, 2, 3),
                ),
            ),
        )

        // Act
        val result = mockPigeonServiceGrpcClient.send(emailRequest)

        // Assert
        assertEquals("mock-notification-id", result)
    }

    @Test
    fun `send should handle different notification types`() = runBlocking {
        // Test with different notification types
        val notificationTypes = NotificationType.values()

        for (type in notificationTypes) {
            // Arrange
            val emailRequest = PigeonEmailNotificationRequest(
                type = type,
                to = "<EMAIL>",
                from = "<EMAIL>",
                subject = "Test Subject for $type",
            )

            // Act
            val result = mockPigeonServiceGrpcClient.send(emailRequest)

            // Assert
            assertEquals("mock-notification-id", result)
        }
    }
}
