package com.multiplier.compensation.grpc.mapper

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.grpc.client.mapper.toCountryCode
import com.multiplier.compensation.grpc.client.mapper.toGrpcCountryCode
import com.multiplier.country.schema.Country
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class CountryCodeMapperTest {
    @Test
    fun `test special case mapping from domain to gRPC`() {
        // Test the special case mapping for XKK
        assertEquals(Country.GrpcCountryCode.UNRECOGNIZED, CountryCode.XKK.toGrpcCountryCode())
    }

    @Test
    fun `test special case mapping from gRPC to domain throws exception`() {
        // Test that NULL_COUNTRY_CODE throws an exception
        val nullCountryCodeException = assertThrows<IllegalArgumentException> {
            Country.GrpcCountryCode.NULL_COUNTRY_CODE.toCountryCode()
        }
        assertEquals("NULL_COUNTRY_CODE is not supported", nullCountryCodeException.message)

        // Test that UNRECOGNIZED throws an exception
        val unrecognizedException = assertThrows<IllegalArgumentException> {
            Country.GrpcCountryCode.UNRECOGNIZED.toCountryCode()
        }
        assertEquals("UNRECOGNIZED is not supported", unrecognizedException.message)
    }

    @Test
    fun `test all domain CountryCode values are mapped to gRPC`() {
        // Ensure all CountryCode values have a mapping to GrpcCountryCode
        for (countryCode in CountryCode.entries) {
            val grpcCountryCode = countryCode.toGrpcCountryCode()

            // Special case for XKK which maps to UNRECOGNIZED
            if (countryCode == CountryCode.XKK) {
                assertEquals(Country.GrpcCountryCode.UNRECOGNIZED, grpcCountryCode)
            } else {
                // For all other cases, the name should match
                assertEquals(countryCode.name, grpcCountryCode.name)
            }
        }
    }

    @Test
    fun `test all gRPC CountryCode values are mapped to domain`() {
        // Ensure all GrpcCountryCode values (except special cases) have a mapping to CountryCode
        for (grpcCountryCode in Country.GrpcCountryCode.entries) {
            // Skip special cases that throw exceptions
            if (grpcCountryCode == Country.GrpcCountryCode.NULL_COUNTRY_CODE ||
                grpcCountryCode == Country.GrpcCountryCode.UNRECOGNIZED
            ) {
                continue
            }

            val countryCode = grpcCountryCode.toCountryCode()
            assertEquals(grpcCountryCode.name, countryCode.name)
        }
    }
}
