import com.google.protobuf.gradle.id

plugins {
    alias(kt.plugins.jvm)

    alias(libs.plugins.protobuf)

    id("maven-publish")
}

dependencies {
    // Multiplier
    implementation(multiplier.grpc.common)

    // GRPC
    compileOnly(libs.protobuf.kotlin)
    compileOnly(libs.grpc.kotlin.stub)
    compileOnly(libs.grpc.protobuf)
}

configure<com.google.protobuf.gradle.ProtobufExtension> {
    protoc {
        artifact = "com.google.protobuf:protoc:${libs.versions.protobuf.get()}"
    }
    plugins {
        id("grpc") {
            artifact = "io.grpc:protoc-gen-grpc-java:${libs.versions.grpc.get()}"
        }
        id("grpckt") {
            artifact = "io.grpc:protoc-gen-grpc-kotlin:${libs.versions.grpcKotlin.get()}:jdk8@jar"
        }
        id("doc") {
            artifact = "io.github.pseudomuto:protoc-gen-doc:${libs.versions.protocGenDoc.get()}"
        }
    }
    generateProtoTasks {
        all().configureEach {
            plugins {
                id("grpc")
                id("grpckt")
                id("doc")
            }
            builtins {
                id("kotlin")
            }
        }
    }
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    compilerOptions {
        jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17)
    }
}

plugins.withType<JavaPlugin> {
    configure<JavaPluginExtension> {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
}

plugins.withType<MavenPublishPlugin> {
    configure<PublishingExtension> {
        publications {
            val versionName: String by rootProject.extra
            create<MavenPublication>("maven") {
                groupId = "${rootProject.group}"
                artifactId = "${rootProject.name}-${project.name}"
                version = versionName

                from(components["java"])
            }
        }
    }
}
