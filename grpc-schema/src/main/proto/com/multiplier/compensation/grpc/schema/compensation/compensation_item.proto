syntax = "proto3";

package com.multiplier.compensation.grpc.schema;

import "com/multiplier/compensation/grpc/schema/common/common.proto";
import "com/multiplier/compensation/grpc/schema/payschedule/pay_schedule.proto";
import "com/multiplier/grpc/common/v1/uuid.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/datetime.proto";

option java_multiple_files = true;

enum CompensationItemStatus {
  UNDEFINED = 0;
  NEW = 1;
  INPUT_LOCKED = 2;
  PROCESSING = 3;
  PAID = 4;
  COMPLETED = 5;
  ABORTED = 6;
  DRAFT = 7;
  REVOKED = 8;
}

message CompensationItem {
  com.multiplier.grpc.common.v1.Uuid id = 1;
  int64 company_id = 2;
  int64 entity_id = 3;
  int64 contract_id = 4;
  string compensation_name = 6;
  string compensation_category = 7;
  string currency = 8;
  BillingRateType billing_rate_type = 9;
  optional double billing_rate = 10;
  BillingFrequency billing_frequency = 11;
  google.type.Date start_date = 12;
  optional google.type.Date end_date = 13;
  CompensationItemStatus status = 14;
  optional CompensationRequestType request_type = 15;
  optional string request_id = 16;
  google.type.Date expected_pay_date = 17;
  optional double calculated_amount = 18;
  optional google.type.Date cut_off_date = 19;
  string pay_schedule_name = 20;
  payschedule.PayScheduleFrequency payment_frequency = 21;
  optional uint32 number_of_installments = 22;
  optional uint32 current_installment = 23;
  bool is_arrear = 24;
  bool is_installment = 25;
  bool is_taxable = 26;
  bool is_fixed = 27;
  bool is_prorated = 28;
  bool is_mandatory = 29;
  bool is_part_of_base_pay = 30;
  google.protobuf.Timestamp created_on = 31;
  int64 created_by = 32;
  google.protobuf.Timestamp updated_on = 33;
  int64 updated_by = 34;
  string label = 35;
  optional com.multiplier.grpc.common.v1.Uuid arrear_of = 36;
  bool is_overtime_eligible = 37;
  bool is_part_of_ctc = 38;
  optional string notes = 39;
}

message CompensationItemsFilter {
  repeated com.multiplier.grpc.common.v1.Uuid ids = 1;
  repeated int64 contract_ids = 2;
  repeated CompensationItemStatus statuses = 3;
  repeated string included_categories = 4;
  repeated string excluded_categories = 5;
  optional google.type.DateTime updated_from_exclusive = 6;
  optional google.type.DateTime updated_to_inclusive = 7;
}

message CompensationItemsResponse {
  repeated CompensationItem items = 2;
}
