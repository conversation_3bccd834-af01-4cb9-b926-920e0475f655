package com.multiplier.compensation.grpc.server.config

import net.devh.boot.grpc.server.autoconfigure.GrpcAdviceAutoConfiguration
import net.devh.boot.grpc.server.autoconfigure.GrpcHealthServiceAutoConfiguration
import net.devh.boot.grpc.server.autoconfigure.GrpcReflectionServiceAutoConfiguration
import net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration
import net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration
import net.devh.boot.grpc.server.autoconfigure.GrpcServerMetricAutoConfiguration
import net.devh.boot.grpc.server.autoconfigure.GrpcServerSecurityAutoConfiguration
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.context.annotation.Configuration

@Configuration
@ImportAutoConfiguration(
    GrpcAdviceAutoConfiguration::class,
    GrpcHealthServiceAutoConfiguration::class,
    GrpcReflectionServiceAutoConfiguration::class,
    GrpcServerAutoConfiguration::class,
    GrpcServerFactoryAutoConfiguration::class,
    GrpcServerMetricAutoConfiguration::class,
    GrpcServerSecurityAutoConfiguration::class,
)
class GrpcServerConfig
