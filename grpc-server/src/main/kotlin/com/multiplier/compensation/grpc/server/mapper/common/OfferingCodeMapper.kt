package com.multiplier.compensation.grpc.server.mapper.common

import com.multiplier.compensation.domain.common.OfferingCode
import com.multiplier.compensation.grpc.schema.Common.OfferingType

fun OfferingType.toDomain(): OfferingCode = when (this) {
    OfferingType.EOR -> OfferingCode.EOR
    OfferingType.GLOBAL_PAYROLL -> OfferingCode.GLOBAL_PAYROLL
    else -> throw IllegalArgumentException("OfferingType: [$this] is not supported")
}
