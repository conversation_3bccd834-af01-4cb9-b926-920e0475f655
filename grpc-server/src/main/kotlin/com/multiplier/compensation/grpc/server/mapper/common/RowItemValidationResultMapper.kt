package com.multiplier.compensation.grpc.server.mapper.common

import com.multiplier.compensation.grpc.schema.cellValidationResult
import com.multiplier.compensation.grpc.schema.rowItemValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.RowValidationResult

fun RowValidationResult.toGrpc() = this.let { rowValidationResult ->
    rowItemValidationResult {
        id = rowValidationResult.id
        cellValidationResults.addAll(rowValidationResult.cellValidationResults.map { it.toGrpc() })
    }
}

fun CellValidationResult.toGrpc() = this.let { cellValidationResult ->
    cellValidationResult {
        keyValuePair = cellValidationResult.field.toGrpc()
        type = cellValidationResult.type.toGrpc()
        message = cellValidationResult.message
    }
}
