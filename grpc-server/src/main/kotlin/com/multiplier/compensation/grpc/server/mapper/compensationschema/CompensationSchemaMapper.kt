package com.multiplier.compensation.grpc.server.mapper.compensationschema

import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.grpc.schema.Common.RowItemInput
import com.multiplier.compensation.grpc.schema.compensationSchemaResponse
import com.multiplier.compensation.grpc.schema.keyValuePair
import com.multiplier.compensation.grpc.schema.rowItemInput
import com.multiplier.compensation.grpc.server.mapper.common.toGrpc
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.compensationschema.mapper.CompensationSchemaSkeletonField
import java.util.UUID
import com.multiplier.compensation.domain.compensationschema.CompensationSchema as CompensationSchemaDomainModel
import com.multiplier.compensation.grpc.schema.CompensationSchema.CompensationSchemaResponse as GrpcCompensationSchemaResponse

fun List<CompensationSchemaDomainModel>.toGrpc(
    payScheduleNamesMap: Map<UUID, String> = emptyMap(),
): GrpcCompensationSchemaResponse {
    val entityId = this.firstOrNull()?.entityId
        ?: throw InvalidArgumentException(
            errorCode = ValidationErrorCode.EntityNotFound,
            message = "Entity not found in input",
        )
    val schemaDetails = this.flatMap {
        it.schemaItems.map { item ->
            item.toGrpc(
                it.country,
                it.name,
                it.tags,
                it.description,
                it.isDefault,
                it.configurationScope,
                payScheduleNamesMap,
            )
        }
    }

    return compensationSchemaResponse {
        this.entityId = entityId
        this.rowItems.addAll(schemaDetails)
    }
}

fun CompensationSchemaItem.toGrpc(
    country: CountryCode,
    schemaName: String,
    tags: List<String>,
    schemaDescription: String?,
    isDefault: Boolean,
    configurationScope: ConfigurationScope,
    payScheduleNamesMap: Map<UUID, String> = emptyMap(),
): RowItemInput {
    val keyValuePairs = CompensationSchemaSkeletonField.entries.map { field ->
        KeyValuePair(
            key = field.id,
            value = when (field) {
                CompensationSchemaSkeletonField.COMPONENT_NAME -> this.componentName
                CompensationSchemaSkeletonField.CATEGORY -> this.category
                CompensationSchemaSkeletonField.IS_TAXABLE -> this.isTaxable.toString()
                CompensationSchemaSkeletonField.IS_PRORATED -> this.isProrated.toString()
                CompensationSchemaSkeletonField.IS_FIXED -> this.isFixed.toString()
                CompensationSchemaSkeletonField.IS_ACTIVE -> this.isActive.toString()
                CompensationSchemaSkeletonField.IS_MANDATORY -> this.isMandatory.toString()
                CompensationSchemaSkeletonField.IS_PART_OF_BASE_PAY -> this.isPartOfBasePay.toString()
                CompensationSchemaSkeletonField.COUNTRY_CODE -> country.toString()
                CompensationSchemaSkeletonField.SCHEMA_NAME -> schemaName
                CompensationSchemaSkeletonField.TAGS -> tags.joinToString(",")
                CompensationSchemaSkeletonField.LABEL -> this.label
                CompensationSchemaSkeletonField.ITEM_TYPE -> this.itemType.toString()
                CompensationSchemaSkeletonField.VALIDATION -> this.validation
                CompensationSchemaSkeletonField.CALCULATION -> this.calculation
                CompensationSchemaSkeletonField.BILLING_RATE_TYPE -> this.billingRateType.toString()
                CompensationSchemaSkeletonField.IS_OVERTIME_ELIGIBLE -> this.isOvertimeEligible.toString()
                CompensationSchemaSkeletonField.COMPONENT_DESCRIPTION -> this.description
                CompensationSchemaSkeletonField.BILLING_FREQUENCY -> this.billingFrequency.toString()
                CompensationSchemaSkeletonField.PAY_SCHEDULE_NAME -> payScheduleNamesMap[this.id]
                CompensationSchemaSkeletonField.CURRENCY -> this.currency
                CompensationSchemaSkeletonField.SCHEMA_DESCRIPTION -> schemaDescription
                CompensationSchemaSkeletonField.IS_PART_OF_CTC -> this.isPartOfCtc.toString()
                CompensationSchemaSkeletonField.IS_DEFAULT -> isDefault.toString()
                CompensationSchemaSkeletonField.CONFIGURATION_SCOPE -> configurationScope.toString()
            },
        )
    }
    return rowItemInput {
        this.id = <EMAIL>()
        this.keyValuePairs.addAll(keyValuePairs.map { it.toGrpc() })
    }
}

fun KeyValuePair.toGrpc() = this.let { keyValuePairDto ->
    keyValuePair {
        key = keyValuePairDto.key
        value = keyValuePairDto.value.toString()
    }
}
