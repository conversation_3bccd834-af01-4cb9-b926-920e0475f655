package com.multiplier.compensation.grpc.server.mapper.payschedule

import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.grpc.schema.payschedule.PayScheduleFrequency as GrpcPayScheduleFrequency

fun PayScheduleFrequency.toGrpc() = when (this) {
    PayScheduleFrequency.ANNUALLY -> GrpcPayScheduleFrequency.ANNUALLY
    PayScheduleFrequency.SEMI_ANNUALLY -> GrpcPayScheduleFrequency.SEMI_ANNUALLY
    PayScheduleFrequency.QUARTERLY -> GrpcPayScheduleFrequency.QUARTERLY
    PayScheduleFrequency.MONTHLY -> GrpcPayScheduleFrequency.MONTHLY
    PayScheduleFrequency.SEMI_MONTHLY -> GrpcPayScheduleFrequency.SEMI_MONTHLY
    PayScheduleFrequency.BI_WEEKLY -> GrpcPayScheduleFrequency.BI_WEEKLY
    PayScheduleFrequency.WEEKLY -> GrpcPayScheduleFrequency.WEEKLY
    PayScheduleFrequency.DAILY -> GrpcPayScheduleFrequency.DAILY
    PayScheduleFrequency.ONE_TIME -> GrpcPayScheduleFrequency.ONE_TIME
}

fun GrpcPayScheduleFrequency.toDomain(): PayScheduleFrequency = when (this) {
    GrpcPayScheduleFrequency.ANNUALLY -> PayScheduleFrequency.ANNUALLY
    GrpcPayScheduleFrequency.SEMI_ANNUALLY -> PayScheduleFrequency.SEMI_ANNUALLY
    GrpcPayScheduleFrequency.QUARTERLY -> PayScheduleFrequency.QUARTERLY
    GrpcPayScheduleFrequency.MONTHLY -> PayScheduleFrequency.MONTHLY
    GrpcPayScheduleFrequency.SEMI_MONTHLY -> PayScheduleFrequency.SEMI_MONTHLY
    GrpcPayScheduleFrequency.BI_WEEKLY -> PayScheduleFrequency.BI_WEEKLY
    GrpcPayScheduleFrequency.WEEKLY -> PayScheduleFrequency.WEEKLY
    GrpcPayScheduleFrequency.DAILY -> PayScheduleFrequency.DAILY
    GrpcPayScheduleFrequency.ONE_TIME -> PayScheduleFrequency.ONE_TIME
    GrpcPayScheduleFrequency.UNRECOGNIZED ->
        throw IllegalArgumentException("Pay Schedule Frequency: [$this] is not supported")
}
