package com.multiplier.compensation.grpc.server.service.bulkupload

import com.fasterxml.uuid.impl.TimeBasedEpochGenerator
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.grpc.server.mapper.bulkupload.toBulkDataResponse
import com.multiplier.compensation.grpc.server.mapper.bulkupload.toCompensationBulkBackFillValidationInput
import com.multiplier.compensation.grpc.server.mapper.bulkupload.toCompensationBulkCreationInput
import com.multiplier.compensation.grpc.server.mapper.bulkupload.toCompensationBulkValidationInput
import com.multiplier.compensation.grpc.server.mapper.bulkupload.toCompensationInputFilter
import com.multiplier.compensation.grpc.server.mapper.bulkupload.toCompensationSchemaBulkCreationInput
import com.multiplier.compensation.grpc.server.mapper.bulkupload.toCompensationSchemaBulkValidationInput
import com.multiplier.compensation.grpc.server.mapper.bulkupload.toGrpcUpsertBulkResponse
import com.multiplier.compensation.grpc.server.mapper.bulkupload.toGrpcValidateBulkResponse
import com.multiplier.compensation.grpc.server.mapper.bulkupload.toPayScheduleBulkCreationInput
import com.multiplier.compensation.grpc.server.mapper.bulkupload.toPayScheduleBulkValidationInput
import com.multiplier.compensation.grpc.server.mapper.bulkupload.toSkeletonType
import com.multiplier.compensation.grpc.server.mapper.skeleton.toGrpcFieldRequirements
import com.multiplier.compensation.service.common.bulkplatform.BulkJsonCustomParamsUtil.parseJsonCustomParams
import com.multiplier.compensation.service.compensation.BulkCompensationBackFillService
import com.multiplier.compensation.service.compensation.BulkCompensationCreationService
import com.multiplier.compensation.service.compensation.BulkCompensationRevisionBackFillService
import com.multiplier.compensation.service.compensation.BulkCompensationRevisionService
import com.multiplier.compensation.service.compensation.CompensationValidationService
import com.multiplier.compensation.service.compensationinput.CompensationInputService
import com.multiplier.compensation.service.compensationschema.CompensationSchemaService
import com.multiplier.compensation.service.compensationschema.CompensationSchemaValidationService
import com.multiplier.compensation.service.payschedule.BulkPayScheduleCreationService
import com.multiplier.compensation.service.payschedule.validation.PayScheduleValidationService
import com.multiplier.compensation.service.skeleton.SkeletonService
import com.multiplier.grpc.common.bulkupload.v1.BulkDataRequest
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsRequest
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsResponse
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkResponse
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkResponse
import org.springframework.stereotype.Service

@Service
class BulkUploadProcessor(
    private val bulkCompensationCreationService: BulkCompensationCreationService,
    private val bulkCompensationRevisionService: BulkCompensationRevisionService,
    private val bulkCompensationRevisionBackFillService: BulkCompensationRevisionBackFillService,
    private val compensationInputService: CompensationInputService,
    private val compensationSchemaService: CompensationSchemaService,
    private val compensationSchemaValidationService: CompensationSchemaValidationService,
    private val compensationValidationService: CompensationValidationService,
    private val payScheduleCreationService: BulkPayScheduleCreationService,
    private val payScheduleValidationService: PayScheduleValidationService,
    private val skeletonService: SkeletonService,
    private val timeBasedEpochGenerator: TimeBasedEpochGenerator,
    private val bulkCompensationBackFillService: BulkCompensationBackFillService,
) {
    fun getRequirements(request: FieldRequirementsRequest): FieldRequirementsResponse = skeletonService.getSkeleton(
        entityId = request.entityId,
        skeletonType = request.useCase.toSkeletonType(),
        customParams = parseJsonCustomParams(request.jsonCustomParams),
    ).toGrpcFieldRequirements()

    fun validate(request: ValidateUpsertInputBulkRequest): ValidateUpsertInputBulkResponse =
        when (request.useCase.toSkeletonType()) {
            SkeletonType.COMPENSATION_SCHEMA -> compensationSchemaValidationService.validate(
                request.toCompensationSchemaBulkValidationInput(),
            ).toGrpcValidateBulkResponse(
                request = request,
                allowedSkeletonFields = skeletonService.getSkeletonFields(
                    skeletonType = request.useCase.toSkeletonType(),
                ),
            )

            SkeletonType.PAY_SCHEDULE -> payScheduleValidationService.validate(
                request.toPayScheduleBulkValidationInput(),
            ).toGrpcValidateBulkResponse(
                request = request,
                allowedSkeletonFields = skeletonService.getSkeletonFields(
                    skeletonType = request.useCase.toSkeletonType(),
                ),
            )

            SkeletonType.COMPENSATION_SETUP,
            SkeletonType.PAY_SUPPLEMENT,
            SkeletonType.DEDUCTION,
            SkeletonType.COMPENSATION_REVISION,
            -> compensationValidationService.validate(
                request.toCompensationBulkValidationInput(),
            ).toGrpcValidateBulkResponse(
                request = request,
                allowedSkeletonFields = skeletonService.getSkeletonFields(
                    skeletonType = request.useCase.toSkeletonType(),
                ),
            )

            SkeletonType.COMPENSATION_BACKFILL -> compensationValidationService.validate(
                request.toCompensationBulkBackFillValidationInput(),
            ).toGrpcValidateBulkResponse(
                request = request,
                allowedSkeletonFields = skeletonService.getSkeletonFields(
                    skeletonType = request.useCase.toSkeletonType(),
                ),
            )

            SkeletonType.COMPENSATION_REVISION_BACKFILL -> compensationValidationService.validate(
                request.toCompensationBulkBackFillValidationInput(),
            ).toGrpcValidateBulkResponse(
                request = request,
                allowedSkeletonFields = skeletonService.getSkeletonFields(
                    skeletonType = request.useCase.toSkeletonType(),
                ),
            )

            else -> throw InvalidArgumentException(
                errorCode = ValidationErrorCode.InvalidCompensationUseCase,
                message = "Invalid use case [${request.useCase}]",
                context = mapOf("useCase" to request.useCase),
            )
        }

    fun upsert(request: UpsertBulkRequest): UpsertBulkResponse = when (request.useCase.toSkeletonType()) {
        SkeletonType.COMPENSATION_SCHEMA -> compensationSchemaService.upsertCompensationSchema(
            request.toCompensationSchemaBulkCreationInput(),
        ).toGrpcUpsertBulkResponse(request)

        SkeletonType.PAY_SCHEDULE -> payScheduleCreationService.bulkCreatePaySchedules(
            request.toPayScheduleBulkCreationInput(),
        ).toGrpcUpsertBulkResponse(request)

        SkeletonType.COMPENSATION_SETUP,
        -> bulkCompensationCreationService.execute(
            request.toCompensationBulkCreationInput(timeBasedEpochGenerator),
        ).toGrpcUpsertBulkResponse(request)

        SkeletonType.PAY_SUPPLEMENT,
        SkeletonType.DEDUCTION,
        SkeletonType.COMPENSATION_REVISION,
        -> bulkCompensationRevisionService.execute(
            request.toCompensationBulkCreationInput(timeBasedEpochGenerator),
        ).toGrpcUpsertBulkResponse(request)

        SkeletonType.COMPENSATION_BACKFILL -> bulkCompensationBackFillService.execute(
            request.toCompensationBulkCreationInput(timeBasedEpochGenerator),
        ).toGrpcUpsertBulkResponse(request)

        SkeletonType.COMPENSATION_REVISION_BACKFILL -> bulkCompensationRevisionBackFillService.execute(
            request.toCompensationBulkCreationInput(timeBasedEpochGenerator),
        ).toGrpcUpsertBulkResponse(request)

        else -> throw InvalidArgumentException(
            errorCode = ValidationErrorCode.InvalidCompensationUseCase,
            message = "Invalid use case [${request.useCase}]",
            context = mapOf("useCase" to request.useCase),
        )
    }

    fun getFieldData(request: BulkDataRequest) = when (request.useCase.toSkeletonType()) {
        SkeletonType.COMPENSATION_SETUP -> compensationInputService.getCompensationInputEnrichedData(
            requestFilter = request.toCompensationInputFilter(),
        ).toBulkDataResponse()

        else -> throw InvalidArgumentException(
            errorCode = ValidationErrorCode.InvalidCompensationUseCase,
            message = "Use case [${request.useCase}] not allowed for get field data",
            context = mapOf("useCase" to request.useCase),
        )
    }
}
