package com.multiplier.compensation.grpc.server.service.bulkupload

import com.multiplier.common.logging.MDCKeys
import com.multiplier.grpc.common.bulkupload.v1.BulkDataRequest
import com.multiplier.grpc.common.bulkupload.v1.BulkDataResponse
import com.multiplier.grpc.common.bulkupload.v1.BulkUploadServiceGrpcKt
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsRequest
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsResponse
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkResponse
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkResponse
import com.multiplier.transaction.grpc.grpcApi
import io.github.oshai.kotlinlogging.KotlinLogging
import net.devh.boot.grpc.server.service.GrpcService
import kotlin.coroutines.CoroutineContext

private val log = KotlinLogging.logger {}

@GrpcService
class GrpcBulkUploadService(
    grpcContext: CoroutineContext,
    private val bulkUploadProcessor: BulkUploadProcessor,
) : BulkUploadServiceGrpcKt.BulkUploadServiceCoroutineImplBase(coroutineContext = grpcContext) {
    override suspend fun getFieldRequirements(request: FieldRequirementsRequest): FieldRequirementsResponse = grpcApi(
        MDCKeys.ENTITY_ID to "${request.entityId}",
        MDCKeys.USE_CASE to request.useCase,
    ) {
        log.info {
            "gRPC field requirements request for entity [${request.entityId}] and skeleton type [${request.useCase}]"
        }

        run {
            bulkUploadProcessor.getRequirements(request)
        }
    }

    override suspend fun bulkValidateUpsertInput(
        request: ValidateUpsertInputBulkRequest,
    ): ValidateUpsertInputBulkResponse = grpcApi(
        MDCKeys.ENTITY_ID to "${request.inputsList.map { it.keys.entityId }.toSet()}",
        MDCKeys.USE_CASE to request.useCase,
    ) {
        log.info {
            "gRPC bulk input validation request for use case [${request.useCase}]"
        }

        run {
            bulkUploadProcessor.validate(request)
        }
    }

    override suspend fun bulkUpsert(request: UpsertBulkRequest): UpsertBulkResponse = grpcApi(
        MDCKeys.ENTITY_ID to "${request.inputsList.mapNotNull { it.dataMap["ENTITY_ID"] }.toSet()}",
        MDCKeys.USE_CASE to request.useCase,
    ) {
        log.info {
            "gRPC bulk upsert request for use case [${request.useCase}]"
        }

        run {
            bulkUploadProcessor.upsert(request)
        }
    }

    override suspend fun getFieldData(request: BulkDataRequest): BulkDataResponse = grpcApi(
        MDCKeys.ENTITY_ID to "${request.entityIdsList.toSet()}",
        MDCKeys.USE_CASE to request.useCase,
    ) {
        log.info {
            "gRPC get field data request for entity ${request.entityIdsList.toSet()} " +
                "and skeleton type [${request.useCase}]"
        }

        run {
            bulkUploadProcessor.getFieldData(request)
        }
    }
}
