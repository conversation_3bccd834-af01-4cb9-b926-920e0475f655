package com.multiplier.compensation.grpc.server.mapper.bulkupload

import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.ValidationDraftObject
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.grpc.common.bulkupload.v1.PlatformKeys
import com.multiplier.grpc.common.bulkupload.v1.validateUpsertInputBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.validateUpsertInputRequest
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class ValidateBulkResponseMapperTest {
    companion object {
        private const val INPUT_ID = "1"
        private const val COMPONENT_NAME = "COMPONENT_NAME"
        private const val COMPONENT_NAME_VALUE = "Base Pay"
        private const val BILLING_RATE = "BILLING_RATE"
        private const val BILLING_RATE_VALUE = "1000"
        private const val DISALLOWED_FIELD = "COMPONENT_CATEGORY"
        private const val ERROR_MESSAGE = "Error message"
        private const val WARNING_MESSAGE = "Warning message"
        private const val INFO_MESSAGE = "Info message"
        private const val COUNTRY_CODE_USA = "USA"

        private const val ENTITY_ID = 1L
        private const val COMPANY_ID = 2L
        private const val CONTRACT_ID = 3L
    }

    @Test
    fun `given validation results with errors and warnings when mapped should contain all messages`() {
        val collector = ValidationDataCollector<ValidationDraftObject>(inputPlusDerivedRows = emptyMap()).apply {
            rowValidationResult[INPUT_ID] = mutableListOf(
                CellValidationResult(
                    KeyValuePair(COMPONENT_NAME, COMPONENT_NAME_VALUE),
                    ValidationResultType.ERROR,
                    ERROR_MESSAGE,
                ),
                CellValidationResult(
                    KeyValuePair(BILLING_RATE, BILLING_RATE_VALUE),
                    ValidationResultType.WARN,
                    WARNING_MESSAGE,
                ),
            )
        }

        val request = createBulkRequest(
            inputId = INPUT_ID,
            data = mapOf(COMPONENT_NAME to COMPONENT_NAME_VALUE, BILLING_RATE to BILLING_RATE_VALUE),
            skeletonType = SkeletonType.COMPENSATION_SETUP,
        )

        val response = collector.toGrpcValidateBulkResponse(
            request,
            setOf(COMPONENT_NAME, BILLING_RATE),
        )

        with(response.resultsList[0]) {
            assertFalse(success)
            assertEquals(2, messagesList.size)
            assertEquals(ERROR_MESSAGE, messagesList[0].errorsList[0])
            assertEquals(WARNING_MESSAGE, messagesList[1].warningsList[0])
        }
    }

    @Test
    fun `given validation results with only INFO when mapped should filter out messages`() {
        val collector = ValidationDataCollector<ValidationDraftObject>(inputPlusDerivedRows = emptyMap()).apply {
            rowValidationResult[INPUT_ID] = mutableListOf(
                CellValidationResult(
                    KeyValuePair(COMPONENT_NAME, COMPONENT_NAME_VALUE),
                    ValidationResultType.INFO,
                    INFO_MESSAGE,
                ),
            )
        }

        val request = createBulkRequest(
            inputId = INPUT_ID,
            data = mapOf(COMPONENT_NAME to COMPONENT_NAME_VALUE),
            skeletonType = SkeletonType.COMPENSATION_SETUP,
        )

        val response = collector.toGrpcValidateBulkResponse(request, setOf(COMPONENT_NAME))

        with(response.resultsList[0]) {
            assertTrue(success)
            assertTrue(messagesList.isEmpty())
        }
    }

    @Test
    fun `given platform keys when mapped should map based on skeleton type`() {
        val collector = ValidationDataCollector<ValidationDraftObject>(inputPlusDerivedRows = emptyMap())
        val request = createBulkRequest(
            inputId = INPUT_ID,
            countryCode = COUNTRY_CODE_USA,
            skeletonType = SkeletonType.COMPENSATION_SCHEMA,
        )

        val response = collector.toGrpcValidateBulkResponse(request, emptySet())

        with(response.resultsList[0].validatedInputDataMap) {
            assertEquals(ENTITY_ID.toString(), get(CommonSkeletonField.ENTITY_ID.name))
            assertEquals(COMPANY_ID.toString(), get(CommonSkeletonField.COMPANY_ID.name))
            assertEquals(COUNTRY_CODE_USA, get(CommonSkeletonField.COUNTRY_CODE.name))
        }
    }

    @Test
    fun `given input data when filtered should only include allowed skeleton fields`() {
        val collector = ValidationDataCollector<ValidationDraftObject>(inputPlusDerivedRows = emptyMap())
        val request = createBulkRequest(
            inputId = INPUT_ID,
            data = mapOf(
                COMPONENT_NAME to COMPONENT_NAME_VALUE,
                DISALLOWED_FIELD to "VALUE",
            ),
            skeletonType = SkeletonType.PAY_SCHEDULE,
        )

        val response = collector.toGrpcValidateBulkResponse(request, setOf(COMPONENT_NAME))

        with(response.resultsList[0].validatedInputDataMap) {
            assertTrue(containsKey(COMPONENT_NAME))
            assertFalse(containsKey(DISALLOWED_FIELD))
        }
    }

    private fun createBulkRequest(
        inputId: String,
        data: Map<String, String> = emptyMap(),
        skeletonType: SkeletonType,
        countryCode: String? = null,
    ) = validateUpsertInputBulkRequest {
        this.inputs.add(
            validateUpsertInputRequest {
                this.inputId = inputId
                this.data.putAll(data)
                keys = PlatformKeys.newBuilder()
                    .setEntityId(ENTITY_ID)
                    .setCompanyId(COMPANY_ID)
                    .apply {
                        countryCode?.let { setCountryCode(it) }
                        if (skeletonType == SkeletonType.COMPENSATION_REVISION) {
                            setContractId(CONTRACT_ID)
                        }
                    }
                    .build()
            },
        )
        useCase = skeletonType.name
    }
}
