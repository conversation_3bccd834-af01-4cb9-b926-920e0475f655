package com.multiplier.compensation.grpc.server.mapper.bulkupload.compensation

import com.google.type.Date
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.compensationitem.CompensationItemEnriched
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.grpc.schema.CompensationItem
import com.multiplier.compensation.grpc.schema.CompensationItemsResponse
import com.multiplier.compensation.grpc.server.mapper.common.toGrpc
import com.multiplier.compensation.grpc.server.mapper.compensation.toGrpc
import com.multiplier.compensation.grpc.server.mapper.payschedule.toGrpc
import com.multiplier.grpc.common.toDate
import com.multiplier.grpc.common.toTimestamp
import com.multiplier.grpc.common.v1.toGrpc
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class CompensationItemDataResponseMapperTest {
    @Test
    fun `toGrpc maps all fields correctly`() {
        // Given
        val compensationItemEnriched = CompensationItemEnriched(
            id = UUID.randomUUID(),
            companyId = 1L,
            entityId = 2L,
            contractId = 3L,
            category = "BONUS",
            schemaComponentName = "Performance Bonus",
            currency = "USD",
            billingRateType = BillingRateType.VALUE,
            billingRate = 5000.0,
            billingFrequency = BillingFrequency.MONTHLY,
            startDate = LocalDate.of(2025, 1, 1),
            endDate = LocalDate.of(2025, 12, 31),
            isInstallment = false,
            noOfInstallments = 12,
            createdOn = LocalDateTime.of(2025, 1, 1, 12, 0),
            createdBy = -1L,
            cutOffDate = LocalDate.of(2025, 6, 30),
            expectedPayDate = LocalDate.of(2025, 7, 15),
            calculatedAmount = 10000.0,
            payScheduleName = "Monthly Pay",
            payScheduleFrequency = PayScheduleFrequency.MONTHLY,
            isTaxable = true,
            isFixed = false,
            isProrated = true,
            isMandatory = false,
            isPartOfBasePay = true,
            payDate = LocalDate.of(2025, 7, 20),
            previousId = UUID.randomUUID(),
            status = CompensationItemStatus.NEW,
            isArrear = false,
            updatedOn = LocalDateTime.of(2025, 6, 1, 12, 0),
            updatedBy = -1L,
            currentInstallment = 6,
            label = "Performance Bonus",
            isOvertimeEligible = false,
            arrearOf = UUID.randomUUID(),
            isPartOfCtc = true,
            notes = "This is a test note.",
        )

        val expectedGrpcItem = CompensationItem.newBuilder()
            .setId(compensationItemEnriched.id.toGrpc())
            .setCompanyId(compensationItemEnriched.companyId)
            .setEntityId(compensationItemEnriched.entityId)
            .setContractId(compensationItemEnriched.contractId)
            .setCompensationName(compensationItemEnriched.schemaComponentName)
            .setCompensationCategory(compensationItemEnriched.category)
            .setCurrency(compensationItemEnriched.currency)
            .setBillingRateType(compensationItemEnriched.billingRateType.toGrpc())
            .apply { compensationItemEnriched.billingRate?.let { setBillingRate(it) } }
            .setBillingFrequency(compensationItemEnriched.billingFrequency.toGrpc())
            .setStartDate(compensationItemEnriched.startDate.toDate())
            .setEndDate(compensationItemEnriched.endDate!!.toDate())
            .setStatus(compensationItemEnriched.status.toGrpc())
            .setExpectedPayDate(compensationItemEnriched.expectedPayDate.toDate())
            .setCalculatedAmount(compensationItemEnriched.calculatedAmount!!)
            .setCutOffDate(compensationItemEnriched.cutOffDate!!.toDate())
            .setPayScheduleName(compensationItemEnriched.payScheduleName)
            .setPaymentFrequency(compensationItemEnriched.payScheduleFrequency.toGrpc())
            .setIsInstallment(compensationItemEnriched.isInstallment)
            .setNumberOfInstallments(compensationItemEnriched.noOfInstallments ?: 0)
            .setIsArrear(compensationItemEnriched.isArrear)
            .setArrearOf(compensationItemEnriched.arrearOf?.toGrpc())
            .setIsTaxable(compensationItemEnriched.isTaxable)
            .setIsFixed(compensationItemEnriched.isFixed)
            .setIsProrated(compensationItemEnriched.isProrated)
            .setIsMandatory(compensationItemEnriched.isMandatory)
            .setIsPartOfBasePay(compensationItemEnriched.isPartOfBasePay)
            .setLabel(compensationItemEnriched.label)
            .setIsOvertimeEligible(compensationItemEnriched.isOvertimeEligible)
            .setIsPartOfCtc(compensationItemEnriched.isPartOfCtc)
            .setNotes(compensationItemEnriched.notes)
            .setCreatedOn(compensationItemEnriched.createdOn.toTimestamp())
            .setCreatedBy(compensationItemEnriched.createdBy)
            .setUpdatedOn(compensationItemEnriched.updatedOn.toTimestamp())
            .setUpdatedBy(compensationItemEnriched.updatedBy)
            .build()

        // When
        val actualGrpcItem = compensationItemEnriched.toGrpc()

        // Then
        assertEquals(expectedGrpcItem, actualGrpcItem, "The toGrpc function did not correctly map all fields.")
    }

    @Test
    fun `toGrpc handles null fields correctly`() {
        // Given
        val compensationItemEnriched = CompensationItemEnriched(
            id = UUID.randomUUID(),
            companyId = 1L,
            entityId = 2L,
            contractId = 3L,
            category = "ALLOWANCE",
            schemaComponentName = "Housing Allowance",
            currency = "USD",
            billingRateType = BillingRateType.VALUE,
            billingRate = null,
            billingFrequency = BillingFrequency.MONTHLY,
            startDate = LocalDate.of(2025, 2, 1),
            endDate = null,
            isInstallment = false,
            noOfInstallments = null,
            createdOn = LocalDateTime.of(2025, 2, 1, 12, 0),
            createdBy = 1L,
            cutOffDate = null,
            expectedPayDate = LocalDate.of(2025, 3, 15),
            calculatedAmount = null,
            payScheduleName = "Bi-Weekly",
            payScheduleFrequency = PayScheduleFrequency.BI_WEEKLY,
            isTaxable = false,
            isFixed = true,
            isProrated = false,
            isMandatory = true,
            isPartOfBasePay = false,
            payDate = null,
            previousId = null,
            status = CompensationItemStatus.PROCESSING,
            isArrear = true,
            updatedOn = LocalDateTime.of(2025, 2, 15, 12, 0),
            updatedBy = 2L,
            currentInstallment = null,
            label = "Housing Allowance",
            isOvertimeEligible = false,
            arrearOf = UUID.randomUUID(),
            isPartOfCtc = false,
            notes = null,
        )

        // When
        val grpcItem = compensationItemEnriched.toGrpc()

        // Then
        assertEquals(Date.getDefaultInstance(), grpcItem.endDate)
        assertEquals(Date.getDefaultInstance(), grpcItem.cutOffDate)
        assertEquals(0.0, grpcItem.calculatedAmount)
        assertEquals(0, grpcItem.numberOfInstallments)
        assertEquals(0.0, grpcItem.billingRate)
    }

    @Test
    fun `toGrpc for list maps all fields correctly`() {
        // Given
        val compensationItemList = listOf(
            CompensationItemEnriched(
                id = UUID.randomUUID(),
                companyId = 1L,
                entityId = 2L,
                contractId = 3L,
                category = "ALLOWANCE",
                schemaComponentName = "Housing Allowance",
                currency = "USD",
                billingRateType = BillingRateType.VALUE,
                billingRate = 2000.0,
                billingFrequency = BillingFrequency.MONTHLY,
                startDate = LocalDate.of(2025, 2, 1),
                endDate = null,
                isInstallment = false,
                noOfInstallments = 6,
                createdOn = LocalDateTime.of(2025, 2, 1, 12, 0),
                createdBy = 1L,
                cutOffDate = null,
                expectedPayDate = LocalDate.of(2025, 3, 15),
                calculatedAmount = 6000.0,
                payScheduleName = "Bi-Weekly",
                payScheduleFrequency = PayScheduleFrequency.BI_WEEKLY,
                isTaxable = false,
                isFixed = true,
                isProrated = false,
                isMandatory = true,
                isPartOfBasePay = false,
                payDate = LocalDate.of(2025, 3, 20),
                previousId = null,
                status = CompensationItemStatus.PROCESSING,
                isArrear = true,
                updatedOn = LocalDateTime.of(2025, 2, 15, 12, 0),
                updatedBy = 2L,
                currentInstallment = 3,
                label = "Housing Allowance",
                isOvertimeEligible = false,
                arrearOf = UUID.randomUUID(),
                isPartOfCtc = false,
                notes = "This is a test note.",
            ),
        )

        val expectedGrpcResponse = CompensationItemsResponse.newBuilder()
            .addAllItems(compensationItemList.map { it.toGrpc() })
            .build()

        // When
        val actualGrpcResponse = compensationItemList.toGrpc()

        // Then
        assertEquals(
            expectedGrpcResponse,
            actualGrpcResponse,
            "The toGrpc function did not correctly map all fields in the list.",
        )
    }
}
