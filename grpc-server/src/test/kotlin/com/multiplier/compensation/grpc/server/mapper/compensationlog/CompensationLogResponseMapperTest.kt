package com.multiplier.compensation.grpc.server.mapper.compensationlog

import com.google.type.Date
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationlog.CompensationLogEnriched
import com.multiplier.compensation.grpc.server.mapper.common.toGrpc
import com.multiplier.grpc.common.toDate
import com.multiplier.grpc.common.toTimestamp
import com.multiplier.grpc.common.v1.toGrpc
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class CompensationLogResponseMapperTest {
    @Test
    fun `toGrpc maps all fields correctly`() {
        val log = CompensationLogEnriched(
            id = UUID.randomUUID(),
            entityId = 1L,
            contractId = 2L,
            compensationName = "Bonus",
            compensationCategory = "INCENTIVE",
            category = "INCENTIVE",
            currency = "INR",
            billingRateType = BillingRateType.VALUE,
            billingRate = 2500.0,
            billingFrequency = BillingFrequency.MONTHLY,
            payScheduleName = "Monthly Pay",
            isInstallment = false,
            startDate = LocalDate.of(2024, 4, 1),
            endDate = LocalDate.of(2024, 12, 31),
            noOfInstallments = 6,
            requestType = RequestType.COMPENSATION_SETUP,
            requestId = "R1",
            isTaxable = true,
            isFixed = true,
            isProrated = true,
            isMandatory = false,
            isPartOfBasePay = false,
            createdOn = LocalDateTime.of(2024, 4, 2, 10, 0),
            createdBy = 500L,
            updatedOn = LocalDateTime.of(2024, 4, 15, 11, 30),
            updatedBy = 501L,
        )

        val grpc = log.toGrpc()

        assertEquals(log.id.toGrpc(), grpc.id)
        assertEquals(log.entityId, grpc.entityId)
        assertEquals(log.contractId, grpc.contractId)
        assertEquals(log.compensationName, grpc.compensationName)
        assertEquals(log.category, grpc.compensationCategory)
        assertEquals(log.currency, grpc.currency)
        assertEquals(log.billingRateType.toGrpc(), grpc.billingRateType)
        assertEquals(log.billingRate, grpc.billingRate)
        assertEquals(log.billingFrequency.toGrpc(), grpc.billingFrequency)
        assertEquals(log.payScheduleName, grpc.payScheduleName)
        assertEquals(log.isInstallment, grpc.isInstallment)
        assertEquals(log.startDate.toDate(), grpc.startDate)
        assertEquals(log.endDate?.toDate(), grpc.endDate)
        assertEquals(log.noOfInstallments, grpc.numberOfInstallments)
        assertEquals(log.requestType.toGrpc(), grpc.requestType)
        assertEquals(log.requestId, grpc.requestId)
        assertEquals(log.isTaxable, grpc.isTaxable)
        assertEquals(log.isFixed, grpc.isFixed)
        assertEquals(log.isProrated, grpc.isProrated)
        assertEquals(log.isMandatory, grpc.isMandatory)
        assertEquals(log.isPartOfBasePay, grpc.isPartOfBasePay)
        assertEquals(log.createdOn.toTimestamp(), grpc.createdOn)
        assertEquals(log.createdBy, grpc.createdBy)
        assertEquals(log.updatedOn.toTimestamp(), grpc.updatedOn)
        assertEquals(log.updatedBy, grpc.updatedBy)
    }

    @Test
    fun `toGrpc handles nulls correctly`() {
        val log = CompensationLogEnriched(
            id = UUID.randomUUID(),
            entityId = 1L,
            contractId = 2L,
            compensationName = "Bonus",
            category = "INCENTIVE",
            compensationCategory = "INCENTIVE",
            currency = "INR",
            billingRateType = BillingRateType.VALUE,
            billingRate = null,
            billingFrequency = BillingFrequency.MONTHLY,
            payScheduleName = "Monthly Pay",
            isInstallment = false,
            startDate = LocalDate.of(2024, 4, 1),
            endDate = null,
            noOfInstallments = null,
            requestType = RequestType.COMPENSATION_SETUP,
            requestId = "R4",
            isTaxable = true,
            isFixed = true,
            isProrated = true,
            isMandatory = false,
            isPartOfBasePay = false,
            createdOn = LocalDateTime.of(2024, 4, 2, 10, 0),
            createdBy = 500L,
            updatedOn = LocalDateTime.of(2024, 4, 15, 11, 30),
            updatedBy = 501L,
        )

        val grpc = log.toGrpc()

        assertEquals(0.0, grpc.billingRate)
        assertEquals(Date.getDefaultInstance(), grpc.endDate)
        assertEquals(0, grpc.numberOfInstallments)
    }

    @Test
    fun `list of CompensationLogEnriched is mapped to response`() {
        val list = listOf(
            CompensationLogEnriched(
                id = UUID.randomUUID(),
                entityId = 1L,
                contractId = 2L,
                compensationName = "Bonus",
                category = "INCENTIVE",
                compensationCategory = "INCENTIVE",
                currency = "INR",
                billingRateType = BillingRateType.VALUE,
                billingRate = 1000.0,
                billingFrequency = BillingFrequency.MONTHLY,
                payScheduleName = "Monthly Pay",
                isInstallment = false,
                startDate = LocalDate.of(2024, 4, 1),
                endDate = null,
                noOfInstallments = null,
                requestType = RequestType.COMPENSATION_SETUP,
                requestId = "R2",
                isTaxable = true,
                isFixed = false,
                isProrated = true,
                isMandatory = false,
                isPartOfBasePay = true,
                createdOn = LocalDateTime.of(2024, 4, 1, 10, 0),
                createdBy = 100L,
                updatedOn = LocalDateTime.of(2024, 4, 10, 12, 0),
                updatedBy = 200L,
            ),
        )

        val grpcResponse = list.toGrpc()

        assertEquals(1, grpcResponse.itemsCount)
    }
}
