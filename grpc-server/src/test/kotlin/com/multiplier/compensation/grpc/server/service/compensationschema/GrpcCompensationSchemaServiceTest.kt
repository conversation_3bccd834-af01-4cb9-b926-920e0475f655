package com.multiplier.compensation.grpc.server.service.compensationschema

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.domain.common.exception.CompensationServiceException
import com.multiplier.compensation.domain.common.exception.config.CompensationServiceErrorCode
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.grpc.schema.CompensationSchema
import com.multiplier.compensation.grpc.server.mapper.compensationschema.toGrpc
import com.multiplier.compensation.service.compensationschema.CompensationSchemaService
import com.multiplier.compensation.service.payschedule.PayScheduleService
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import java.util.UUID
import kotlin.coroutines.EmptyCoroutineContext
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

class GrpcCompensationSchemaServiceTest {
    private lateinit var compensationSchemaService: CompensationSchemaService
    private lateinit var payScheduleService: PayScheduleService
    private lateinit var grpcCompensationSchemaService: GrpcCompensationSchemaService

    @BeforeEach
    fun setUp() {
        compensationSchemaService = mockk()
        payScheduleService = mockk()
        grpcCompensationSchemaService = GrpcCompensationSchemaService(
            grpcContext = EmptyCoroutineContext,
            compensationSchemaService = compensationSchemaService,
            payScheduleService = payScheduleService,
        )
    }

    @Test
    fun `getCompensationSchema should return response when successful`() = runBlocking {
        val request = CompensationSchema.GetCompensationSchemaRequest.newBuilder().setEntityId(1L).build()
        var domainResponse = listOf(createCompensationSchema)
        domainResponse = domainResponse.map { it.copy(schemaItems = listOf(createCompensationSchemaItem)) }
        val expectedResponse = domainResponse.toGrpc(emptyMap())
        coEvery { compensationSchemaService.getSchemaByEntityIds(any()) } returns domainResponse
        coEvery { payScheduleService.getPaySchedulesByIds(any()) } returns emptySet()
        val response = grpcCompensationSchemaService.getCompensationSchema(request)
        coVerify { compensationSchemaService.getSchemaByEntityIds(any()) }
        coVerify { payScheduleService.getPaySchedulesByIds(any()) }
        assertEquals(expectedResponse, response)
    }

    @Test
    fun `getCompensationSchema should throw INTERNAL StatusRuntimeException on error`() = runBlocking {
        val request = mockk<CompensationSchema.GetCompensationSchemaRequest> {
            every { entityId } returns 1L
        }
        coEvery { compensationSchemaService.getSchemaByEntityIds(any()) } throws Exception("Some error")
        val exception = assertFailsWith<CompensationServiceException> {
            grpcCompensationSchemaService.getCompensationSchema(request)
        }
        assertEquals(CompensationServiceErrorCode.CompensationSchemaFailed, exception.errorCode)
        assertEquals("Error occurred while fetching compensation schema for entity [1]", exception.message)
    }
}

private val createCompensationSchemaItem: CompensationSchemaItem
    get() {
        return CompensationSchemaItem(
            id = UUID.randomUUID(),
            schemaId = UUID.randomUUID(),
            componentName = "Component",
            category = "Category",
            isTaxable = true,
            isProrated = true,
            isFixed = true,
            isActive = true,
            isMandatory = true,
            isPartOfBasePay = true,
            createdOn = LocalDateTime.now(),
            createdBy = 1,
            updatedOn = LocalDateTime.now(),
            updatedBy = 1,
            label = "Label",
            itemType = ItemType.INPUT,
            validation = null,
            calculation = null,
            billingRateType = BillingRateType.VALUE,
            isOvertimeEligible = false,
            description = "Test component description",
            billingFrequency = BillingFrequency.MONTHLY,
            payScheduleId = null,
            currency = "USD",
            isPartOfCtc = true,
        )
    }

private val createCompensationSchema: com.multiplier.compensation.domain.compensationschema.CompensationSchema
    get() {
        return com.multiplier.compensation.domain.compensationschema.CompensationSchema(
            id = UUID.randomUUID(),
            country = CountryCode.USA,
            companyId = 1,
            isDefault = true,
            name = "DefaultSchema",
            isActive = true,
            createdOn = LocalDateTime.now(),
            createdBy = 1,
            updatedOn = LocalDateTime.now(),
            updatedBy = 1,
            entityId = 1L,
            schemaItems = emptyList(),
            tags = listOf("GLOBAL_PAYROLL"),
            configurationScope = ConfigurationScope.COMPANY,
            description = "Test schema description",
        )
    }
