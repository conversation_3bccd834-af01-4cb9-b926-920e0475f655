package com.multiplier.compensation.service.common.service

import com.multiplier.growthbook.sdk.GrowthBookSDK
import com.multiplier.growthbook.sdk.GrowthBookSdkBuilder
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PreDestroy
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

const val COMPENSATION_VARIABLE_COMPONENT_NOTIFICATION: String = "compensation-variable-component-notification"
const val COMPENSATION_VARIABLE_COMPONENT_TESTING_FLAG: String = "compensation-variable-component-notification-testing"

@Service
@EnableScheduling
class FeatureFlagService(
    @Value("\${growthbook.base-url}") baseUrl: String,
    @Value("\${growthbook.env-key}") envKey: String,
) {
    private val sdkInstance: GrowthBookSDK

    init {
        sdkInstance = GrowthBookSdkBuilder(envKey, baseUrl)
            .setRefreshHandler { success: Boolean -> flagsRefreshHandler(success) }
            .initialize()
    }

    fun feature(
        id: String,
        attributes: Map<String, Any> = emptyMap(),
    ): GBFeatureResult = sdkInstance.feature(id, attributes)

    fun isOn(
        id: String,
        attributes: Map<String, Any> = emptyMap(),
    ): Boolean = try {
        val flag = feature(id, attributes)
        flag.on
    } catch (e: Exception) {
        log.warn(e) { "Error checking feature flag: ${e.message}" }
        false
    }

    @Scheduled(fixedRateString = "\${growthbook.refresh-frequency-ms}")
    fun refreshFeatureFlags() {
        sdkInstance.reloadFeatureFlags()
    }

    private fun flagsRefreshHandler(success: Boolean) {
        if (!success) {
            log.warn { "[FeatureFlagService] Feature Flags failed to refresh!" }
        }
    }

    /**
     * Cleanup method to ensure all resources are properly released when the application shuts down.
     * This prevents Timer thread leaks that can occur with the GrowthBookSDK.
     */
    @PreDestroy
    fun cleanup() {
        log.info { "Cleaning up resources for FeatureFlagService" }
        try {
            // Use reflection to access and cancel the Timer in GrowthBookSDK
            // This is necessary because GrowthBookSDK doesn't expose a proper shutdown method
            val sdkClass = sdkInstance.javaClass
            val timerField = sdkClass.getDeclaredField("refreshTimer")
            timerField.isAccessible = true
            val timer = timerField.get(sdkInstance) as? java.util.Timer
            timer?.cancel()
            log.info { "Successfully canceled Timer in GrowthBookSDK" }
        } catch (e: Exception) {
            log.warn(e) { "Failed to clean up Timer in GrowthBookSDK: ${e.message}" }
        }
    }
}
