package com.multiplier.compensation.service.common.validationpipeline

import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem

interface GroupValidationStage<T : ValidatorContext, R : ValidationDraftObject> {
    fun process(
        items: List<ValidationInputItem>,
        context: T,
        collector: ValidationDataCollector<R>,
    ): <PERSON><PERSON><PERSON>
}
