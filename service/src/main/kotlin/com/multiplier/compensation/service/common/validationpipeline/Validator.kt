package com.multiplier.compensation.service.common.validationpipeline

import com.multiplier.compensation.service.common.enums.skeletionfield.SkeletonField
import com.multiplier.compensation.service.common.validationpipeline.common.ValidatorUtil.getOriginalInputByKey
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.extrapolation.getParentInputRowForDerivedInputRow

abstract class Validator<T : ValidatorContext, R : ValidationDraftObject> {
    abstract fun validate(
        input: ValidationInputItem,
        context: T,
        collector: ValidationDataCollector<R>,
    ): Boolean

    fun addValidationResult(
        input: ValidationInputItem,
        field: SkeletonField,
        resultType: ValidationResultType,
        message: String,
        collector: ValidationDataCollector<R>,
    ) {
        // Find the original input that was used to derive the input that is being validated
        val originalInputRowForDerivedRow = getParentInputRowForDerivedInputRow(input, collector.inputPlusDerivedRows)

        val validationResult = CellValidationResult(
            field = getOriginalInputByKey(field = field, input = originalInputRowForDerivedRow),
            type = resultType,
            message = message,
        )
        collector.rowValidationResult
            .getOrPut(originalInputRowForDerivedRow.id) { mutableListOf() }
            .add(validationResult)
    }
}
