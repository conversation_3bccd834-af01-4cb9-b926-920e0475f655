package com.multiplier.compensation.service.compensation

import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus
import com.multiplier.compensation.service.compensation.termination.BulkCompensationTerminationService
import com.multiplier.compensation.service.compensationinput.dto.UpdateCompensationStatusBulkRequest
import org.springframework.stereotype.Component

@Component
class BulkCompensationStatusUpdateHandler(
    private val bulkActivationService: BulkCompensationActivationService,
    private val bulkTerminationService: BulkCompensationTerminationService,
) {
    fun update(request: UpdateCompensationStatusBulkRequest) = when (request.status) {
        CompensationInputStatus.ACTIVATED -> bulkActivationService.activate(request.entityId, request.contractIds)

        CompensationInputStatus.TERMINATED -> bulkTerminationService.terminate(request.contractIds)

        else -> throw InvalidArgumentException(
            errorCode = ValidationErrorCode.InvalidCompensationUpdateInputStatus,
            message = "${request.status} status update is not supported",
        )
    }
}
