package com.multiplier.compensation.service.compensation

import com.multiplier.compensation.service.common.dto.OperationStatus
import com.multiplier.compensation.service.common.dto.RowItem
import com.multiplier.compensation.service.common.dto.getValueAsLong
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import io.github.oshai.kotlinlogging.KotlinLogging

private val log = KotlinLogging.logger {}

/**
 * Filter out the drafts based on the operation status and the contract wise
 *
 * - If the operation status is failure, then we don't need to process the drafts any further.
 * - If any of the compensation input for a contract is failure, eliminate all other drafts for that contract.
 *
 * @param operationStatus Operation status of the validation results and the partial commit boolean
 * @param inputRowItems Bulk compensation input row items
 * @param validationDataCollector Validation data collector from the validation pipeline
 * @return Eligible drafts mapped respective input ids
 */
fun getEligibleDrafts(
    operationStatus: OperationStatus,
    inputRowItems: List<RowItem>,
    validationDataCollector: ValidationDataCollector<CompensationDraft>,
): Map<String, CompensationDraft> {
    if (operationStatus == OperationStatus.FAILURE) return emptyMap()

    val contractIdToRowItemMap = buildContractIdToRowItemMap(inputRowItems)
    val failedContractIds = extractFailedContractIds(
        contractIdToRowItemMap,
        validationDataCollector,
    )
    val eligibleDrafts = validationDataCollector.drafts
        .filterValues { it.contractId !in failedContractIds }

    log.info { "Found [${eligibleDrafts.size}] valid compensation drafts for processing." }

    return eligibleDrafts
}

private fun buildContractIdToRowItemMap(rowItems: List<RowItem>): Map<Long?, List<String>> = rowItems
    .groupBy {
        it.keyValuePairs
            .find { keyValue -> keyValue.key == CommonSkeletonField.CONTRACT_ID.id }.getValueAsLong()
    }
    .mapValues { (_, rowItems) -> rowItems.map { it.id } }

private fun extractFailedContractIds(
    contractIdToInputRecordMap: Map<Long?, List<String>>,
    validationDataCollector: ValidationDataCollector<CompensationDraft>,
): Set<Long?> {
    val failedRowItems = validationDataCollector.rowValidationResult
        .filterValues { validationResults ->
            validationResults.any { it.type == ValidationResultType.ERROR }
        }.keys

    return contractIdToInputRecordMap
        .filterValues { rowItemIds -> rowItemIds.any { it in failedRowItems } }
        .keys
}
