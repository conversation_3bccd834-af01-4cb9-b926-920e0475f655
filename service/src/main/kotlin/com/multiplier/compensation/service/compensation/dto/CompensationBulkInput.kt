package com.multiplier.compensation.service.compensation.dto

import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.service.common.dto.RowItem

data class CompensationBulkInput(
    val entityId: Long,
    val commitPartially: Boolean,
    val requestType: RequestType,
    val requestId: String,
    val customParams: Map<String, String>,
    val rowItems: List<RowItem> = emptyList(),
)
