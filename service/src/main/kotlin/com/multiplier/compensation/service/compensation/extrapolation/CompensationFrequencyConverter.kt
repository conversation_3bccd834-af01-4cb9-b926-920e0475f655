package com.multiplier.compensation.service.compensation.extrapolation

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.RoundingMode

private val log = KotlinLogging.logger {}

/**
 * Service responsible for converting billing frequencies and amounts in compensation drafts.
 * Handles conversion between all supported billing frequencies using strategy pattern.
 * Supported frequencies: ANNUALLY, SEMIANNUALLY, QUARTERLY, MONTHLY, SEMIMONTHLY, BIWEEKLY, WEEKLY, DAILY
 * Excludes: HOURLY and ONETIME frequencies
 */
@Service
class CompensationFrequencyConverter {
    companion object {
        // Frequency conversion multipliers (all relative to annual)
        private const val ANNUAL_PERIODS = 1.0
        private const val SEMIANNUAL_PERIODS = 2.0
        private const val QUARTERLY_PERIODS = 4.0
        private const val MONTHLY_PERIODS = 12.0
        private const val SEMIMONTHLY_PERIODS = 24.0
        private const val BIWEEKLY_PERIODS = 26.0
        private const val WEEKLY_PERIODS = 52.0
        private const val DAILY_PERIODS = 365.0

        private val SUPPORTED_FREQUENCIES = setOf(
            BillingFrequency.ANNUALLY,
            BillingFrequency.SEMIANNUALLY,
            BillingFrequency.QUARTERLY,
            BillingFrequency.MONTHLY,
            BillingFrequency.SEMIMONTHLY,
            BillingFrequency.BIWEEKLY,
            BillingFrequency.WEEKLY,
            BillingFrequency.DAILY,
        )

        private val CATEGORIES_TO_BASE_FREQUENCY_CONVERSION = setOf(
            CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
            CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
        )

        /**
         * Maps billing frequencies to their annual period multipliers
         */
        private val FREQUENCY_TO_ANNUAL_PERIODS = mapOf(
            BillingFrequency.ANNUALLY to ANNUAL_PERIODS,
            BillingFrequency.SEMIANNUALLY to SEMIANNUAL_PERIODS,
            BillingFrequency.QUARTERLY to QUARTERLY_PERIODS,
            BillingFrequency.MONTHLY to MONTHLY_PERIODS,
            BillingFrequency.SEMIMONTHLY to SEMIMONTHLY_PERIODS,
            BillingFrequency.BIWEEKLY to BIWEEKLY_PERIODS,
            BillingFrequency.WEEKLY to WEEKLY_PERIODS,
            BillingFrequency.DAILY to DAILY_PERIODS,
        )
    }

    /**
     * Converts input draft compensations' frequencies based on schema configured frequency.
     * Consider component categories TOTAL_COST_TO_COMPANY and CONTRACT_BASE_PAY for deriving configured vs input frequency.
     *
     * @param inputDrafts List of input drafts passed in user request
     * @param context Validation context containing schema information
     * @return List of input drafts with converted frequencies and amounts
     */
    fun adjustBillingFrequencyOnInputDrafts(
        inputDrafts: List<CompensationDraft>,
        context: CompensationValidatorContext,
    ): List<CompensationDraft> {
        val configuredSchemaFrequency = context.getCompensationSchemaItems().values.find {
            CATEGORIES_TO_BASE_FREQUENCY_CONVERSION.contains(it.category)
        }?.billingFrequency

        return inputDrafts.map {
            adjustFrequency(
                draft = it,
                from = it.billingFrequency,
                to = configuredSchemaFrequency,
                context = context,
            )
        }
    }

    /**
     * Converts derived draft compensations' frequencies back to original input frequencies.
     * Consider component categories TOTAL_COST_TO_COMPANY and CONTRACT_BASE_PAY for deriving configured vs input frequency.
     *
     * @param derivedDrafts List of derived drafts generated on top of user inputs.
     * @param context Validation context containing schema information
     * @param inputDrafts List of input drafts passed in user request
     * @return List of derived drafts with converted frequencies and amounts
     */
    fun adjustBillingFrequencyOnDerivedDrafts(
        derivedDrafts: List<CompensationDraft>,
        context: CompensationValidatorContext,
        inputDrafts: List<CompensationDraft>,
    ): List<CompensationDraft> {
        val configuredSchemaFrequency = context.getCompensationSchemaItems().values.find {
            CATEGORIES_TO_BASE_FREQUENCY_CONVERSION.contains(it.category)
        }?.billingFrequency

        val inputFrequency = inputDrafts.find {
            CATEGORIES_TO_BASE_FREQUENCY_CONVERSION.contains(it.schemaCategory)
        }?.billingFrequency

        return derivedDrafts.map {
            adjustFrequency(
                draft = it,
                from = configuredSchemaFrequency,
                to = inputFrequency,
                context = context,
            )
        }
    }

    /**
     * Adjusts billing frequencies and amounts for compensation draft.
     * Converts billing rates from one frequency to another.
     *
     * @param draft compensation draft to adjust
     * @param from Source billing frequency
     * @param to Target billing frequency
     * @return List of compensation drafts with adjusted frequencies and amounts
     */
    private fun adjustFrequency(
        draft: CompensationDraft,
        from: BillingFrequency?,
        to: BillingFrequency?,
        context: CompensationValidatorContext,
    ): CompensationDraft {
        log.info {
            "Starting frequency adjustment from [$from] to [$to] of component [${draft.schemaCategory}] " +
                "for contract [${draft.contractId}]"
        }

        if (draft.schemaItemId == null ||
            context.getCompensationSchemaItem(draft.schemaItemId)?.itemType == ItemType.FIXED
        ) {
            return draft
        }

        // Return original drafts if either frequency is null or same
        if (from == null || to == null || from == to) {
            return draft
        }

        // Check if both frequencies are supported
        if (!SUPPORTED_FREQUENCIES.contains(from) || !SUPPORTED_FREQUENCIES.contains(to)) {
            log.warn { "Unsupported frequency conversion from [$from] to [$to], returning original drafts" }
            return draft
        }

        // Get conversion multiplier
        val fromPeriods = FREQUENCY_TO_ANNUAL_PERIODS[from] ?: return draft
        val toPeriods = FREQUENCY_TO_ANNUAL_PERIODS[to] ?: return draft
        val multiplier = BigDecimal(fromPeriods / toPeriods)

        return adjustBillingRate(draft, multiplier, to)
    }

    private fun adjustBillingRate(
        draft: CompensationDraft,
        multiplier: BigDecimal,
        targetFrequency: BillingFrequency,
    ): CompensationDraft {
        val originalRate = draft.billingRate
        if (originalRate == null) {
            log.warn {
                "No billing rate found for draft for contract [${draft.contractId}] with schema item " +
                    "[${draft.schemaItemId}], skipping conversion"
            }
            return draft
        }

        val convertedRate = BigDecimal(originalRate)
            .multiply(multiplier)
            .setScale(2, RoundingMode.HALF_UP)

        return draft.copy(
            billingRate = convertedRate.toDouble(),
            billingFrequency = targetFrequency,
        )
    }
}
