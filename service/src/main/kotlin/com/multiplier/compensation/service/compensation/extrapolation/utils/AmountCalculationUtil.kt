package com.multiplier.compensation.service.compensation.extrapolation.utils

import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.service.common.utils.log
import com.udojava.evalex.Expression
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * Evaluates a mathematical formula using EvalEx library.
 *
 * @param request The formula request containing formula and variables
 * @return The calculated result as Double
 * @throws InvalidArgumentException if the formula is invalid
 */
fun evaluateFormula(request: FormulaRequest): Double {
    log.info { "Evaluating formula [${request.formula}] with variables [${request.variables}]" }

    try {
        val expression = Expression(request.formula)
        expression.setPrecision(10)
        request.variables.forEach { (k, v) ->
            expression.with(k, v)
        }

        val result = expression.eval()
        val roundedResult = result.setScale(0, RoundingMode.HALF_UP)

        log.info { "Formula evaluation result [$roundedResult]" }

        return roundedResult.toDouble()
    } catch (exception: Exception) {
        throw InvalidArgumentException(
            ValidationErrorCode.InvalidCalculationFormula,
            "Failed to evaluate formula [${request.formula}]",
            exception,
        )
    }
}

/**
 * Parses a fixed amount from a string value.
 *
 * @param amountString The string representation of the amount
 * @param itemName The name of the item (for error messages)
 * @return The parsed amount as Double
 * @throws InvalidArgumentException if parsing fails
 */
fun parseFixedAmount(
    amountString: String?,
    itemName: String,
): Double = try {
    amountString?.toDouble()
        ?: throw IllegalArgumentException("No amount value provided for FIXED item [$itemName]")
} catch (exception: Exception) {
    throw InvalidArgumentException(
        ValidationErrorCode.InvalidFixedAmount,
        "Invalid amount value [$amountString] for FIXED item [$itemName]",
        exception,
    )
}

/**
 * Request object for formula evaluation.
 *
 * @param formula The mathematical formula to evaluate
 * @param variables Map of variable names to their values
 */
data class FormulaRequest(
    val formula: String,
    val variables: Map<String, BigDecimal>,
)
