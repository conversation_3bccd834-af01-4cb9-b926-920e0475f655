package com.multiplier.compensation.service.compensation.mapper

import com.fasterxml.uuid.impl.TimeBasedEpochGenerator
import com.multiplier.compensation.domain.common.ReasonCode
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationinput.CompensationInput
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus
import com.multiplier.compensation.domain.compensationinput.enums.CompensationLifecycle
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.payschedule.schedulegeneration.estimateEndDate
import java.time.LocalDate
import java.util.UUID

fun CompensationDraft.toCompensation(
    uuidGenerator: TimeBasedEpochGenerator,
    entityId: Long,
    status: CompensationStatus = CompensationStatus.NEW,
    previousId: UUID? = null,
    paySchedules: Map<UUID, PaySchedule>,
) = buildCompensation(
    uuidGenerator = uuidGenerator,
    entityId = entityId,
    paySchedules = paySchedules,
    status = status,
    previousId = previousId,
)

fun CompensationDraft.toBackFilledCompensation(
    uuidGenerator: TimeBasedEpochGenerator,
    entityId: Long,
    status: CompensationStatus = CompensationStatus.NEW,
    previousId: UUID? = null,
    paySchedules: Map<UUID, PaySchedule>,
) = buildCompensation(
    uuidGenerator = uuidGenerator,
    entityId = entityId,
    paySchedules = paySchedules,
    status = status,
    previousId = previousId,
    isBackFilled = true,
)

@Suppress("UnsafeCallOnNullableType", "MapGetWithNotNullAssertionOperator")
private fun CompensationDraft.buildCompensation(
    uuidGenerator: TimeBasedEpochGenerator,
    entityId: Long,
    paySchedules: Map<UUID, PaySchedule>,
    status: CompensationStatus,
    previousId: UUID?,
    isBackFilled: Boolean = false,
) = Compensation(
    id = uuidGenerator.generate(),
    companyId = companyId!!,
    entityId = entityId,
    contractId = contractId!!,
    schemaItemId = schemaItemId!!,
    category = schemaCategory,
    currency = currency!!,
    billingRateType = billingRateType!!,
    billingRate = billingRate,
    billingFrequency = billingFrequency!!,
    payScheduleId = payScheduleId!!,
    startDate = startDate!!,
    endDate = evaluateEndDate(
        paySchedules[payScheduleId]!!,
        startDate,
        endDate,
        noOfInstallments,
    ),
    isInstallment = isInstallment!!,
    noOfInstallments = noOfInstallments,
    status = status,
    notes = notes,
    processedUntilDate = processedUntilDate,
    processingFrom = if (isBackFilled) startDate else null,
    processingTo = if (isBackFilled) endDate else null,
).also {
    it.previousId = previousId
}

@Suppress("UnsafeCallOnNullableType", "MapGetWithNotNullAssertionOperator")
fun CompensationDraft.toCompensationInput(
    uuidGenerator: TimeBasedEpochGenerator,
    entityId: Long,
    status: CompensationInputStatus = CompensationInputStatus.ACTIVATED,
    requestType: RequestType,
    requestId: String,
    reasonCode: ReasonCode?,
    paySchedules: Map<UUID, PaySchedule>,
    lifecycle: CompensationLifecycle,
) = CompensationInput(
    id = uuidGenerator.generate(),
    companyId = companyId!!,
    entityId = entityId,
    contractId = contractId!!,
    schemaItemId = schemaItemId!!,
    category = schemaCategory!!,
    currency = currency!!,
    billingRateType = billingRateType!!,
    billingRate = billingRate,
    billingFrequency = billingFrequency!!,
    payScheduleId = payScheduleId!!,
    startDate = startDate!!,
    endDate = evaluateEndDate(
        paySchedules[payScheduleId]!!,
        startDate,
        endDate,
        noOfInstallments,
    ),
    isInstallment = isInstallment!!,
    noOfInstallments = noOfInstallments,
    requestType = this.getRequestType(inputRequestType = requestType),
    requestId = requestId,
    reasonCode = reasonCode,
    status = status,
    notes = notes,
    lifecycle = lifecycle,
    sourceEffectiveDate = sourceEffectiveDate,
)

/**
 * Evaluates the end date for a compensation based on the given parameters.
 *
 * @param paySchedule The pay schedule containing the frequency and installment information.
 * @param startDate The start date of the compensation.
 * @param endDate The end date of the compensation, if already present.
 * @param noOfInstallments The number of installments for the compensation.
 * @return The calculated end date if the pay schedule is an installment, otherwise returns the provided end date.
 */
fun evaluateEndDate(
    paySchedule: PaySchedule,
    startDate: LocalDate,
    endDate: LocalDate?,
    noOfInstallments: Int?,
): LocalDate? = when {
    endDate != null || !paySchedule.isInstallment -> endDate
    else -> estimateEndDate(
        startDate,
        paySchedule.frequency,
        true,
        noOfInstallments ?: 0,
        paySchedule.payDateReferenceType,
    )
}

/**
 * Determines the request type based on the given parameters.
 * If the use case is not a migration backfill, the request type is returned as is.
 * If the use case is a migration backfill, the request type is determined based on the backfill type.
 *      * If the compensation draft is a backfill for a revision, the request type is set to COMPENSATION_REVISION_BACKFILL.
 *      * Otherwise, the request type is set to COMPENSATION_BACKFILL.
 *  This method can be deprecated after GP and EOR migration for compensations is complete.
 */
private fun CompensationDraft.getRequestType(inputRequestType: RequestType): RequestType =
    if (inputRequestType != RequestType.COMPENSATION_REVISION_BACKFILL) {
        inputRequestType
    } else {
        if (isBackfillForRevision == true) {
            RequestType.COMPENSATION_REVISION_BACKFILL
        } else {
            RequestType.COMPENSATION_BACKFILL
        }
    }
