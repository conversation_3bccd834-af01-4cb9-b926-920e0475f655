package com.multiplier.compensation.service.compensation.mapper

import com.multiplier.compensation.service.common.dto.OperationStatus
import com.multiplier.compensation.service.common.dto.ResultType
import com.multiplier.compensation.service.compensation.dto.RevokeCompensationRequest
import com.multiplier.compensation.service.compensation.dto.RevokeCompensationResult
import com.multiplier.compensation.service.compensation.dto.RevokeCompensationsRequest
import com.multiplier.compensation.service.compensation.dto.RevokeCompensationsResponse

fun RevokeCompensationRequest.toFailure(msg: String) = RevokeCompensationResult(
    compensationId = this.compensationId,
    resultType = ResultType.ERROR,
    message = msg,
)

fun RevokeCompensationRequest.toSuccess() = RevokeCompensationResult(
    compensationId = this.compensationId,
    resultType = ResultType.INFO,
    message = "Revoked successfully",
)

fun buildRevokeCompensationsResponse(
    results: List<RevokeCompensationResult>,
    request: RevokeCompensationsRequest,
): RevokeCompensationsResponse {
    val invalidResults = request.invalidRequests.map {
        RevokeCompensationResult(
            compensationId = it.compensationId,
            resultType = ResultType.ERROR,
            message = it.reason,
        )
    }

    val inaccessibleResults = request.inaccessibleRequests.map {
        RevokeCompensationResult(
            compensationId = it.compensationId,
            resultType = ResultType.ERROR,
            message = it.reason,
        )
    }

    val allResults = results + invalidResults + inaccessibleResults

    val hasErrors = allResults.any { it.resultType == ResultType.ERROR }
    val hasSuccess = allResults.any { it.resultType == ResultType.INFO }

    val status = when {
        hasErrors && hasSuccess -> OperationStatus.PARTIAL_SUCCESS
        hasErrors -> OperationStatus.FAILURE
        else -> OperationStatus.SUCCESS
    }

    return RevokeCompensationsResponse(
        status = status,
        results = allResults,
    )
}
