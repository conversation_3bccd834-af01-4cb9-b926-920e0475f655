package com.multiplier.compensation.service.compensation.validation

import com.multiplier.compensation.service.common.mapper.SkeletonCompensationMapperUtils.toCompensationDraft
import com.multiplier.compensation.service.common.validationpipeline.common.validators.SkeletonValidator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.stages.CompensationBackFillGroupValidationStage
import com.multiplier.compensation.service.compensation.validation.stages.CompensationBackFillPrimaryValidationStage
import com.multiplier.compensation.service.compensation.validation.stages.CompensationBackFillSecondaryValidationStage
import org.springframework.stereotype.Component

@Component
class CompensationBackFillValidationPipeline(
    groupValidationStage: CompensationBackFillGroupValidationStage,
    primaryValidationStage: CompensationBackFillPrimaryValidationStage,
    secondaryValidationStage: CompensationBackFillSecondaryValidationStage,
    skeletonValidator: SkeletonValidator,
) : BaseCompensationValidationPipeline(
        groupValidationStage,
        listOf(
            primaryValidationStage,
            secondaryValidationStage,
        ),
        skeletonValidator,
    ) {
    override fun buildDraftItem(
        item: ValidationInputItem,
        context: CompensationValidatorContext,
    ) = item.toCompensationDraft(
        context,
    )
}
