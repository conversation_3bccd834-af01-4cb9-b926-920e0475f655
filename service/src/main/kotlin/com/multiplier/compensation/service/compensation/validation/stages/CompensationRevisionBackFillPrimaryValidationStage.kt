package com.multiplier.compensation.service.compensation.validation.stages

import com.multiplier.compensation.service.common.validationpipeline.common.stages.PrimaryValidationStage
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.validators.BackFillContractValidator
import com.multiplier.compensation.service.compensation.validation.validators.ContractIdValidator
import com.multiplier.compensation.service.compensation.validation.validators.DuplicateInputValidator
import org.springframework.stereotype.Component

@Component
class CompensationRevisionBackFillPrimaryValidationStage(
    duplicateInputValidator: DuplicateInputValidator,
    contractIdValidator: ContractIdValidator,
    backFillContractValidator: BackFillContractValidator,
) : PrimaryValidationStage<CompensationValidatorContext, CompensationDraft>() {
    init {
        setValidators(
            duplicateInputValidator,
            contractIdValidator,
            backFillContractValidator,
        )
    }
}
