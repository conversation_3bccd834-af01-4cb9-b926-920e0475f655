package com.multiplier.compensation.service.compensation.validation.stages

import com.multiplier.compensation.service.common.validationpipeline.common.stages.SecondaryValidationStage
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.validators.BillingFrequencyValidator
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateInputValidator
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateTypeValidator
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateValidator
import com.multiplier.compensation.service.compensation.validation.validators.DependentComponentCurrencyValidator
import com.multiplier.compensation.service.compensation.validation.validators.InstallmentValidator
import com.multiplier.compensation.service.compensation.validation.validators.PayScheduleTypeValidator
import com.multiplier.compensation.service.compensation.validation.validators.PayScheduleValidator
import org.springframework.stereotype.Component

@Component
class CompensationRevisionBackFillSecondaryValidationStage(
    billingFrequencyValidator: BillingFrequencyValidator,
    billingRateTypeValidator: BillingRateTypeValidator,
    billingRateValidator: BillingRateValidator,
    installmentValidator: InstallmentValidator,
    payScheduleValidator: PayScheduleValidator,
    payScheduleTypeValidator: PayScheduleTypeValidator,
    dependentComponentCurrencyValidator: DependentComponentCurrencyValidator,
    billingRateInputValidator: BillingRateInputValidator,
) : SecondaryValidationStage<CompensationValidatorContext, CompensationDraft>() {
    init {
        setValidators(
            billingFrequencyValidator,
            billingRateTypeValidator,
            billingRateValidator,
            installmentValidator,
            payScheduleValidator,
            payScheduleTypeValidator,
            dependentComponentCurrencyValidator,
            billingRateInputValidator,
        )
    }
}
