package com.multiplier.compensation.service.compensation.validation.stages

import com.multiplier.compensation.service.common.validationpipeline.common.stages.PrimaryValidationStage
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.validators.ComponentChangeValidator
import com.multiplier.compensation.service.compensation.validation.validators.ContractIdValidator
import com.multiplier.compensation.service.compensation.validation.validators.DeletionValidator
import com.multiplier.compensation.service.compensation.validation.validators.DuplicateComponentNameValidator
import com.multiplier.compensation.service.compensation.validation.validators.DuplicateInputValidator
import com.multiplier.compensation.service.compensation.validation.validators.InActiveContractValidator
import com.multiplier.compensation.service.compensation.validation.validators.RevisionStartDateValidator
import org.springframework.stereotype.Component

@Component
class PaySupplementPrimaryValidationStage(
    duplicateInputValidator: DuplicateInputValidator,
    duplicateComponentNameValidator: DuplicateComponentNameValidator,
    contractIdValidator: ContractIdValidator,
    inActiveContractValidator: InActiveContractValidator,
    deletionValidator: DeletionValidator,
    componentChangeValidator: ComponentChangeValidator,
    revisionStartDateValidator: RevisionStartDateValidator,
) : PrimaryValidationStage<CompensationValidatorContext, CompensationDraft>() {
    init {
        setValidators(
            duplicateInputValidator,
            duplicateComponentNameValidator,
            contractIdValidator,
            deletionValidator,
            inActiveContractValidator,
            componentChangeValidator,
            revisionStartDateValidator,
        )
    }
}
