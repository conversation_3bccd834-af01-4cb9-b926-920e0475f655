package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import org.springframework.stereotype.Component

@Component
class BasePayChangeValidator : Validator<CompensationValidatorContext, CompensationDraft>() {
    override fun validate(
        input: ValidationInputItem,
        context: CompensationValidatorContext,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        val draft = collector.drafts[input.id] ?: return false
        val contractId = draft.contractId ?: return false

        if (draft.schemaCategory != CategoryConstants.CATEGORY_CONTRACT_BASE_PAY) return true

        val isBasePayBreakdownGiven = collector.drafts.values.any {
            it.contractId == contractId && it.schemaCategory == CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP
        }

        if (isBasePayBreakdownGiven) return true

        val breakDownSchemaItems = context.getCompensationSchemaItems().values
            .filter { it.category == CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP }
            .map { it.id }

        val isValidBasePayBreakdownExists = context.validCompensations[contractId]?.any { comp ->
            comp.schemaItemId in breakDownSchemaItems
        } ?: false

        if (isValidBasePayBreakdownExists) {
            addValidationResult(
                input = input,
                field = CompensationSkeletonField.COMPONENT_NAME,
                resultType = ValidationResultType.ERROR,
                message = "Base pay breakdown is required for this employee",
                collector = collector,
            )
            return false
        }

        return true
    }
}
