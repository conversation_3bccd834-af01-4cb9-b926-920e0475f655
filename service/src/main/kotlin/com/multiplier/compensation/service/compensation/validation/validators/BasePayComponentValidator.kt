package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_CONTRACT_BASE_PAY
import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import org.springframework.stereotype.Component

@Component
class BasePayComponentValidator : Validator<CompensationValidatorContext, CompensationDraft>() {
    override fun validate(
        input: ValidationInputItem,
        context: CompensationValidatorContext,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        val draft = collector.drafts[input.id] ?: return false
        val employeeId = draft.employeeId ?: return false

        val basePayComponentCount = collector.drafts.values.count {
            it.employeeId == employeeId &&
                (
                    it.schemaCategory == CATEGORY_CONTRACT_BASE_PAY
                )
        }

        val ctcComponentCount = collector.drafts.values.count {
            it.employeeId == employeeId && it.schemaCategory == CATEGORY_TOTAL_COST_TO_COMPANY
        }

        if (basePayComponentCount == 0 && ctcComponentCount == 0) {
            addErrorMessage(
                input = input,
                message = "No valid $CATEGORY_CONTRACT_BASE_PAY or $CATEGORY_TOTAL_COST_TO_COMPANY component found " +
                    "for employee id $employeeId",
                collector = collector,
            )
            return false
        }

        if (basePayComponentCount > 1) {
            addErrorMessage(
                input = input,
                message = "Multiple $CATEGORY_CONTRACT_BASE_PAY components found for employee id $employeeId",
                collector = collector,
            )
            return false
        }

        if (ctcComponentCount > 1) {
            addErrorMessage(
                input = input,
                message = "Multiple $CATEGORY_TOTAL_COST_TO_COMPANY components found for employee id $employeeId",
                collector = collector,
            )
            return false
        }

        return true
    }

    private fun addErrorMessage(
        input: ValidationInputItem,
        message: String,
        collector: ValidationDataCollector<CompensationDraft>,
    ) {
        addValidationResult(
            input = input,
            field = CommonSkeletonField.EMPLOYEE_ID,
            resultType = ValidationResultType.ERROR,
            message = message,
            collector = collector,
        )
    }
}
