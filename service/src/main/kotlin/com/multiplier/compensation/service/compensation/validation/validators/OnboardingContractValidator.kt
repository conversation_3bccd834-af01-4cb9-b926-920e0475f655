package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import org.springframework.stereotype.Component

@Component
class OnboardingContractValidator : Validator<CompensationValidatorContext, CompensationDraft>() {
    override fun validate(
        input: ValidationInputItem,
        context: CompensationValidatorContext,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        val draft = collector.drafts[input.id] ?: return false
        val contractStatus = getContractStatus(context, draft, input, collector)
            ?: return false

        if (contractStatus == ContractStatus.ONBOARDING) return true

        addValidationResult(
            input = input,
            field = CommonSkeletonField.CONTRACT_ID,
            resultType = ValidationResultType.ERROR,
            message = "Contract should be in onboarding status to setup compensations",
            collector = collector,
        )

        return false
    }

    private fun getContractStatus(
        context: CompensationValidatorContext,
        draft: CompensationDraft,
        input: ValidationInputItem,
        collector: ValidationDataCollector<CompensationDraft>,
    ): ContractStatus? {
        val contractStatus = if (isSetupValidation(context, draft.contractId)) {
            draft.contractDraft?.status
        } else {
            context.contracts[draft.contractId]?.status
        }

        if (contractStatus == null) {
            val (field, message) = if (isSetupValidation(context, draft.contractId)) {
                CommonSkeletonField.CONTRACT_STATUS to "Contract onboarding status is not passed in input"
            } else {
                CommonSkeletonField.CONTRACT_ID to "Contract id is either not passed in input or is invalid"
            }

            addValidationResult(
                input = input,
                field = field,
                resultType = ValidationResultType.ERROR,
                message = message,
                collector = collector,
            )
        }

        return contractStatus
    }
}
