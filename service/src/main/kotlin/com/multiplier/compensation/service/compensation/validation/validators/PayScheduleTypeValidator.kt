package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.payschedule.dto.PayScheduleSkeletonField
import org.springframework.stereotype.Component

@Component
class PayScheduleTypeValidator : Validator<CompensationValidatorContext, CompensationDraft>() {
    override fun validate(
        input: ValidationInputItem,
        context: CompensationValidatorContext,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        val draft = collector.drafts[input.id] ?: return false
        val payScheduleId = draft.payScheduleId ?: return false

        val isComponentTypeInstallment = draft.isInstallment ?: return false
        val isPayScheduleTypeInstallment = context.paySchedules.values.firstOrNull {
            it.id == payScheduleId
        }?.isInstallment ?: return false

        if (isComponentTypeInstallment != isPayScheduleTypeInstallment) {
            addValidationResult(
                input = input,
                field = PayScheduleSkeletonField.PAY_SCHEDULE_NAME,
                resultType = ValidationResultType.ERROR,
                message = "Is Installment flag of compensation does not match with that of the associated pay schedule",
                collector = collector,
            )
            return false
        }

        return true
    }
}
