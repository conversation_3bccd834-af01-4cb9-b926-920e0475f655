package com.multiplier.compensation.service.compensationitem.dto

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import com.multiplier.compensation.domain.compensationitem.CompensationItem

data class CompensationItemUpdateMessage
    @JsonCreator
    constructor(
        @JsonProperty("compensationItem") val compensationItem: CompensationItem,
    )
