package com.multiplier.compensation.service.compensationitem.dto

import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem

/**
 * Represents a request to update compensation items, including the old record, updated inline record, and the new record.
 *
 * @property oldImage The original compensation record before any updates were applied.
 * @property newImage The updated compensation record reflecting inline changes to dates or the number of installments.
 * @property newRecord The new compensation record with new billing rates or other adjustments like updated dates or installments.
 * @property parentUpdateRequest Represents an update request for the parent compensation. Its presence indicates that the given request is for a dependent compensation update.
 * @property shouldPersist Whether the update should be applied to the Database.
 * @property schemaItem The schema item associated with the compensation item update.
 */
data class CompensationItemUpdateRequest(
    val oldImage: Compensation?,
    val newImage: Compensation?,
    val newRecord: Compensation?,
    val parentUpdateRequest: CompensationItemUpdateRequest? = null,
    val shouldPersist: Boolean = false,
    val schemaItem: CompensationSchemaItem?,
)

data class BulkCompensationItemUpdateRequest(
    val individualRequests: List<CompensationItemUpdateRequest>,
    val shouldPersist: Boolean = false,
)
