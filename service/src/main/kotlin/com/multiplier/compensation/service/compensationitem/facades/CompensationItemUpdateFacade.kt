package com.multiplier.compensation.service.compensationitem.facades

import com.multiplier.compensation.database.mapper.compensationItem.toKey
import com.multiplier.compensation.database.repository.compensationitem.CompensationItemRepository
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.service.compensationitem.dto.AdjustedItems
import com.multiplier.compensation.service.compensationitem.dto.BulkCompensationItemUpdateRequest
import com.multiplier.compensation.service.compensationitem.dto.BulkCompensationItemUpdateResponse
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemAdjustmentTransaction
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemUpdateRequest
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemUpdateResponse
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.update.handlers.UpdateHandlerFactory
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isItemUpdated
import com.multiplier.transaction.database.jooq.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class CompensationItemUpdateFacade(
    private val updateHandlerFactory: UpdateHandlerFactory,
    private val compensationItemRepository: CompensationItemRepository,
    private val transactional: Transactional,
) {
    fun previewSingleCompensationItemUpdate(request: CompensationItemUpdateRequest): CompensationItemUpdateResponse {
        val updateContext = initializeUpdateContext(request)
        val handler = updateHandlerFactory.getUpdateHandler(updateContext)
        val updatedContext = handler.handle(updateContext)
        val pendingTransaction = if (request.shouldPersist) {
            createAdjustmentTransaction(updatedContext.adjustedItems)
        } else {
            null
        }

        return CompensationItemUpdateResponse(
            oldImage = updatedContext.oldImage,
            updatedNewImage = updatedContext.newImage,
            updatedNewRecord = updatedContext.newRecord,
            adjustedItems = updatedContext.adjustedItems,
            pendingTransaction = pendingTransaction,
        )
    }

    fun previewBulkCompensationItemUpdate(
        request: BulkCompensationItemUpdateRequest,
    ): BulkCompensationItemUpdateResponse {
        val individualResponses = request.individualRequests.map {
            previewSingleCompensationItemUpdate(it).copy(pendingTransaction = null)
        }
        val adjustedItemsList = individualResponses.map { it.adjustedItems }
        val pendingTransaction = if (request.shouldPersist) {
            val consolidatedAdjustedItems = getConsolidatedAdjustmentItems(adjustedItemsList)
            createAdjustmentTransaction(consolidatedAdjustedItems)
        } else {
            null
        }

        return BulkCompensationItemUpdateResponse(
            individualResponses = individualResponses,
            pendingTransaction = pendingTransaction,
        )
    }

    private fun initializeUpdateContext(request: CompensationItemUpdateRequest): CompensationUpdateContext =
        CompensationUpdateContext(
            oldImage = request.oldImage,
            newImage = request.newImage,
            newRecord = request.newRecord,
            parentUpdateContext = request.parentUpdateRequest?.let {
                CompensationUpdateContext(
                    oldImage = it.oldImage,
                    newImage = it.newImage,
                    newRecord = it.newRecord,
                    existingItems = emptyList(),
                    adjustedItems = AdjustedItems(),
                    schemaItem = it.schemaItem,
                )
            },
            existingItems = fetchExistingItems(request),
            adjustedItems = AdjustedItems(),
            schemaItem = request.schemaItem,
        )

    private fun fetchExistingItems(request: CompensationItemUpdateRequest): List<CompensationItem> =
        request.oldImage?.let {
            transactional {
                compensationItemRepository.getAllValidCompensationItems(it.id, this)
            }
        }.orEmpty()

    /*
     * Segregation of updated and new items is achieved using the `updateTriggerReference` property of a compensation-item.
     * This field is set when an existing item is updated. A couple scenarios :
     * 1. when updating the status of an existing item as ABORTED or
     * 2. when updating the installment number and the associated compensation snapshot of an existing item
     */
    private fun createAdjustmentTransaction(adjustedItems: AdjustedItems): CompensationItemAdjustmentTransaction {
        val updatedItems = adjustedItems.oldImageItems.filter { isItemUpdated(it) } +
            adjustedItems.newImageItems.filter { isItemUpdated(it) }

        val newItems = adjustedItems.oldImageItems.filter { !isItemUpdated(it) } +
            adjustedItems.newImageItems.filter { !isItemUpdated(it) } +
            adjustedItems.newRecordItems

        return CompensationItemAdjustmentTransaction(
            updatedItems = updatedItems,
            newItems = newItems,
            compensationItemRepository = compensationItemRepository,
        )
    }

    private fun getConsolidatedAdjustmentItems(adjustedItemsList: List<AdjustedItems>): AdjustedItems = AdjustedItems(
        oldImageItems = adjustedItemsList.flatMap { it.oldImageItems }.distinctBy { it.toKey() },
        newImageItems = adjustedItemsList.flatMap { it.newImageItems }.distinctBy { it.toKey() },
        newRecordItems = adjustedItemsList.flatMap { it.newRecordItems }.distinctBy { it.toKey() },
    )
}
