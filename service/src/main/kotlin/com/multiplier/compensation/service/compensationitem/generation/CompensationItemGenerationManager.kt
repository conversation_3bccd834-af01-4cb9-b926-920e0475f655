package com.multiplier.compensation.service.compensationitem.generation

import com.fasterxml.uuid.Generators
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.common.exception.requireCondition
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.domain.payschedule.PayScheduleItem
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil
import com.multiplier.compensation.service.compensationitem.util.CompensationUtil.getCurrentInstallment
import com.multiplier.compensation.service.compensationitem.util.CompensationUtil.getNextItemStartDate
import com.multiplier.compensation.service.compensationitem.util.CompensationUtil.isItemGenerationComplete
import com.multiplier.compensation.service.compensationschema.CompensationSchemaService
import com.multiplier.compensation.service.payschedule.PayScheduleService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.LocalDateTime

private val log = KotlinLogging.logger {}
private val uuidGenerator = Generators.timeBasedEpochGenerator()

@Service
class CompensationItemGenerationManager(
    private val payScheduleService: PayScheduleService,
    private val compensationSchemaService: CompensationSchemaService,
) {
    /*
     * Generates items for a compensation till toDate.
     *
     * The function returns an empty list if :
     * 1. The compensation status is ABORTED or DELETED.
     * 2. The compensation start date is after the toDate.
     * 3. Item generation has already been completed.
     * 4. Item generation has already been completed till toDate.
     */
    fun generateItems(
        compensation: Compensation,
        toDate: LocalDate,
    ): List<CompensationItem> {
        log.info { "Generating items for compensation $compensation till requested date [$toDate]." }

        if (isInvalidRequest(compensation, toDate)) {
            return emptyList()
        }

        val adjustedDates = getAdjustedDatesForItemGeneration(compensation, toDate)
        val adjustedFromDate = adjustedDates.first
        val adjustedToDate = adjustedDates.second

        return generateCompensationItems(compensation, adjustedFromDate, adjustedToDate)
    }

    fun generateTruncatedItem(
        updatedCompensation: Compensation,
        affectedItem: CompensationItem,
        schemaItem: CompensationSchemaItem?,
    ): CompensationItem {
        requireCondition(
            condition = updatedCompensation.id == affectedItem.compensationId,
            errorCode = ValidationErrorCode.InvalidItemAdjustmentRequest,
            message = "Affected item does not correspond to the compensation.",
            context = mapOf(
                "updatedCompensationId" to updatedCompensation.id,
                "affectedItemId" to affectedItem.id,
                "affectedCompensationId" to affectedItem.compensationId,
            ),
        )

        val computedStartDate = maxOf(updatedCompensation.startDate, affectedItem.startDate)
        val computedEndDate = updatedCompensation.endDate?.let {
            minOf(it, affectedItem.endDate)
        } ?: affectedItem.endDate

        requireCondition(
            condition = computedStartDate.isAfter(affectedItem.startDate) ||
                computedEndDate.isBefore(affectedItem.endDate),
            errorCode = ValidationErrorCode.InvalidItemAdjustmentRequest,
            message = "No truncation required.",
            context = mapOf(
                "updatedParentCompensation" to updatedCompensation,
                "affectedItem" to affectedItem,
                "affectedCompensation.id" to affectedItem.compensationId,
            ),
        )

        return CompensationItem(
            id = uuidGenerator.generate(),
            companyId = updatedCompensation.companyId,
            entityId = updatedCompensation.entityId,
            contractId = updatedCompensation.contractId,
            compensationId = updatedCompensation.id,
            category = affectedItem.category,
            compensationStatus = updatedCompensation.status,
            compensationStartDate = updatedCompensation.startDate,
            compensationEndDate = updatedCompensation.endDate,
            schemaItemId = updatedCompensation.schemaItemId,
            currency = updatedCompensation.currency,
            billingRateType = updatedCompensation.billingRateType,
            billingRate = updatedCompensation.billingRate,
            billingFrequency = updatedCompensation.billingFrequency,
            payScheduleId = updatedCompensation.payScheduleId,
            startDate = computedStartDate,
            endDate = computedEndDate,
            isInstallment = affectedItem.isInstallment,
            noOfInstallments = updatedCompensation.noOfInstallments,
            currentInstallment = affectedItem.currentInstallment,
            cutOffDate = null,
            expectedPayDate = affectedItem.expectedPayDate,
            calculatedAmount = null,
            previousId = updatedCompensation.previousId,
            status = CompensationItemUtil.getItemStatus(schemaItem),
            isArrear = affectedItem.isArrear,
            createdOn = LocalDateTime.now(),
            createdBy = -1,
            updatedOn = LocalDateTime.now(),
            updatedBy = -1,
        )
    }

    private fun isInvalidRequest(
        compensation: Compensation,
        toDate: LocalDate,
    ): Boolean = isInvalidStatus(compensation) ||
        compensationStartsAfterToDate(compensation, toDate) ||
        isItemGenerationAlreadyComplete(compensation) ||
        nextItemStartsAfterToDate(compensation, toDate)

    private fun isInvalidStatus(compensation: Compensation): Boolean {
        if (compensation.status in listOf(CompensationStatus.ABORTED, CompensationStatus.DELETED)) {
            logInvalidRequest(
                reason = "The compensation status does not permit item generation.",
                compensation = compensation,
            )
            return true
        }
        return false
    }

    private fun compensationStartsAfterToDate(
        compensation: Compensation,
        toDate: LocalDate,
    ): Boolean {
        if (!toDate.isAfter(compensation.startDate)) {
            logInvalidRequest(
                reason = "The compensation starts after the specified toDate.",
                compensation = compensation,
                toDate = toDate,
            )
            return true
        }
        return false
    }

    private fun isItemGenerationAlreadyComplete(compensation: Compensation): Boolean {
        if (isItemGenerationComplete(compensation)) {
            logInvalidRequest(
                reason = "Item generation has already been completed for compensation.",
                compensation = compensation,
            )
            return true
        }
        return false
    }

    private fun nextItemStartsAfterToDate(
        compensation: Compensation,
        toDate: LocalDate,
    ): Boolean {
        val nextItemStartDate = getNextItemStartDate(compensation)
        if (nextItemStartDate.isAfter(toDate)) {
            logInvalidRequest(
                reason = "The next item start date is after the specified toDate.",
                compensation = compensation,
                toDate = toDate,
            )
            return true
        }
        return false
    }

    private fun getAdjustedDatesForItemGeneration(
        compensation: Compensation,
        toDate: LocalDate,
    ): Pair<LocalDate, LocalDate> {
        val adjustedFromDate = getNextItemStartDate(compensation)
        val adjustedToDate = getAdjustedToDate(compensation, toDate)
        log.debug {
            "Adjusted item generation dates for the compensationId [${compensation.id}] " +
                "to adjustedFromDate [$adjustedFromDate] and adjustedToDate [$adjustedToDate]."
        }
        return Pair(adjustedFromDate, adjustedToDate)
    }

    private fun getAdjustedToDate(
        compensation: Compensation,
        toDate: LocalDate,
    ): LocalDate = compensation.endDate?.let { minOf(it, toDate) } ?: toDate

    private fun generateCompensationItems(
        compensation: Compensation,
        adjustedFromDate: LocalDate,
        adjustedToDate: LocalDate,
    ): List<CompensationItem> {
        val schemaItem = getCompensationSchemaItem(compensation)
        val payScheduleItems = getApplicablePayScheduleItems(compensation, adjustedFromDate, adjustedToDate)
        val compensationItems = generateCompensationItems(compensation, payScheduleItems, schemaItem)
        log.info {
            "Generated ${compensationItems.count()} items for compensationId [${compensation.id}]"
        }
        return compensationItems
    }

    private fun getCompensationSchemaItem(compensation: Compensation): CompensationSchemaItem =
        compensationSchemaService.getSchemaItemById(compensation.schemaItemId)
            ?: error("Schema item not found for schemaItemId [${compensation.schemaItemId}]")

    /*
     * For a date range, PayScheduleService might return a bunch of items.
     * However, some might be irrelevant to the date range and some might not completely overlap with the date range.
     * As such,
     * - we need to filter out the payScheduleItems that do not overlap at all with the requested date range.
     * - we need to truncate the payScheduleItems to avoid generating items that
     *   1. overlap with existing items or
     *   2. extend before the compensation start date or beyond the end date.
     *   This truncation is bound by the input fromDate and compensation endDate (NOT toDate).
     */
    private fun getApplicablePayScheduleItems(
        compensation: Compensation,
        fromDate: LocalDate,
        toDate: LocalDate,
    ): List<PayScheduleItem> {
        val payScheduleItems = getPayScheduleItems(compensation, fromDate, toDate)
        log.debug {
            "Adjusting payScheduleItems for compensationId [${compensation.id}] " +
                "with upper bound [$fromDate] and lower bound [${compensation.endDate}]."
        }
        val applicablePayScheduleItems = payScheduleItems
            .filterNot { item -> item.endDate.isBefore(fromDate) }
            .filterNot { item ->
                item.startDate.isAfter(toDate) ||
                    (compensation.endDate != null && item.startDate.isAfter(compensation.endDate))
            }
            .map { item ->
                item.copy(
                    startDate = maxOf(item.startDate, fromDate),
                    endDate = compensation.endDate?.let { minOf(item.endDate, it) } ?: item.endDate,
                )
            }.sortedBy { it.payDate }

        logPayScheduleItems(applicablePayScheduleItems, compensation)
        return applicablePayScheduleItems
    }

    private fun getPayScheduleItems(
        compensation: Compensation,
        fromDate: LocalDate,
        toDate: LocalDate,
    ): List<PayScheduleItem> {
        log.info {
            "Fetching payScheduleItems for compensationId [${compensation.id}], " +
                "fromDate [$fromDate], toDate [$toDate]"
        }

        return payScheduleService.getPayScheduleItems(
            id = compensation.payScheduleId,
            from = fromDate,
            to = toDate,
        ).toList()
    }

    private fun logPayScheduleItems(
        payScheduleItems: List<PayScheduleItem>,
        compensation: Compensation,
    ) {
        log.info { "Fetched ${payScheduleItems.count()} payScheduleItems for compensationId [${compensation.id}]." }
        payScheduleItems.forEachIndexed { index, item ->
            log.debug { "PayScheduleItem $index: $item" }
        }
    }

    private fun generateCompensationItems(
        compensation: Compensation,
        payScheduleItems: List<PayScheduleItem>,
        schemaItem: CompensationSchemaItem,
    ): List<CompensationItem> {
        var currentInstallment = getCurrentInstallment(compensation)
        val compensationItems = mutableListOf<CompensationItem>()
        for (payScheduleItem in payScheduleItems) {
            val item = generateItem(payScheduleItem, compensation, schemaItem, currentInstallment)
            compensationItems.add(item)
            currentInstallment = currentInstallment?.inc()
        }
        return compensationItems
    }

    private fun generateItem(
        payScheduleItem: PayScheduleItem,
        compensation: Compensation,
        schemaItem: CompensationSchemaItem,
        currentInstallment: Int?,
    ): CompensationItem {
        log.debug {
            "Generating item for compensationId [${compensation.id}] and payScheduleItem [$payScheduleItem]."
        }

        return CompensationItem(
            id = uuidGenerator.generate(),
            companyId = compensation.companyId,
            entityId = compensation.entityId,
            contractId = compensation.contractId,
            compensationId = compensation.id,
            category = schemaItem.category,
            compensationStatus = compensation.status,
            compensationStartDate = compensation.startDate,
            compensationEndDate = compensation.endDate,
            schemaItemId = compensation.schemaItemId,
            currency = compensation.currency,
            billingRateType = compensation.billingRateType,
            billingRate = compensation.billingRate,
            billingFrequency = compensation.billingFrequency,
            payScheduleId = payScheduleItem.payScheduleId,
            startDate = payScheduleItem.startDate,
            endDate = payScheduleItem.endDate,
            isInstallment = compensation.isInstallment,
            noOfInstallments = compensation.noOfInstallments,
            currentInstallment = currentInstallment,
            cutOffDate = null,
            expectedPayDate = payScheduleItem.payDate,
            calculatedAmount = null,
            previousId = compensation.previousId,
            status = CompensationItemUtil.getItemStatus(schemaItem),
            isArrear = false,
            createdOn = LocalDateTime.now(),
            createdBy = -1,
            updatedOn = LocalDateTime.now(),
            updatedBy = -1,
        )
    }

    private fun logInvalidRequest(
        reason: String,
        compensation: Compensation,
        toDate: LocalDate,
    ) = log.error { "Invalid request [$reason] compensation [$compensation], toDate [$toDate]." }

    private fun logInvalidRequest(
        reason: String,
        compensation: Compensation,
    ) = log.error { "Invalid request [$reason] compensation [$compensation]." }
}
