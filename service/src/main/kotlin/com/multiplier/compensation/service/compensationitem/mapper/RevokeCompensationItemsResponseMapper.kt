package com.multiplier.compensation.service.compensationitem.mapper

import com.multiplier.compensation.service.common.dto.OperationStatus
import com.multiplier.compensation.service.common.dto.ResultType
import com.multiplier.compensation.service.compensationitem.dto.RevokeCompensationItemRequest
import com.multiplier.compensation.service.compensationitem.dto.RevokeCompensationItemResponse
import com.multiplier.compensation.service.compensationitem.dto.RevokeCompensationItemsRequest
import com.multiplier.compensation.service.compensationitem.dto.RevokeCompensationItemsResponse

fun RevokeCompensationItemRequest.toFailure(msg: String) = RevokeCompensationItemResponse(
    compensationItemId = this.compensationItemId,
    resultType = ResultType.ERROR,
    message = msg,
)

fun RevokeCompensationItemRequest.toSuccess() = RevokeCompensationItemResponse(
    compensationItemId = this.compensationItemId,
    resultType = ResultType.INFO,
    message = "Item Revoked Successfully",
)

fun buildRevokeCompensationItemsResponse(
    results: List<RevokeCompensationItemResponse>,
    request: RevokeCompensationItemsRequest,
): RevokeCompensationItemsResponse {
    val inaccessibleResults = request.inaccessibleRequests.map {
        RevokeCompensationItemResponse(
            compensationItemId = it.compensationItemId,
            resultType = ResultType.ERROR,
            message = "Access denied",
        )
    }
    val allResults = results + inaccessibleResults

    val hasErrors = allResults.any { it.resultType == ResultType.ERROR }
    val hasSuccess = allResults.any { it.resultType == ResultType.INFO }

    val status = when {
        hasErrors && hasSuccess -> OperationStatus.PARTIAL_SUCCESS
        hasErrors -> OperationStatus.FAILURE
        else -> OperationStatus.SUCCESS
    }

    return RevokeCompensationItemsResponse(
        status = status,
        results = allResults,
    )
}
