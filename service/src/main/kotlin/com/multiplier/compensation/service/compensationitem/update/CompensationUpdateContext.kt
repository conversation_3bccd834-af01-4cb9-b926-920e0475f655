package com.multiplier.compensation.service.compensationitem.update

import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.service.compensationitem.dto.AdjustedItems

/**
 * Context containing data for updating compensation records, including old and new states, related items, and adjustments.
 *
 * @property oldImage The original compensation record before any updates were applied.
 * @property newImage The updated compensation record reflecting inline changes to dates or the number of installments.
 * @property newRecord The new compensation record with new billing rate or other adjustments like updated dates or installments.
 * @property parentUpdateContext Update context for the parent compensation.
 * @property existingItems A list of compensation items associated with the original compensation record (`oldImage`).
 * @property adjustedItems A set of adjustments applied to the compensation items.
 * @property schemaItem The schema item associated with the compensation record.
 */
data class CompensationUpdateContext(
    val oldImage: Compensation?,
    val newImage: Compensation?,
    val newRecord: Compensation?,
    val parentUpdateContext: CompensationUpdateContext? = null,
    val existingItems: List<CompensationItem>,
    val adjustedItems: AdjustedItems,
    val schemaItem: CompensationSchemaItem?,
)
