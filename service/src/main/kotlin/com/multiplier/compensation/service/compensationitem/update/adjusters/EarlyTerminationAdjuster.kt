package com.multiplier.compensation.service.compensationitem.update.adjusters

import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.service.compensationitem.dto.AdjustedItems
import com.multiplier.compensation.service.compensationitem.generation.CompensationItemGenerationManager
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.update.managers.CancellationArrearManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemConsolidationManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemUpdateManager
import com.multiplier.compensation.service.compensationitem.update.managers.SpillOverItemManager
import com.multiplier.compensation.service.compensationitem.update.util.extractNewImage
import com.multiplier.compensation.service.compensationitem.update.util.updateContextWithAdjustments
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isCancellationArrear
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isProcessedItem
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

/*
 * This adjuster modifies compensation items when an existing compensation is early terminated,
 * either by updating the end date or decreasing the number of installments.
 *
 * Process:
 * 1. Invalidate items extending beyond the updated end date of the existing record
 *    a. Marks unprocessed items as aborted.
 *    b. Supplements processed items, that extend beyond the updated record's end date, with cancellation arrears.
 * 2. The invalidation process can result in a
 *    a. partial cancellation arrear generation corresponding to an existing processed spillover item OR
 *    b. a new truncated item corresponding to an existing unprocessed spillover item which was aborted.
 */
@Qualifier("earlyTerminationAdjuster")
@Service
class EarlyTerminationAdjuster(
    itemGenerationManager: CompensationItemGenerationManager,
    itemUpdateManager: CompensationItemUpdateManager,
    cancellationArrearManager: CancellationArrearManager,
    spillOverItemManager: SpillOverItemManager,
    consolidationManager: CompensationItemConsolidationManager,
) : CompensationAdjuster(
        itemGenerationManager,
        itemUpdateManager,
        cancellationArrearManager,
        spillOverItemManager,
        consolidationManager,
    ) {
    override fun adjust(context: CompensationUpdateContext): CompensationUpdateContext {
        super.validateContext(context)

        return context.updateContextWithAdjustments(
            AdjustedItems(
                oldImageItems = generateOldImageItems(context),
                newImageItems = generateNewImageItems(context),
                newRecordItems = emptyList(),
            ),
        )
    }

    override fun generateOldImageItems(context: CompensationUpdateContext): List<CompensationItem> {
        val (newCancellationArrears, abortedCancellationArrears) = generateCancellationArrearsForEligibleItems(context)
        log.info { "Generated [${newCancellationArrears.count()}] CancellationArrears." }

        val abortedItems = markEligibleItemsAsAborted(context) + abortedCancellationArrears
        log.info { "Marked [${abortedItems.count()}] existing items as aborted." }

        return abortedItems + newCancellationArrears
    }

    override fun generateNewImageItems(context: CompensationUpdateContext): List<CompensationItem> {
        val newImage = extractNewImage(context)
        val spillOverItem = super.getSpillOverItemAtCompensationEnd(
            compensation = newImage,
            existingItems = context.existingItems,
        )
        return spillOverItem?.takeIf { !newImage.isInstallment && !isProcessedItem(it) }?.let {
            log.info {
                "Generating truncated item corresponding to old image's unprocessed spillover item," +
                    " which was aborted."
            }
            listOf(
                super.generateTruncatedItem(
                    compensation = newImage,
                    affectedItem = spillOverItem,
                    schemaItem = context.schemaItem,
                ),
            )
        }.orEmpty()
    }

    private fun generateCancellationArrearsForEligibleItems(
        context: CompensationUpdateContext,
    ): Pair<List<CompensationItem>, List<CompensationItem>> {
        val newImage = extractNewImage(context)
        val (initialArrears, initialToBeAbortedArrears) = generateInitialCancellationArrears(context, newImage)
        val (finalArrears, finalToBeAbortedArrears) = handleSpillOverItem(
            context,
            newImage,
            initialArrears,
            initialToBeAbortedArrears,
        )
        val abortedArrears = super.markEligibleItemsAsAborted(
            triggerCompensation = newImage,
            affectedItems = finalToBeAbortedArrears,
        )

        return Pair(finalArrears, abortedArrears)
    }

    private fun generateInitialCancellationArrears(
        context: CompensationUpdateContext,
        newImage: Compensation,
    ): Pair<List<CompensationItem>, List<CompensationItem>> = super.generateCancellationArrearsForEligibleItems(
        associatedCompensation = newImage,
        triggerCompensation = newImage,
        affectedItems = fetchAffectedItems(context),
        existingItems = context.existingItems,
    )

    /*
     * If a spillOverItem is found, consider it for cancellation arrear generation.
     * If not, return the initial cancellation arrears.
     */
    private fun handleSpillOverItem(
        context: CompensationUpdateContext,
        newImage: Compensation,
        initialArrears: List<CompensationItem>,
        initialToBeAbortedArrears: List<CompensationItem>,
    ): Pair<List<CompensationItem>, List<CompensationItem>> {
        val spillOverItem = super.getSpillOverItemAtCompensationEnd(
            compensation = newImage,
            existingItems = context.existingItems,
        ) ?: return Pair(initialArrears, initialToBeAbortedArrears)
        val dateAdjustedSpillOverItem = getDateAdjustedSpillOverItemAtNewImageEnd(newImage, spillOverItem)
        val (newSpillOverArrear, toBeAbortedArrears) = generateSpillOverArrear(
            newImage,
            dateAdjustedSpillOverItem,
            context,
        )

        return Pair(
            initialArrears + listOfNotNull(newSpillOverArrear),
            initialToBeAbortedArrears + toBeAbortedArrears,
        )
    }

    private fun generateSpillOverArrear(
        newImage: Compensation,
        spillOverItem: CompensationItem,
        context: CompensationUpdateContext,
    ): Pair<CompensationItem?, List<CompensationItem>> = super.generateCancellationArrearForEligibleItem(
        associatedCompensation = newImage,
        triggerCompensation = newImage,
        affectedItem = spillOverItem,
        existingItems = context.existingItems,
    )

    /*
     * The spillOverItem needs to be date adjusted for arrear generation.
     * The corresponding arrear will have a start date = updated end date (newImage end date) + 1 days.
     */
    private fun getDateAdjustedSpillOverItemAtNewImageEnd(
        newImage: Compensation,
        spillOverItem: CompensationItem,
    ): CompensationItem {
        val newEndDate = requireNotNull(newImage.endDate) { "New image's end date should not be null." }
        return spillOverItem.copy(
            startDate = newEndDate.plusDays(1),
        )
    }

    private fun markEligibleItemsAsAborted(context: CompensationUpdateContext): List<CompensationItem> {
        val newImage = extractNewImage(context)
        val eligibleItems = fetchAffectedItems(context).filter { !isCancellationArrear(it) }
        val abortedItems = super.markEligibleItemsAsAborted(
            triggerCompensation = newImage,
            affectedItems = eligibleItems,
        )
        val spillOverItem = super.getSpillOverItemAtCompensationEnd(
            compensation = newImage,
            existingItems = context.existingItems,
        )
        val spillOverAbortedItem = spillOverItem?.let {
            super.markEligibleItemAsAborted(
                triggerCompensation = newImage,
                affectedItem = it,
            )
        }

        return abortedItems + listOfNotNull(spillOverAbortedItem)
    }

    override fun fetchAffectedItems(context: CompensationUpdateContext): List<CompensationItem> {
        val newImage = extractNewImage(context)
        return context.existingItems.filter { it.startDate.isAfter(newImage.endDate) }
    }
}
