package com.multiplier.compensation.service.compensationitem.update.adjusters

import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.service.compensationitem.dto.AdjustedItems
import com.multiplier.compensation.service.compensationitem.generation.CompensationItemGenerationManager
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.update.managers.CancellationArrearManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemConsolidationManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemUpdateManager
import com.multiplier.compensation.service.compensationitem.update.managers.SpillOverItemManager
import com.multiplier.compensation.service.compensationitem.update.util.extractNewRecord
import com.multiplier.compensation.service.compensationitem.update.util.updateContextWithAdjustments
import com.multiplier.compensation.service.compensationitem.util.CompensationUtil.isNewCompensationEligibleForItemGeneration
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.LocalDate

private val log = KotlinLogging.logger {}

/*
 * This adjuster generates compensation items when a new compensation record is added
 * and no prior active compensation exists. Hence, item invalidation is not needed.
 *
 * Process:
 * 1. Generate New Compensation Items:
 *    a. If the compensation is relevant in the upcoming payroll cycle i.e. will have an item in the next 'generationPeriodInDays'
 *       i. Generate new items for the updated compensation
 *    b. If the compensation starts is not relevant in the upcoming payroll cycle i.e. will not have an item in the next 'generationPeriodInDays'
 *       i. Skip item generation, as it will be handled by the item generation job.
 */
@Qualifier("newComponentAdjuster")
@Service
class NewComponentAdjuster(
    itemGenerationManager: CompensationItemGenerationManager,
    itemUpdateManager: CompensationItemUpdateManager,
    cancellationArrearManager: CancellationArrearManager,
    spillOverItemManager: SpillOverItemManager,
    consolidationManager: CompensationItemConsolidationManager,
    @Value("\${core.scheduler.compensation-item-generation.generation-period-in-days}")
    private val generationPeriodInDays: Long,
) : CompensationAdjuster(
        itemGenerationManager,
        itemUpdateManager,
        cancellationArrearManager,
        spillOverItemManager,
        consolidationManager,
    ) {
    override fun adjust(context: CompensationUpdateContext): CompensationUpdateContext {
        validateContext(context)

        return context.updateContextWithAdjustments(
            AdjustedItems(
                oldImageItems = emptyList(),
                newImageItems = emptyList(),
                newRecordItems = generateNewRecordItems(context),
            ),
        )
    }

    override fun generateNewRecordItems(context: CompensationUpdateContext): List<CompensationItem> {
        val newRecord = extractNewRecord(context)

        return if (isNewCompensationEligibleForItemGeneration(newRecord, generationPeriodInDays)) {
            log.info { "The new compensation might be relevant for the upcoming payroll cycle. Generating items." }
            super.generateItems(
                compensation = newRecord,
                toDate = LocalDate.now().plusDays(generationPeriodInDays),
            )
        } else {
            log.info {
                "The new compensation starts in the future and will not be relevant for the upcoming payroll cycle. " +
                    "Skipping item generation."
            }
            emptyList()
        }
    }

    override fun validateContext(context: CompensationUpdateContext) {
        require(context.oldImage == null) { "Old image should be null." }
        require(context.newImage == null) { "New image should be null." }
        requireNotNull(context.newRecord) { "New record should not be null." }
        require(context.parentUpdateContext == null) { "Parent update context should be null." }
    }
}
