package com.multiplier.compensation.service.compensationitem.update.managers

import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.service.compensationitem.util.CancellationArrearUtil.doArrearsCoverItemCompletely
import com.multiplier.compensation.service.compensationitem.util.CancellationArrearUtil.extractArrearsForItem
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isAbortedItem
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isCancellationArrear
import org.springframework.stereotype.Service

/*
 * NOTE: Multiple spill-over items may exist from previous updates,
 * but at most one spill-over item can exist without full coverage by the cancellation arrears.
 *
 * The methods process the existing items; for each item :
 * a. it checks if it is a potential spillOverItem
 * b. it extracts the existing cancellation arrears for the item
 * c. it checks if the arrears completely cover the item.
 * d. if the arrears do not completely cover with the item, the item is returned.
 */
@Service
class SpillOverItemManager {
    /*
     * A spillOverItem in this case is an item (non-cancellation arrear) that starts before the compensation start date and
     * ends on or after the compensation start date.
     */
    fun getSpillOverItemAtCompensationStart(
        compensation: Compensation,
        existingItems: List<CompensationItem>,
    ): CompensationItem? {
        val startDate = compensation.startDate

        return existingItems
            .filter { !isAbortedItem(it) && !isCancellationArrear(it) }
            .filter {
                it.startDate.isBefore(startDate) &&
                    (it.endDate == startDate || it.endDate.isAfter(startDate))
            }
            .firstOrNull { item ->
                val arrearsForItem = extractArrearsForItem(item, existingItems)
                !doArrearsCoverItemCompletely(arrearsForItem, item)
            }
    }

    /*
     * A spillOverItem in this case is an item (non-cancellation arrear) that starts before or on the compensation end date and
     * ends after the compensation end date.
     */
    fun getSpillOverItemAtCompensationEnd(
        compensation: Compensation,
        existingItems: List<CompensationItem>,
    ): CompensationItem? {
        val endDate = compensation.endDate ?: return null

        return existingItems
            .filter { !isAbortedItem(it) && !isCancellationArrear(it) }
            .filter {
                (it.startDate.isBefore(endDate) || it.startDate == endDate) &&
                    it.endDate.isAfter(endDate)
            }
            .firstOrNull { item ->
                val arrearsForItem = extractArrearsForItem(item, existingItems)
                !doArrearsCoverItemCompletely(arrearsForItem, item)
            }
    }
}
