package com.multiplier.compensation.service.compensationitem.update.processors

import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.update.adjusters.CompensationAdjuster
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Qualifier("noOfInstallmentsUpdateProcessor")
@Service
class NoOfInstallmentsUpdateProcessor(
    @Qualifier("earlyTerminationAdjuster") private val earlyTerminationAdjuster: CompensationAdjuster,
    @Qualifier("extensionAdjuster") private val extensionAdjuster: CompensationAdjuster,
) : CompensationUpdateProcessor {
    override fun process(context: CompensationUpdateContext): CompensationUpdateContext {
        val oldImage = requireNotNull(context.oldImage) { "Old image should not be null." }
        val newImage = requireNotNull(context.newImage) { "New image should not be null." }
        require(context.newRecord == null) { "New record should be null." }
        require(context.parentUpdateContext == null) { "Parent update context should be null." }

        checkForCompensationTypeMismatch(oldImage, newImage)

        val oldNoOfInstallments = oldImage.noOfInstallments
        val newNoOfInstallments = newImage.noOfInstallments

        return when {
            oldNoOfInstallments == null || newNoOfInstallments == null -> {
                log.info { "Compensation is not of installment type, skipping NoOfInstallmentsUpdateAdjuster." }
                context
            }
            oldNoOfInstallments == newNoOfInstallments -> {
                log.info { "Compensation noOfInstallments remains unchanged. No adjustment needed." }
                context
            }
            oldNoOfInstallments < newNoOfInstallments -> {
                log.info {
                    "Compensation noOfInstallments has been increased. Delegating to ExtensionAdjuster."
                }
                extensionAdjuster.adjust(context)
            }
            else -> {
                log.info {
                    "Compensation noOfInstallments has been decreased. Delegating to EarlyTerminationAdjuster."
                }
                earlyTerminationAdjuster.adjust(context)
            }
        }
    }

    private fun checkForCompensationTypeMismatch(
        oldImage: Compensation,
        newImage: Compensation,
    ) {
        val isInstallmentMismatch = oldImage.isInstallment != newImage.isInstallment
        val noOfInstallmentsMismatch = (oldImage.noOfInstallments == null) != (newImage.noOfInstallments == null)

        if (isInstallmentMismatch || noOfInstallmentsMismatch) {
            throw InvalidArgumentException(
                errorCode = ValidationErrorCode.NumberOfInstallmentsMismatch,
                message = "Compensation type mismatch. Both old and new images should be of installment type.",
            )
        }
    }
}
