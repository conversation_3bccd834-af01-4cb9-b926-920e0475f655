package com.multiplier.compensation.service.compensationitem.update.util

import com.multiplier.compensation.database.mapper.compensationItem.toKey
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.service.compensationitem.dto.AdjustedItems
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.util.CancellationArrearUtil.extractArrearsForItem
import com.multiplier.compensation.service.compensationitem.util.CancellationArrearUtil.extractItemForGapInArrearCoverage
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isAbortedItem
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isCancellationArrear
import io.github.oshai.kotlinlogging.KotlinLogging

private val log = KotlinLogging.logger {}

fun CompensationUpdateContext.updateContextWithAdjustments(newAdjustedItems: AdjustedItems): CompensationUpdateContext {
    val updatedAdjustedItems = getUpdatedAdjustedItems(
        this.adjustedItems,
        newAdjustedItems,
    )

    return this.copy(
        newImage = this.newImage?.let {
            updateNewImageWithItemGenerationContext(
                it,
                this.existingItems,
                updatedAdjustedItems.oldImageItems,
                updatedAdjustedItems.newImageItems,
            )
        },
        newRecord = this.newRecord?.let {
            updateNewRecordWithItemGenerationContext(
                it,
                updatedAdjustedItems.newRecordItems,
            )
        },
        adjustedItems = updatedAdjustedItems,
    )
}

/*
 * Adjustments can overlap between different adjusters, hence they need to be consolidated and de-duplicated.
 *
 * For instance, in the case of postponement + early termination
 *  - same spill over item can get aborted
 *  - same truncated new item can get generated
 */
private fun getUpdatedAdjustedItems(
    existingAdjustedItems: AdjustedItems,
    newAdjustedItems: AdjustedItems,
): AdjustedItems = AdjustedItems(
    oldImageItems = (existingAdjustedItems.oldImageItems + newAdjustedItems.oldImageItems).distinctBy { it.toKey() },
    newImageItems = (existingAdjustedItems.newImageItems + newAdjustedItems.newImageItems).distinctBy { it.toKey() },
    newRecordItems = (existingAdjustedItems.newRecordItems + newAdjustedItems.newRecordItems).distinctBy { it.toKey() },
)

/*
 * Updates the `newImage` with context from the latest generated or retained items, to ensure continuity in item generation.
 *
 * This method processes and combines retained items (valid, pre-existing items) and newly generated items,
 * then finds the item with the latest `endDate`. Based on the latest item:
 * - If no items are retained or generated, resets `processedUntilDate` and `generatedInstallments` in `newImage`.
 * - If a valid latest item is found, updates `newImage` with:
 *   - `processedUntilDate` set to the end date of the latest item.
 *   - `generatedInstallments` set to the current installment number of the latest item.
 *
 * The method ensures chronology by preserving the `previousId` of `newImage`.
 */
private fun updateNewImageWithItemGenerationContext(
    newImage: Compensation,
    existingItems: List<CompensationItem>,
    oldImageItems: List<CompensationItem>,
    newImageItems: List<CompensationItem>,
): Compensation {
    val retainedItems = extractRetainedItems(existingItems, oldImageItems, newImageItems)
    val lastItem = (retainedItems + newImageItems).maxByOrNull { it.endDate }

    if (lastItem == null) {
        log.info {
            "No retained or valid existing items found, and no new items generated for the new image."
        }
        return newImage.copy(
            processedUntilDate = null,
            generatedInstallments = null,
        ).also {
            it.previousId = newImage.previousId
        }
    }

    log.info { "Updating item generation context with the latest retained or newly generated item." }
    val lastItemEndDate = lastItem.endDate
    val lastGeneratedInstallmentNumber = lastItem.currentInstallment
    return newImage.copy(
        processedUntilDate = lastItemEndDate,
        generatedInstallments = lastGeneratedInstallmentNumber,
    ).also {
        it.previousId = newImage.previousId
    }
}

/*
 * Updates the `newRecord` with context from the latest generated items, to ensure continuity in item generation.
 *
 * This method identifies the latest item in `newRecordItems` based on `endDate` and, if found:
 * - Sets `processedUntilDate` in `newRecord` to the `endDate` of the latest item.
 * - Sets `generatedInstallments` to the installment number of the latest item.
 *
 * If no items are found in `newRecordItems`, logs the absence and returns `newRecord` unmodified.
 *
 * This method ensures chronology by preserving the `previousId` of `newRecord`.
 */
private fun updateNewRecordWithItemGenerationContext(
    newRecord: Compensation,
    newRecordItems: List<CompensationItem>,
): Compensation {
    val lastItem = newRecordItems.maxByOrNull { it.endDate }

    if (lastItem == null) {
        log.info { "No items generated for the new record. Skipping item generation context update." }
        return newRecord
    }

    log.info { "Updating item generation context with the latest generated item for new record." }
    val lastItemEndDate = lastItem.endDate
    val lastGeneratedInstallmentNumber = lastItem.currentInstallment
    return newRecord.copy(
        processedUntilDate = lastItemEndDate,
        generatedInstallments = lastGeneratedInstallmentNumber,
    ).also {
        it.previousId = newRecord.previousId
    }
}

/*
 * Extracts and returns a list of compensation items from `existingItems` that remain valid after an update.
 *
 * This method performs the following steps:
 * - Identifies candidate items from `existingItems` that:
 *   - Are not cancellation-arrear items.
 *   - Are not aborted and remain relevant after the update.
 *   - Were not updated in the new compensation data
 * - For each candidate item
 *   - Retrieves any existing, non-aborted cancellation arrears from `existingItems` (retained arrears).
 *   - Identifies new cancellation arrears generated by the update from `oldImageItems`.
 *   - Calculates any gaps in arrear coverage by comparing retained and new arrears.
 *   - Adds an adjusted item, covering any gaps in arrear coverage, to the retained items list (gap item).
 *
 * The resulting list consists of non-overlapping compensation items or their truncated forms, ensuring no duplicated coverage.
 */
private fun extractRetainedItems(
    existingItems: List<CompensationItem>,
    oldImageItems: List<CompensationItem>,
    newImageItems: List<CompensationItem>,
): List<CompensationItem> {
    val updatedItemIds = newImageItems
        .filter { it.updateTriggerReference != null }
        .map { it.id }
        .toSet()
    val candidateItems = extractUnAbortedItems(existingItems, oldImageItems)
        .filterNot { it.id in updatedItemIds }
    val retainedItems = mutableListOf<CompensationItem>()

    candidateItems.forEach { item ->
        val retainedArrearsForItem = extractRetainedArrearsForItem(item, existingItems, oldImageItems)
        val newArrears = extractNewArrearsForItem(item, oldImageItems)
        val gapItem = extractItemForGapInArrearCoverage(item, retainedArrearsForItem + newArrears)
        retainedItems += listOfNotNull(gapItem)
    }

    return retainedItems
}

private fun extractUnAbortedItems(
    existingItems: List<CompensationItem>,
    oldImageItems: List<CompensationItem>,
): List<CompensationItem> {
    val abortedItemIds = oldImageItems
        .filter { !isCancellationArrear(it) && isAbortedItem(it) }
        .map { it.id }
        .toSet()
    return existingItems
        .filter { !isAbortedItem(it) && !isCancellationArrear(it) }
        .filterNot { it.id in abortedItemIds }
}

private fun extractRetainedArrearsForItem(
    item: CompensationItem,
    existingItems: List<CompensationItem>,
    oldImageItems: List<CompensationItem>,
): List<CompensationItem> {
    val arrearsForItem = extractArrearsForItem(item, existingItems)
    val abortedArrearsIdsForItem = oldImageItems
        .filter { isCancellationArrear(it) && isAbortedItem(it) && it.arrearOf == item.id }
        .map { it.id }
        .toSet()

    return arrearsForItem.filterNot { abortedArrearsIdsForItem.contains(it.id) }
}

private fun extractNewArrearsForItem(
    item: CompensationItem,
    oldImageItems: List<CompensationItem>,
): List<CompensationItem> = oldImageItems
    .filter { isCancellationArrear(it) && !isAbortedItem(it) && it.arrearOf == item.id }

fun extractOldImage(context: CompensationUpdateContext): Compensation = requireNotNull(context.oldImage) {
    "Old image should not be null."
}

fun extractNewImage(context: CompensationUpdateContext): Compensation = requireNotNull(context.newImage) {
    "New image should not be null."
}

fun extractNewRecord(context: CompensationUpdateContext): Compensation = requireNotNull(context.newRecord) {
    "New record should not be null."
}

fun extractParentOldImage(context: CompensationUpdateContext): Compensation {
    val parentUpdateContext = requireNotNull(context.parentUpdateContext) {
        "Parent update context should not be null."
    }
    return requireNotNull(parentUpdateContext.oldImage) {
        "Parent update context's old image should not be null."
    }
}

fun extractParentNewImage(context: CompensationUpdateContext): Compensation {
    val parentUpdateContext = requireNotNull(context.parentUpdateContext) {
        "Parent update context should not be null."
    }
    return requireNotNull(parentUpdateContext.newImage) {
        "Parent update context's new image should not be null."
    }
}

fun extractParentNewRecord(context: CompensationUpdateContext): Compensation {
    val parentUpdateContext = requireNotNull(context.parentUpdateContext) {
        "Parent update context should not be null."
    }
    return requireNotNull(parentUpdateContext.newRecord) {
        "Parent update context's new record should not be null."
    }
}
