package com.multiplier.compensation.service.compensationitem.util

import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.common.exception.requireCondition
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isAbortedItem
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isCancellationArrear
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isProcessedItem
import java.time.LocalDate

object CancellationArrearUtil {
    fun isItemEligibleForArrearGeneration(item: CompensationItem): Boolean =
        !isAbortedItem(item) && !isCancellationArrear(item) && isProcessedItem(item)

    fun extractArrearsForItem(
        item: CompensationItem,
        existingItems: List<CompensationItem>,
    ): List<CompensationItem> = existingItems
        .filter { isCancellationArrear(it) && !isAbortedItem(it) && it.arrearOf == item.id }
        .toList()

    fun doArrearsCoverItemCompletely(
        arrearsForItem: List<CompensationItem>,
        item: CompensationItem,
    ): Boolean {
        if (arrearsForItem.isEmpty()) return false

        val sortedArrears = arrearsForItem.sortedBy { it.startDate }
        var coverageEndDate = item.startDate

        for (arrear in sortedArrears) {
            if (hasGapBetweenCoverageAndArrear(coverageEndDate, arrear)) return false

            coverageEndDate = extendCoverageEndDate(coverageEndDate, arrear)

            if (isItemCompletelyCovered(coverageEndDate, item.endDate)) return true
        }

        return false
    }

    /*
     * This function extracts a compensation item for arrear generation based on the date range in the input item
     * which is not covered by existing arrears for the item.
     */
    fun extractItemForGapInArrearCoverage(
        affectedItem: CompensationItem,
        arrearsForItem: List<CompensationItem>,
    ): CompensationItem? = when {
        arrearsForItem.isEmpty() -> affectedItem

        doArrearsCoverItemCompletely(arrearsForItem, affectedItem) -> null

        else -> {
            buildItemForGapInArrearCoverage(affectedItem, arrearsForItem)
        }
    }

    /*
     * This function extracts a compensation item for arrear generation based on the date range in the input item
     * which is not covered by existing processed arrears for the item.
     */
    fun extractItemForGapInProcessedArrearCoverage(
        affectedItem: CompensationItem,
        processedArrearsForItem: List<CompensationItem>,
    ): CompensationItem? = when {
        processedArrearsForItem.isEmpty() -> affectedItem

        doArrearsCoverItemCompletely(processedArrearsForItem, affectedItem) -> null

        else -> {
            buildItemForGapInArrearCoverage(affectedItem, processedArrearsForItem)
        }
    }

    private fun buildItemForGapInArrearCoverage(
        item: CompensationItem,
        arrearsForItem: List<CompensationItem>,
    ): CompensationItem? {
        val sortedArrears = arrearsForItem.sortedBy { it.startDate }
        val missingCoverageItems = generateItemsForMissingCoverage(item, sortedArrears)

        requireCondition(
            condition = missingCoverageItems.count() <= 1,
            errorCode = ValidationErrorCode.InvalidCancellationArrearCoverageForItem,
            message = "More than 1 non-consecutive gap identified in the arrear coverage for the item $item.",
            context = mapOf(
                "item" to item,
                "arrearsForItem" to sortedArrears,
                "missingCoverageItems" to missingCoverageItems,
            ),
        )

        return if (missingCoverageItems.isEmpty()) {
            null
        } else {
            missingCoverageItems[0]
        }
    }

    private fun generateItemsForMissingCoverage(
        item: CompensationItem,
        arrears: List<CompensationItem>,
    ): List<CompensationItem> {
        val truncatedItems = mutableListOf<CompensationItem>()
        var currentCoverageEnd = item.startDate

        for (arrear in arrears) {
            if (hasGapBetweenCoverageAndArrear(currentCoverageEnd, arrear)) {
                val truncatedItem = createTruncatedItem(item, currentCoverageEnd, arrear.startDate.minusDays(1))
                truncatedItems.add(truncatedItem)
            }
            currentCoverageEnd = extendCoverageEndDate(currentCoverageEnd, arrear)
        }

        if (currentCoverageEnd <= item.endDate) {
            val truncateItem = createTruncatedItem(item, currentCoverageEnd, item.endDate)
            truncatedItems.add(truncateItem)
        }

        return truncatedItems
    }

    // Check if there is a gap between current coverage and the next arrear
    private fun hasGapBetweenCoverageAndArrear(
        coverageEndDate: LocalDate,
        arrear: CompensationItem,
    ): Boolean = arrear.startDate > coverageEndDate

    // Extend coverage to include the end of the current arrear
    private fun extendCoverageEndDate(
        coverageEndDate: LocalDate,
        arrear: CompensationItem,
    ): LocalDate = maxOf(coverageEndDate, arrear.endDate.plusDays(1))

    // Determine if the coverage completely overlaps the item’s date range
    private fun isItemCompletelyCovered(
        coverageEndDate: LocalDate,
        itemEndDate: LocalDate,
    ): Boolean = coverageEndDate >= itemEndDate

    // Create a truncated item covering a specified date range
    private fun createTruncatedItem(
        item: CompensationItem,
        start: LocalDate,
        end: LocalDate,
    ): CompensationItem = item.copy(startDate = start, endDate = end)
}
