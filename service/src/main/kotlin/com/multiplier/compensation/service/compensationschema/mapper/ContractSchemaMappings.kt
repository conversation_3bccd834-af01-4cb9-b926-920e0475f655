package com.multiplier.compensation.service.compensationschema.mapper

import com.fasterxml.uuid.impl.TimeBasedEpochGenerator
import com.multiplier.compensation.domain.compensationschema.ContractSchemaMapping
import com.multiplier.compensation.domain.compensationschema.ContractSchemaMappingReasonCode
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

object ContractSchemaMappings {
    fun build(
        contractId: Long,
        schemaId: UUID,
        effectiveStartDate: LocalDate,
        effectiveEndDate: LocalDate? = null,
        reasonCode: ContractSchemaMappingReasonCode,
        timeBasedEpochGenerator: TimeBasedEpochGenerator,
        isDeleted: Boolean = false,
    ): ContractSchemaMapping {
        val now = LocalDateTime.now()
        return ContractSchemaMapping(
            id = timeBasedEpochGenerator.generate(),
            contractId = contractId,
            schemaId = schemaId,
            effectiveStartDate = effectiveStartDate,
            effectiveEndDate = effectiveEndDate,
            reasonCode = reasonCode,
            isDeleted = isDeleted,
            createdOn = now,
            createdBy = -1,
            updatedOn = now,
            updatedBy = -1,
        )
    }
}
