package com.multiplier.compensation.service.compensationschema.validation

import com.multiplier.compensation.service.common.validationpipeline.common.stages.PrimaryValidationStage
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaDraft
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaValidationContext
import com.multiplier.compensation.service.compensationschema.validation.validators.SchemaComponentNameValidator
import com.multiplier.compensation.service.compensationschema.validation.validators.SchemaIsFixedValidator
import org.springframework.stereotype.Component

@Component
class CompensationSchemaValidationStage(
    schemaComponentNameValidator: SchemaComponentNameValidator,
    schemaIsFixedValidator: SchemaIsFixedValidator,
) : PrimaryValidationStage<CompensationSchemaValidationContext, CompensationSchemaDraft>() {
    init {
        setValidators(
            schemaComponentNameValidator,
            schemaIsFixedValidator,
        )
    }
}
