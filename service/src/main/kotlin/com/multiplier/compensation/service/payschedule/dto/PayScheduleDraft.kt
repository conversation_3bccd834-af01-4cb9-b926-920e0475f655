package com.multiplier.compensation.service.payschedule.dto

import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.common.validationpipeline.ValidationDraftObject
import java.time.LocalDate

data class PayScheduleDraft(
    val companyId: Long?,
    val name: String?,
    val countryCode: CountryCode?,
    val frequency: PayScheduleFrequency?,
    val configurationScope: ConfigurationScope?,
    val startDateReference: LocalDate?,
    val endDateReference: LocalDate?,
    val relativePayDays: Long?,
    val payDateReferenceType: PayDateReference?,
    val isInstallment: Boolean?,
    val label: String?,
) : ValidationDraftObject
