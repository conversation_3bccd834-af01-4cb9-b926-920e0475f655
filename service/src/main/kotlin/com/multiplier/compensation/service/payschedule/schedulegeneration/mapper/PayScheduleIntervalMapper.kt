package com.multiplier.compensation.service.payschedule.schedulegeneration.mapper

import com.fasterxml.uuid.impl.TimeBasedEpochGenerator
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.PayScheduleItem
import com.multiplier.compensation.service.payschedule.schedulegeneration.dto.PayScheduleInterval
import java.time.LocalDateTime

fun Collection<PayScheduleInterval>.toPayScheduleItems(
    paySchedule: PaySchedule,
    idGenerator: TimeBasedEpochGenerator,
): List<PayScheduleItem> = this.map {
    it.toPayScheduleItem(
        paySchedule = paySchedule,
        idGenerator = idGenerator,
    )
}

fun PayScheduleInterval.toPayScheduleItem(
    paySchedule: PaySchedule,
    idGenerator: TimeBasedEpochGenerator,
): PayScheduleItem = PayScheduleItem(
    id = idGenerator.generate(),
    payScheduleId = paySchedule.id,
    entityId = paySchedule.entityId,
    companyId = paySchedule.companyId,
    name = paySchedule.name,
    frequency = paySchedule.frequency,
    startDate = this.startDate,
    endDate = this.endDate,
    payDate = this.payDate,
    isActive = paySchedule.isActive,
    createdOn = LocalDateTime.now(),
    createdBy = paySchedule.createdBy,
    updatedOn = LocalDateTime.now(),
    updatedBy = paySchedule.updatedBy,
)
