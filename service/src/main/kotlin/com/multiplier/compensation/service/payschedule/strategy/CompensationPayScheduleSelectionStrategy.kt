package com.multiplier.compensation.service.payschedule.strategy

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.payschedule.GetEligibleBillingFrequenciesRequest
import com.multiplier.compensation.domain.payschedule.GetEligiblePayScheduleRequest
import com.multiplier.compensation.domain.payschedule.PaySchedule

/**
 * Strategy interface for handling pay schedule operations for different offering types.
 * Follows the Strategy pattern to separate offering-specific logic.
 */
interface CompensationPayScheduleSelectionStrategy {
    /**
     * Gets eligible pay schedules for the given request.
     */
    fun getEligiblePaySchedules(request: GetEligiblePayScheduleRequest): List<PaySchedule>

    /**
     * Gets eligible billing frequencies for the given request.
     */
    fun getEligibleBillingFrequencies(request: GetEligibleBillingFrequenciesRequest): List<BillingFrequency>

    /**
     * Validates the request before processing.
     */
    fun validateRequest(request: GetEligiblePayScheduleRequest)

    /**
     * Validates the billing frequencies request before processing.
     */
    fun validateBillingFrequenciesRequest(request: GetEligibleBillingFrequenciesRequest)
}
