package com.multiplier.compensation.service.payschedule.strategy

import com.multiplier.compensation.domain.common.OfferingCode
import org.springframework.stereotype.Component

/**
 * Factory for creating appropriate PayScheduleStrategy based on offering code.
 * Follows the Factory pattern to encapsulate strategy creation logic.
 */
@Component
class CompensationPayScheduleSelectionStrategyFactory(
    private val globalPayrollStrategy: GlobalPayrollCompensationPayScheduleSelectionStrategy,
    private val eorStrategy: EorCompensationPayScheduleSelectionStrategy,
) {
    /**
     * Returns the appropriate strategy for the given offering code.
     */
    fun getStrategy(offeringCode: OfferingCode): CompensationPayScheduleSelectionStrategy = when (offeringCode) {
        OfferingCode.GLOBAL_PAYROLL -> globalPayrollStrategy
        OfferingCode.EOR -> eorStrategy
    }
}
