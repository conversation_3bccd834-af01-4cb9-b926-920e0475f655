package com.multiplier.compensation.service.payschedule.util

import com.multiplier.compensation.domain.compensationschema.CompensationSchema
import com.multiplier.compensation.domain.payschedule.PaySchedule
import java.util.UUID

/**
 * Utility functions for working with pay schedules in compensation schemas.
 */
object PayScheduleUtils {
    /**
     * Builds a map of schema item IDs to pay schedule names for the given compensation schemas.
     *
     * This function extracts all pay schedule IDs from the schema items, fetches the corresponding
     * pay schedules, and creates a mapping from schema item ID to pay schedule name.
     *
     * @param schemas List of compensation schemas to process
     * @param payScheduleService Service to fetch pay schedule details
     * @return Map where keys are schema item IDs and values are pay schedule names
     */
    fun buildPayScheduleNamesMap(
        schemas: List<CompensationSchema>,
        paySchedules: Collection<PaySchedule>,
    ): Map<UUID, String> {
        val payScheduleIdToNameMap = paySchedules.associateBy({ it.id }, { it.name })

        return schemas.flatMap { schema ->
            schema.schemaItems.mapNotNull { schemaItem ->
                schemaItem.payScheduleId?.let { payScheduleId ->
                    payScheduleIdToNameMap[payScheduleId]?.let { payScheduleName ->
                        schemaItem.id to payScheduleName
                    }
                }
            }
        }.toMap()
    }
}
