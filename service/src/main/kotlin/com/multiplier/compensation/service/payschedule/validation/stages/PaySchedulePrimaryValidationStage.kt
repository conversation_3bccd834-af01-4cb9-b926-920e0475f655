package com.multiplier.compensation.service.payschedule.validation.stages

import com.multiplier.compensation.service.common.validationpipeline.common.stages.PrimaryValidationStage
import com.multiplier.compensation.service.payschedule.dto.PayScheduleDraft
import com.multiplier.compensation.service.payschedule.dto.PayScheduleValidatorContext
import com.multiplier.compensation.service.payschedule.validation.validators.PayScheduleCountryCodeValidator
import com.multiplier.compensation.service.payschedule.validation.validators.PayScheduleDuplicationValidator
import org.springframework.stereotype.Component

@Component
class PaySchedulePrimaryValidationStage(
    payScheduleCountryCodeValidator: PayScheduleCountryCodeValidator,
    payScheduleDuplicationValidator: PayScheduleDuplicationValidator,
) : PrimaryValidationStage<PayScheduleValidatorContext, PayScheduleDraft>() {
    init {
        setValidators(
            payScheduleCountryCodeValidator,
            payScheduleDuplicationValidator,
        )
    }
}
