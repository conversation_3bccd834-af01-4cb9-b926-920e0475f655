package com.multiplier.compensation.service.skeleton.enricher

import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.service.common.bulkplatform.BulkJsonCustomParamsUtil
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.skeleton.structure.SkeletonStructureData
import org.springframework.stereotype.Component

@Component
class BillingRateTypeEnricher : Enricher {
    override fun enrich(
        skeletonType: SkeletonType,
        entityId: Long,
        customParams: Map<String, String>,
        skeletonData: SkeletonStructureData,
    ): List<String> {
        val offeringType = BulkJsonCustomParamsUtil.getOfferingType(customParams)

        return when (offeringType) {
            OfferingType.EOR -> {
                listOf(
                    BillingRateType.VALUE.description,
                    BillingRateType.BASE_PAY_PERCENTAGE.description,
                )
            }
            else -> {
                listOf(BillingRateType.VALUE.description)
            }
        }
    }
}
