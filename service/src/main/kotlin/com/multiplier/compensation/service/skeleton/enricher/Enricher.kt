package com.multiplier.compensation.service.skeleton.enricher

import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.service.skeleton.structure.SkeletonStructureData

interface Enricher {
    fun enrich(
        skeletonType: SkeletonType,
        entityId: Long,
        customParams: Map<String, String>,
        skeletonData: SkeletonStructureData,
    ): List<String>?
}
