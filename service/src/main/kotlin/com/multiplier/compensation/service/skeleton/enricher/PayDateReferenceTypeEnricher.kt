package com.multiplier.compensation.service.skeleton.enricher

import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.service.skeleton.structure.SkeletonStructureData
import org.springframework.stereotype.Component

@Component
class PayDateReferenceTypeEnricher : Enricher {
    override fun enrich(
        skeletonType: SkeletonType,
        entityId: Long,
        customParams: Map<String, String>,
        skeletonData: SkeletonStructureData,
    ): List<String> = listOf(
        PayDateReference.PAY_SCHEDULE_START_DATE.name,
        PayDateReference.PAY_SCHEDULE_END_DATE.name,
        PayDateReference.COMPENSATION_START_DATE.name,
    )
}
