package com.multiplier.compensation.service.skeleton.enricher

import com.multiplier.compensation.domain.common.ReasonCode
import com.multiplier.compensation.domain.common.toCombinedString
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.service.skeleton.structure.SkeletonStructureData
import org.springframework.stereotype.Component

@Component
class ReasonCodeEnricher : Enricher {
    override fun enrich(
        skeletonType: SkeletonType,
        entityId: Long,
        customParams: Map<String, String>,
        skeletonData: SkeletonStructureData,
    ): List<String> = ReasonCode.entries.map { it.toCombinedString() }
}
