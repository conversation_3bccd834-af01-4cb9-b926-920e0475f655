package com.multiplier.compensation.service.skeleton.structure

import com.multiplier.compensation.domain.skeleton.ValidationRegex
import com.multiplier.compensation.domain.skeleton.enums.SkeletonKey
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.domain.skeleton.enums.ValueType
import com.multiplier.compensation.service.skeleton.enricher.enum.SkeletonEnricherType

data class SkeletonStructure(
    val skeletonType: List<SkeletonType>,
    val keys: List<SkeletonKey>,
    val structure: List<SkeletonStructureData>,
)

data class SkeletonStructureData(
    val fieldId: String,
    val fieldName: String,
    val valueType: ValueType,
    val description: String,
    val mandatory: Boolean,
    val possibleValues: List<ValueOption>?,
    val validationRegex: ValidationRegex?,
    val defaultValue: String?,
)

data class ValueOption(
    val op: SkeletonEnricherType? = null,
    val value: String? = null,
)
