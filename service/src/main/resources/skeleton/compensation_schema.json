{"skeletonType": ["COMPENSATION_SCHEMA"], "keys": ["COUNTRY_CODE", "ENTITY_ID", "COMPANY_ID"], "structure": [{"fieldId": "SCHEMA_NAME", "fieldName": "Schema Name", "valueType": "STRING", "validationRegex": {"regex": ".*", "errorMessage": "Should be text"}, "description": "Name of schema", "mandatory": true}, {"fieldId": "COMPONENT_NAME", "fieldName": "Component Name", "valueType": "STRING", "validationRegex": {"regex": ".*", "errorMessage": "Should be text"}, "description": "Name of component", "mandatory": true}, {"fieldId": "CATEGORY_KEY", "fieldName": "Category Key", "valueType": "SELECT", "description": "Category of component. Select from a predefined list of categories.", "possibleValues": [{"op": "ENTITY_CATEGORY_ENRICHER"}], "mandatory": true}, {"fieldId": "IS_TAXABLE", "fieldName": "Is Taxable?", "valueType": "SELECT", "description": "Component is taxable or not.", "possibleValues": [{"value": "Yes"}, {"value": "No"}], "mandatory": true}, {"fieldId": "IS_FIXED", "fieldName": "Is Fixed?", "valueType": "SELECT", "description": "Select Yes for fixed and No for variable.", "defaultValue": "Yes", "mandatory": true, "possibleValues": [{"value": "Yes"}, {"value": "No"}]}, {"fieldId": "IS_PRORATED", "fieldName": "Is Prorated?", "valueType": "SELECT", "description": "Indicates if the compensation component is prorated based on working days/hours.", "possibleValues": [{"value": "Yes"}, {"value": "No"}], "defaultValue": "No", "mandatory": true}, {"fieldId": "IS_MANDATORY", "fieldName": "Is Mandatory?", "valueType": "SELECT", "description": "Mandatory components from compliance standpoint.", "possibleValues": [{"value": "Yes"}, {"value": "No"}], "defaultValue": "Yes", "mandatory": true}, {"fieldId": "IS_PART_OF_BASE_PAY", "fieldName": "Is Part of BasePay?", "valueType": "SELECT", "description": "Indicates if the compensation component is part of base pay or not.", "possibleValues": [{"value": "Yes"}, {"value": "No"}], "defaultValue": "No", "mandatory": true}, {"fieldId": "TAGS", "fieldName": "Tags", "valueType": "SELECT", "description": "Indicates compensation schema tags to enable filtering on schemas.", "possibleValues": [{"op": "SCHEMA_TAGS_ENRICHER"}], "mandatory": true}, {"fieldId": "LABEL", "fieldName": "Label", "valueType": "STRING", "validationRegex": {"regex": ".*", "errorMessage": "Should be text"}, "description": "Label for the component.", "mandatory": true}, {"fieldId": "ITEM_TYPE", "fieldName": "Item Type", "valueType": "SELECT", "description": "Type of the item.", "possibleValues": [{"op": "ITEM_TYPE_ENRICHER"}], "defaultValue": "INPUT", "mandatory": true}, {"fieldId": "VALIDATION", "fieldName": "Validation", "valueType": "STRING", "description": "Input validation for the component.", "mandatory": false}, {"fieldId": "CALCULATION", "fieldName": "Calculation", "valueType": "STRING", "description": "Calculation for the component.", "mandatory": false}, {"fieldId": "BILLING_RATE_TYPE", "fieldName": "Billing Rate Type", "valueType": "SELECT", "description": "Billing rate type.", "possibleValues": [{"op": "BILLING_RATE_TYPE_ENRICHER"}], "mandatory": false}, {"fieldId": "IS_OVERTIME_ELIGIBLE", "fieldName": "Is Overtime Eligible?", "valueType": "SELECT", "description": "Indicates if the compensation component is overtime eligible or not.", "possibleValues": [{"value": "Yes"}, {"value": "No"}], "defaultValue": "No", "mandatory": true}, {"fieldId": "COMPONENT_DESCRIPTION", "fieldName": "Component Description", "valueType": "STRING", "validationRegex": {"regex": ".*", "errorMessage": "Should be text"}, "description": "Description for the component.", "mandatory": false}, {"fieldId": "BILLING_FREQUENCY", "fieldName": "Billing Frequency", "valueType": "SELECT", "description": "Billing Frequency for compensation component.", "possibleValues": [{"op": "BILLING_FREQUENCY_ENRICHER"}], "mandatory": false}, {"fieldId": "PAY_SCHEDULE_NAME", "fieldName": "Pay Schedule Name", "valueType": "SELECT", "description": "Pay schedule name for the component.", "possibleValues": [{"op": "ENTITY_PAY_SCHEDULE_ENRICHER"}], "mandatory": false}, {"fieldId": "CURRENCY", "fieldName": "<PERSON><PERSON><PERSON><PERSON>", "valueType": "SELECT", "description": "Currency for the component.", "possibleValues": [{"op": "ENTITY_CURRENCY_ENRICHER"}], "mandatory": false}, {"fieldId": "SCHEMA_DESCRIPTION", "fieldName": "Schema Description", "valueType": "STRING", "description": "Description for the schema.", "mandatory": false}, {"fieldId": "IS_PART_OF_CTC", "fieldName": "Is Part of CTC?", "valueType": "SELECT", "description": "Indicates if the compensation component is part of CTC or not.", "possibleValues": [{"value": "Yes"}, {"value": "No"}], "mandatory": true}]}