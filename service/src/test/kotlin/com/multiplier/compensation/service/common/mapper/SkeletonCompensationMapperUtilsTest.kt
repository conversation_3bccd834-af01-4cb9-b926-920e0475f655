package com.multiplier.compensation.service.common.mapper

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationschema.CompensationSchema
import com.multiplier.compensation.domain.compensationschema.SchemaTags
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.mapper.SkeletonCompensationMapperUtils.toCompensationDraft
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.rowItemFixture
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test

class SkeletonCompensationMapperUtilsTest {
    private lateinit var context: CompensationValidatorContext

    @Test
    fun `toCompensationDraft with all fields`() {
        val validationInputItem = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = rowItemFixture().keyValuePairs,
        )
        val schemaItemId = UUID.randomUUID()
        val payScheduleId = UUID.randomUUID()

        context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = mapOf(
                mockk<CompensationSchema> {
                    every { id } returns UUID.randomUUID()
                    every { name } returns "Default Schema"
                    every { isDefault } returns true
                    every { tags } returns listOf(SchemaTags.GLOBAL_PAYROLL.toString())
                    every { schemaItems } returns listOf(
                        mockk {
                            every { id } returns schemaItemId
                            every { componentName } returns "COMPONENT_1"
                            every { category } returns "CATEGORY"
                        },
                    )
                } to mapOf(
                    "COMPONENT_1" to mockk {
                        every { id } returns schemaItemId
                        every { componentName } returns "COMPONENT_1"
                        every { category } returns "CATEGORY"
                    },
                ),
            ),
            paySchedules = mapOf(
                "SCHEDULE_1" to mockk<PaySchedule> {
                    every { id } returns payScheduleId
                    every { isInstallment } returns true
                    every { frequency } returns PayScheduleFrequency.MONTHLY
                },
            ),
            contracts = mapOf(
                50000L to Contract(
                    id = 50000L,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ONBOARDING,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "CA",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        val compensationDraft = validationInputItem.toCompensationDraft(context)

        assertEquals(123L, compensationDraft.contractId)
        assertEquals(schemaItemId, compensationDraft.schemaItemId)
        assertEquals("CATEGORY", compensationDraft.schemaCategory)
        assertEquals("USD", compensationDraft.currency)
        assertEquals(BillingRateType.VALUE, compensationDraft.billingRateType)
        assertEquals(100.0, compensationDraft.billingRate)
        assertEquals(BillingFrequency.MONTHLY, compensationDraft.billingFrequency)
        assertEquals(payScheduleId, compensationDraft.payScheduleId)
        assertEquals(LocalDate.parse("2023-01-01"), compensationDraft.startDate)
        assertEquals(LocalDate.parse("2023-12-31"), compensationDraft.endDate)
        assertEquals(true, compensationDraft.isInstallment)
        assertEquals(12, compensationDraft.noOfInstallments)
        assertEquals("Test note", compensationDraft.notes)
    }

    @Test
    fun `toCompensationDraft with missing endDate for installment fields`() {
        val validationInputItem = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = rowItemFixture().keyValuePairs,
        )
        val schemaItemId = UUID.randomUUID()
        val payScheduleId = UUID.randomUUID()
        context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = mapOf(
                mockk<CompensationSchema> {
                    every { id } returns UUID.randomUUID()
                    every { name } returns "Default Schema"
                    every { tags } returns listOf(SchemaTags.GLOBAL_PAYROLL.toString())
                    every { isDefault } returns true
                    every { schemaItems } returns listOf(
                        mockk {
                            every { id } returns schemaItemId
                            every { componentName } returns "COMPONENT_1"
                            every { category } returns "CATEGORY"
                        },
                    )
                } to mapOf(
                    "COMPONENT_1" to mockk {
                        every { id } returns schemaItemId
                        every { componentName } returns "COMPONENT_1"
                        every { category } returns "CATEGORY"
                    },
                ),
            ),
            paySchedules = mapOf(
                "SCHEDULE_1" to mockk<PaySchedule> {
                    every { id } returns payScheduleId
                    every { isInstallment } returns true
                    every { frequency } returns PayScheduleFrequency.MONTHLY
                },
            ),
            contracts = mapOf(
                50000L to Contract(
                    id = 50000L,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ONBOARDING,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "CA",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        val fieldKeyValuePairs = rowItemFixture().keyValuePairs.toMutableSet().apply {
            remove(KeyValuePair(CompensationSkeletonField.END_DATE.id, "2023-12-31"))
            add(KeyValuePair(CompensationSkeletonField.END_DATE.id, null))
            remove(KeyValuePair(CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id, "12"))
            add(KeyValuePair(CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id, "5"))
        }.toMutableList()

        val validationInputItemWithInstallments = validationInputItem.copy(fieldKeyValuePairs = fieldKeyValuePairs)

        val compensationDraft = validationInputItemWithInstallments.toCompensationDraft(context)

        assertEquals(123L, compensationDraft.contractId)
        assertEquals(schemaItemId, compensationDraft.schemaItemId)
        assertEquals("CATEGORY", compensationDraft.schemaCategory)
        assertEquals("USD", compensationDraft.currency)
        assertEquals(BillingRateType.VALUE, compensationDraft.billingRateType)
        assertEquals(100.0, compensationDraft.billingRate)
        assertEquals(BillingFrequency.MONTHLY, compensationDraft.billingFrequency)
        assertEquals(payScheduleId, compensationDraft.payScheduleId)
        assertEquals(LocalDate.parse("2023-01-01"), compensationDraft.startDate)
        assertEquals(null, compensationDraft.endDate)
        assertEquals(true, compensationDraft.isInstallment)
        assertEquals(5, compensationDraft.noOfInstallments)
    }

    @Test
    fun `toCompensationDraft with missing endDate for non installment fields`() {
        val validationInputItem = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = rowItemFixture().keyValuePairs,
        )
        val schemaItemId = UUID.randomUUID()
        val payScheduleId = UUID.randomUUID()

        context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = mapOf(
                mockk<CompensationSchema> {
                    every { id } returns UUID.randomUUID()
                    every { name } returns "Default Schema"
                    every { tags } returns listOf(SchemaTags.GLOBAL_PAYROLL.toString())
                    every { isDefault } returns true
                    every { schemaItems } returns listOf(
                        mockk {
                            every { id } returns schemaItemId
                            every { componentName } returns "COMPONENT_1"
                            every { category } returns "CATEGORY"
                        },
                    )
                } to mapOf(
                    "COMPONENT_1" to mockk {
                        every { id } returns schemaItemId
                        every { componentName } returns "COMPONENT_1"
                        every { category } returns "CATEGORY"
                    },
                ),
            ),
            paySchedules = mapOf(
                "SCHEDULE_1" to mockk<PaySchedule> {
                    every { id } returns payScheduleId
                    every { isInstallment } returns true
                    every { frequency } returns PayScheduleFrequency.MONTHLY
                },
            ),
            contracts = mapOf(
                50000L to Contract(
                    id = 50000L,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ONBOARDING,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "CA",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        val fieldKeyValuePairs = rowItemFixture().keyValuePairs.toMutableSet().apply {
            remove(KeyValuePair(CompensationSkeletonField.IS_INSTALLMENT.id, "Yes"))
            remove(KeyValuePair(CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id, "12"))
            remove(KeyValuePair(CompensationSkeletonField.END_DATE.id, "2023-12-31"))
            add(KeyValuePair(CompensationSkeletonField.IS_INSTALLMENT.id, "No"))
        }.toMutableList()

        val validationInputItemWithInstallments = validationInputItem.copy(fieldKeyValuePairs = fieldKeyValuePairs)

        val compensationDraft = validationInputItemWithInstallments.toCompensationDraft(context)

        assertEquals(123L, compensationDraft.contractId)
        assertEquals(schemaItemId, compensationDraft.schemaItemId)
        assertEquals("CATEGORY", compensationDraft.schemaCategory)
        assertEquals("USD", compensationDraft.currency)
        assertEquals(BillingRateType.VALUE, compensationDraft.billingRateType)
        assertEquals(100.0, compensationDraft.billingRate)
        assertEquals(BillingFrequency.MONTHLY, compensationDraft.billingFrequency)
        assertEquals(payScheduleId, compensationDraft.payScheduleId)
        assertEquals(LocalDate.parse("2023-01-01"), compensationDraft.startDate)
        assertEquals(null, compensationDraft.endDate)
        assertEquals(false, compensationDraft.isInstallment)
        assertEquals(null, compensationDraft.noOfInstallments)
    }
}
