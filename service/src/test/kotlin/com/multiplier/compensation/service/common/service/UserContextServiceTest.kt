package com.multiplier.compensation.service.common.service

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.common.transport.user.UserContext
import com.multiplier.common.transport.user.UserScopes
import com.multiplier.compensation.grpc.client.adapter.CompanyServiceAdapter
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertTrue

class UserContextServiceTest {
    private lateinit var currentUser: CurrentUser
    private lateinit var companyServiceAdapter: CompanyServiceAdapter
    private lateinit var userContextService: UserContextService
    private lateinit var userContext: UserContext
    private lateinit var userScopes: UserScopes

    @BeforeEach
    fun setUp() {
        currentUser = mockk()
        companyServiceAdapter = mockk()
        userContext = mockk()
        userScopes = mockk()

        userContextService = UserContextService(currentUser, companyServiceAdapter)
    }

    @Test
    fun `should return true when user has operations in experience`() {
        every { currentUser.context?.experience } returns "operations"
        every { currentUser.context?.experiences } returns emptySet()

        assertTrue(userContextService.isOperationsExperience())
    }

    @Test
    fun `should return true when user has operations in experiences`() {
        every { currentUser.context?.experience } returns ""
        every { currentUser.context?.experiences } returns setOf("operations")

        assertTrue(userContextService.isOperationsExperience())
    }

    @Test
    fun `should return false when user does not have operations experience`() {
        every { currentUser.context?.experience } returns ""
        every { currentUser.context?.experiences } returns emptySet()

        assertFalse(userContextService.isOperationsExperience())
    }

    @Test
    fun `isCompanyExperience should return true when user has company experience`() {
        every { currentUser.context } returns userContext
        every { userContext.experience } returns "company"

        assertTrue(userContextService.isCompanyExperience())
    }

    @Test
    fun `isCompanyExperience should return false when user does not have company experience`() {
        every { currentUser.context } returns userContext
        every { userContext.experience } returns "operations"

        assertFalse(userContextService.isCompanyExperience())
    }

    @Test
    fun `isCompanyExperience should return false when user context is null`() {
        every { currentUser.context } returns null

        assertFalse(userContextService.isCompanyExperience())
    }

    @Test
    fun `user property should return user context`() {
        every { currentUser.context } returns userContext

        assertEquals(userContext, userContextService.user)
    }

    @Test
    fun `userId property should return user id`() {
        every { currentUser.context } returns userContext
        every { userContext.id } returns 123L

        assertEquals(123L, userContextService.userId)
    }

    @Test
    fun `userId property should return null when user context is null`() {
        every { currentUser.context } returns null

        assertNull(userContextService.userId)
    }

    @Test
    fun `scopes property should return user scopes`() {
        every { currentUser.context } returns userContext
        every { userContext.scopes } returns userScopes

        assertEquals(userScopes, userContextService.scopes)
    }

    @Test
    fun `scopes property should return null when user context is null`() {
        every { currentUser.context } returns null

        assertNull(userContextService.scopes)
    }

    @Test
    fun `should return true if company experience and test company`() {
        val scopes = mockk<UserScopes>()
        val userContext = mockk<UserContext>()

        every { currentUser.context } returns userContext
        every { userContext.experience } returns "company"
        every { userContext.scopes } returns scopes
        every { userContext.id } returns 123L
        every { scopes.companyId } returns 456L
        every { scopes.isOperationsTestUser } returns true

        coEvery { companyServiceAdapter.isTestCompany(456L) } returns true

        val result = runBlocking { userContextService.isCurrentUserTest() }

        assertTrue(result)
    }

    @Test
    fun `should return false if company experience and not a test company`() {
        val scopes = mockk<UserScopes>()
        val userContext = mockk<UserContext>()

        every { currentUser.context } returns userContext
        every { userContext.experience } returns "company"
        every { userContext.scopes } returns scopes
        every { userContext.id } returns 123L
        every { scopes.companyId } returns 789L
        every { scopes.isOperationsTestUser } returns false

        coEvery { companyServiceAdapter.isTestCompany(789L) } returns false

        val result = runBlocking { userContextService.isCurrentUserTest() }

        assertFalse(result)
    }

    @Test
    fun `should return true if company experience but no companyId`() {
        val userContext = mockk<UserContext>()
        val scopes = mockk<UserScopes>()

        every { currentUser.context } returns userContext
        every { userContext.experience } returns "company"
        every { userContext.scopes } returns scopes
        every { userContext.id } returns 111L
        every { scopes.companyId } returns null
        every { scopes.isOperationsTestUser } returns true

        val result = runBlocking { userContextService.isCurrentUserTest() }

        assertTrue(result)
    }

    @Test
    fun `should return true if not company experience and is operations test user`() {
        val userContext = mockk<UserContext>()
        val scopes = mockk<UserScopes>()

        every { currentUser.context } returns userContext
        every { userContext.experience } returns "operations"
        every { userContext.scopes } returns scopes
        every { scopes.isOperationsTestUser } returns true

        val result = runBlocking { userContextService.isCurrentUserTest() }

        assertTrue(result)
    }

    @Test
    fun `should return false if not company experience and isOperationsTestUser is false`() {
        val userContext = mockk<UserContext>()
        val scopes = mockk<UserScopes>()

        every { currentUser.context } returns userContext
        every { userContext.experience } returns "operations"
        every { userContext.scopes } returns scopes
        every { scopes.isOperationsTestUser } returns false

        val result = runBlocking { userContextService.isCurrentUserTest() }

        assertFalse(result)
    }

    @Test
    fun `should return false if not company experience and isOperationsTestUser is null`() {
        val userContext = mockk<UserContext>()
        val scopes = mockk<UserScopes>()

        every { currentUser.context } returns userContext
        every { userContext.experience } returns "operations"
        every { userContext.scopes } returns scopes
        every { scopes.isOperationsTestUser } returns null

        val result = runBlocking { userContextService.isCurrentUserTest() }

        assertFalse(result)
    }
}
