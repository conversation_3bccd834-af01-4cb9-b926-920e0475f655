import com.multiplier.compensation.database.repository.compensation.CompensationRepository
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.PageRequest
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.CompensationEnriched
import com.multiplier.compensation.domain.compensation.CompensationRecordType
import com.multiplier.compensation.domain.compensation.CompensationRecordsFilter
import com.multiplier.compensation.domain.compensation.CompensationRecordsGrpcFilter
import com.multiplier.compensation.domain.compensation.enums.CompensationState
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationinput.CompensationInputEnriched
import com.multiplier.compensation.domain.compensationinput.CompensationInputFilter
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.common.mapper.mapToCompensationEnriched
import com.multiplier.compensation.service.compensation.CompensationService
import com.multiplier.compensation.service.compensationinput.CompensationInputService
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationBuilder
import com.multiplier.compensation.service.compensationschema.mapper.CompensationSchemaSkeletonField
import com.multiplier.graph.types.LatestEndedCompensationRecordsFilter
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@ExtendWith(MockKExtension::class)
class CompensationServiceTest {
    private lateinit var compensationRepository: CompensationRepository
    private lateinit var compensationInputService: CompensationInputService
    private lateinit var compensationService: CompensationService

    @BeforeEach
    fun setup() {
        compensationRepository = mockk<CompensationRepository>()
        compensationInputService = mockk<CompensationInputService>()
        compensationService = CompensationService(compensationRepository, compensationInputService)
    }

    @Nested
    inner class GetCompensationWithPagination {
        private val pageRequest = PageRequest(pageSize = 10, pageNumber = 0)

        @Test
        fun `should return only applied records when filter type is APPLIED`() {
            val filter = CompensationRecordsFilter(
                entityId = 1L,
                companyId = 1000L,
                state = CompensationState.ACTIVE,
                activeAsOn = LocalDate.now(),
                categories = listOf("category1", "category2"),
                recordTypes = listOf(CompensationRecordType.APPLIED),
            )
            val expectedRecords = listOf(mockk<CompensationEnriched>())
            every {
                compensationRepository.findCompensationRecordsWithPagination(filter, pageRequest)
            } returns Pair(expectedRecords, 1L)

            val (records, count) = compensationService.getCompensationWithPagination(filter, pageRequest)

            assert(records == expectedRecords)
            assert(count == 1L)
            verify(exactly = 1) {
                compensationRepository.findCompensationRecordsWithPagination(filter, pageRequest)
            }
        }

        @Test
        fun `should return only draft records when filter type is DRAFT`() {
            val filter = CompensationRecordsFilter(
                entityId = 1L,
                companyId = 1000L,
                state = CompensationState.ACTIVE,
                activeAsOn = LocalDate.now(),
                categories = listOf("category1", "category2"),
                recordTypes = listOf(CompensationRecordType.DRAFT),
            )
            val inputFilter = slot<CompensationInputFilter>()
            val enrichedResponse = Pair(listOf(getCompensationInputEnriched()), 1L)

            every {
                compensationInputService.getCompensationInputEnrichedDataWithPagination(
                    capture(inputFilter),
                    pageRequest,
                )
            } returns enrichedResponse

            val (records, count) = compensationService.getCompensationWithPagination(filter, pageRequest)

            assertEquals(1, records.size)
            assertEquals(1L, count)
            assertEquals(filter.entityId, inputFilter.captured.entityId)
            verify(exactly = 1) {
                compensationInputService.getCompensationInputEnrichedDataWithPagination(any(), pageRequest)
            }
        }

        @Test
        fun `should combine applied and draft records when both types are requested`() {
            val filter = CompensationRecordsFilter(
                entityId = 1L,
                companyId = 1000L,
                state = CompensationState.ACTIVE,
                activeAsOn = LocalDate.now(),
                categories = listOf("category1", "category2"),
                recordTypes = listOf(CompensationRecordType.DRAFT, CompensationRecordType.APPLIED),
            )
            val inputFilter = slot<CompensationInputFilter>()
            val appliedRecord = mockk<CompensationEnriched>()
            val enrichedResponse = Pair(listOf(getCompensationInputEnriched()), 1L)

            every {
                compensationRepository.findCompensationRecordsWithPagination(filter, pageRequest)
            } returns Pair(listOf(appliedRecord), 1L)

            every {
                compensationInputService.getCompensationInputEnrichedDataWithPagination(
                    capture(inputFilter),
                    pageRequest,
                )
            } returns enrichedResponse

            val (records, count) = compensationService.getCompensationWithPagination(filter, pageRequest)
            assert(records.size == 2)
            assert(count == 2L)
            assert(records.contains(appliedRecord))
            assert(records.contains(enrichedResponse.first.first().mapToCompensationEnriched()))
        }

        @Test
        fun `should return only applied records when filter type is null`() {
            val filter = CompensationRecordsFilter(
                entityId = 1L,
                companyId = 1000L,
                state = CompensationState.ACTIVE,
                activeAsOn = LocalDate.now(),
                categories = listOf("category1", "category2"),
                recordTypes = null,
            )
            val expectedRecords = listOf(mockk<CompensationEnriched>())
            every {
                compensationRepository.findCompensationRecordsWithPagination(filter, pageRequest)
            } returns Pair(expectedRecords, 1L)

            val (records, count) = compensationService.getCompensationWithPagination(filter, pageRequest)

            assert(records == expectedRecords)
            assert(count == 1L)
            verify(exactly = 1) {
                compensationRepository.findCompensationRecordsWithPagination(filter, pageRequest)
            }
        }
    }

    @Nested
    inner class GetCompensationsWithFilters {
        @Test
        fun `should return only applied records when filter type is APPLIED`() {
            val filter = CompensationRecordsFilter(
                entityId = 1L,
                companyId = 1000L,
                state = CompensationState.ACTIVE,
                activeAsOn = LocalDate.now(),
                categories = listOf("category1", "category2"),
                recordTypes = listOf(CompensationRecordType.APPLIED),
            )
            val expectedRecords = listOf(mockk<CompensationEnriched>())
            every {
                compensationRepository.findCompensationRecordsWithFilters(filter)
            } returns expectedRecords

            val records = compensationService.getCompensationsWithFilters(filter)

            assertEquals(expectedRecords, records)
            verify(exactly = 1) {
                compensationRepository.findCompensationRecordsWithFilters(filter)
            }
        }

        @Test
        fun `should return only draft records when filter type is DRAFT`() {
            val filter = CompensationRecordsFilter(
                entityId = 1L,
                companyId = 1000L,
                state = CompensationState.ACTIVE,
                activeAsOn = LocalDate.now(),
                categories = listOf("category1", "category2"),
                recordTypes = listOf(CompensationRecordType.DRAFT),
            )
            val inputFilter = slot<CompensationInputFilter>()
            val draftRecord = getCompensationInputEnriched()

            every {
                compensationInputService.getCompensationInputEnrichedData(capture(inputFilter))
            } returns listOf(draftRecord)
            val records = compensationService.getCompensationsWithFilters(filter)

            assertEquals(1, records.size)
            assertEquals(filter.entityId, inputFilter.captured.entityId)
            verify(exactly = 1) {
                compensationInputService.getCompensationInputEnrichedData(any())
            }
        }

        @Test
        fun `should combine applied and draft records when both types are requested`() {
            val filter = CompensationRecordsFilter(
                entityId = 1L,
                companyId = 1000L,
                state = CompensationState.ACTIVE,
                activeAsOn = LocalDate.now(),
                categories = listOf("category1", "category2"),
                recordTypes = listOf(CompensationRecordType.DRAFT, CompensationRecordType.APPLIED),
            )
            val inputFilter = slot<CompensationInputFilter>()
            val appliedRecord = mockk<CompensationEnriched>()
            val draftRecord = getCompensationInputEnriched()

            every {
                compensationRepository.findCompensationRecordsWithFilters(filter)
            } returns listOf(appliedRecord)

            every {
                compensationInputService.getCompensationInputEnrichedData(capture(inputFilter))
            } returns listOf(draftRecord)

            val records = compensationService.getCompensationsWithFilters(filter)

            assertEquals(2, records.size)
            assertTrue(records.contains(appliedRecord))
            assertTrue(records.contains(draftRecord.mapToCompensationEnriched()))
            verify(exactly = 1) {
                compensationRepository.findCompensationRecordsWithFilters(filter)
                compensationInputService.getCompensationInputEnrichedData(any())
            }
        }

        @Test
        fun `should return only applied applied and when record types is null`() {
            val filter = CompensationRecordsFilter(
                entityId = 1L,
                companyId = 1000L,
                state = CompensationState.ACTIVE,
                activeAsOn = LocalDate.now(),
                categories = listOf("category1", "category2"),
                recordTypes = null,
            )
            val appliedRecord = mockk<CompensationEnriched>()

            every {
                compensationRepository.findCompensationRecordsWithFilters(filter)
            } returns listOf(appliedRecord)

            val records = compensationService.getCompensationsWithFilters(filter)

            assertEquals(1, records.size)
            assertTrue(records.contains(appliedRecord))
            verify(exactly = 1) {
                compensationRepository.findCompensationRecordsWithFilters(filter)
            }
        }
    }

    @Nested
    inner class GetLatestEndedCompensationRecords {
        @Test
        fun `should return latest ended compensation records`() {
            val contractIds = listOf(1L)
            val categories = listOf(CompensationSchemaSkeletonField.CATEGORY.name)
            val expectedRecords = listOf(mockk<CompensationEnriched>())
            every {
                compensationRepository.getLatestEndedCompensationRecords(
                    CompensationRecordsFilter(contractIds = contractIds, categories = categories),
                )
            } returns expectedRecords

            val records = compensationService.getLatestEndedCompensationRecords(
                LatestEndedCompensationRecordsFilter(contractIds, categories),
            )

            assertEquals(expectedRecords, records)
            verify(exactly = 1) {
                compensationRepository.getLatestEndedCompensationRecords(
                    CompensationRecordsFilter(contractIds = contractIds, categories = categories),
                )
            }
        }

        @Test
        fun `should throw exception when categories are not provided`() {
            val contractIds = listOf(1L)
            val categories = emptyList<String>()

            val exception = assertThrows<IllegalArgumentException> {
                compensationService.getLatestEndedCompensationRecords(
                    LatestEndedCompensationRecordsFilter(contractIds, categories),
                )
            }

            assertEquals("Categories should be provided", exception.message)
        }
    }

    @Nested
    inner class GetCompensationByIds {
        @Test
        fun `getCompensationsByIds should return records when found`() {
            val compensationId1 = UUID.randomUUID()
            val compensationId2 = UUID.randomUUID()
            val expectedComp1 = TestCompensationBuilder().build()
            val expectedComp2 = TestCompensationBuilder().build()
            val compensationIds = listOf(compensationId1, compensationId2)

            coEvery { compensationRepository.findForIds(compensationIds, any()) } returns
                listOf(expectedComp1, expectedComp2)

            val result = compensationService.getCompensationsByIds(compensationIds)

            assertEquals(listOf(expectedComp1, expectedComp2), result)
            coVerify { compensationRepository.findForIds(compensationIds, any()) }
        }

        @Test
        fun `getCompensationsByIds should return empty list when no records are found`() {
            val compensationIds = listOf(UUID.randomUUID(), UUID.randomUUID())

            coEvery { compensationRepository.findForIds(compensationIds, any()) } returns emptyList()

            val result = compensationService.getCompensationsByIds(compensationIds)

            assertEquals(emptyList<Compensation>(), result)
            coVerify { compensationRepository.findForIds(compensationIds, any()) }
        }
    }

    private fun getCompensationInputEnriched(): CompensationInputEnriched = CompensationInputEnriched(
        id = UUID.randomUUID(),
        companyId = 1L,
        entityId = 1L,
        contractId = 1L,
        schemaItemId = UUID.randomUUID(),
        category = "category",
        currency = "USD",
        billingRateType = BillingRateType.VALUE,
        billingRate = 50.0,
        billingFrequency = BillingFrequency.MONTHLY,
        startDate = LocalDate.of(2021, 1, 1),
        endDate = LocalDate.of(2021, 1, 31),
        isInstallment = false,
        noOfInstallments = null,
        requestType = RequestType.COMPENSATION_SETUP,
        requestId = "REQ001",
        reasonCode = null,
        status = CompensationInputStatus.ONBOARDING_DRAFT,
        createdOn = LocalDateTime.now(),
        createdBy = 1L,
        updatedOn = LocalDateTime.now(),
        updatedBy = 1L,
        payScheduleName = "Monthly Pay",
        payScheduleFrequency = PayScheduleFrequency.MONTHLY,
        schemaComponentName = "Base Salary",
        compensationName = "Base Salary",
        compensationCategory = "BASE",
        isTaxable = true,
        isFixed = true,
        isProrated = false,
        isMandatory = true,
        isPartOfBasePay = true,
        label = "Base Salary",
        isOvertimeEligible = true,
        isPartOfCtc = true,
    )

    @Nested
    inner class GetEnrichedCompensationRecordsWithFilters {
        @Test
        fun `should return only APPLIED records when recordType is APPLIED`() {
            // Given
            val filter = CompensationRecordsGrpcFilter(
                entityId = 1L,
                recordType = CompensationRecordType.APPLIED,
                categories = listOf("CONTRACT_BASE_PAY", "CONTRACT_ALLOWANCE"),
            )
            val appliedRecord = mockk<CompensationEnriched>()

            every {
                compensationRepository.getEnrichedCompensationRecordsWithFilters(filter)
            } returns listOf(appliedRecord)

            // When
            val result = compensationService.getEnrichedCompensationRecordsWithFilters(filter)

            // Then
            assertEquals(1, result.size)
            assertEquals(appliedRecord, result[0])
            verify(exactly = 1) {
                compensationRepository.getEnrichedCompensationRecordsWithFilters(filter)
            }
        }

        @Test
        fun `should return only DRAFT records when recordType is DRAFT`() {
            // Given
            val filter = CompensationRecordsGrpcFilter(
                entityId = 1L,
                recordType = CompensationRecordType.DRAFT,
            )
            val draftRecord = getCompensationInputEnriched()
            val inputFilter = slot<CompensationInputFilter>()

            every {
                compensationInputService.getCompensationInputEnrichedData(capture(inputFilter))
            } returns listOf(draftRecord)

            // When
            val result = compensationService.getEnrichedCompensationRecordsWithFilters(filter)

            // Then
            assertEquals(1, result.size)
            assertEquals(draftRecord.mapToCompensationEnriched(), result[0])
            verify(exactly = 1) {
                compensationInputService.getCompensationInputEnrichedData(any())
            }
        }

        @Test
        fun `should return only APPLIED records when recordTypes contains only APPLIED`() {
            // Given
            val filter = CompensationRecordsGrpcFilter(
                entityId = 1L,
                recordTypes = listOf(CompensationRecordType.APPLIED),
            )
            val appliedRecord = mockk<CompensationEnriched>()

            every {
                compensationRepository.getEnrichedCompensationRecordsWithFilters(filter)
            } returns listOf(appliedRecord)

            // When
            val result = compensationService.getEnrichedCompensationRecordsWithFilters(filter)

            // Then
            assertEquals(1, result.size)
            assertEquals(appliedRecord, result[0])
            verify(exactly = 1) {
                compensationRepository.getEnrichedCompensationRecordsWithFilters(filter)
            }
        }

        @Test
        fun `should return only DRAFT records when recordTypes contains only DRAFT`() {
            // Given
            val filter = CompensationRecordsGrpcFilter(
                entityId = 1L,
                recordTypes = listOf(CompensationRecordType.DRAFT),
            )
            val draftRecord = getCompensationInputEnriched()
            val inputFilter = slot<CompensationInputFilter>()

            every {
                compensationInputService.getCompensationInputEnrichedData(capture(inputFilter))
            } returns listOf(draftRecord)

            // When
            val result = compensationService.getEnrichedCompensationRecordsWithFilters(filter)

            // Then
            assertEquals(1, result.size)
            assertEquals(draftRecord.mapToCompensationEnriched(), result[0])
            verify(exactly = 1) {
                compensationInputService.getCompensationInputEnrichedData(any())
            }
        }

        @Test
        fun `should return both DRAFT and APPLIED records when recordTypes contains both`() {
            // Given
            val filter = CompensationRecordsGrpcFilter(
                entityId = 1L,
                recordTypes = listOf(CompensationRecordType.DRAFT, CompensationRecordType.APPLIED),
            )
            val draftRecord = getCompensationInputEnriched()
            val appliedRecord = mockk<CompensationEnriched>()
            val inputFilter = slot<CompensationInputFilter>()

            every {
                compensationInputService.getCompensationInputEnrichedData(capture(inputFilter))
            } returns listOf(draftRecord)

            every {
                compensationRepository.getEnrichedCompensationRecordsWithFilters(filter)
            } returns listOf(appliedRecord)

            // When
            val result = compensationService.getEnrichedCompensationRecordsWithFilters(filter)

            // Then
            assertEquals(2, result.size)
            assertEquals(draftRecord.mapToCompensationEnriched(), result[0])
            assertEquals(appliedRecord, result[1])
            verify(exactly = 1) {
                compensationInputService.getCompensationInputEnrichedData(any())
            }
            verify(exactly = 1) {
                compensationRepository.getEnrichedCompensationRecordsWithFilters(filter)
            }
        }

        @Test
        fun `should prioritize recordTypes over recordType when both are present`() {
            // Given
            val filter = CompensationRecordsGrpcFilter(
                entityId = 1L,
                recordType = CompensationRecordType.APPLIED,
                recordTypes = listOf(CompensationRecordType.DRAFT),
            )
            val draftRecord = getCompensationInputEnriched()
            val inputFilter = slot<CompensationInputFilter>()

            every {
                compensationInputService.getCompensationInputEnrichedData(capture(inputFilter))
            } returns listOf(draftRecord)

            // When
            val result = compensationService.getEnrichedCompensationRecordsWithFilters(filter)

            // Then
            assertEquals(1, result.size)
            assertEquals(draftRecord.mapToCompensationEnriched(), result[0])
            verify(exactly = 1) {
                compensationInputService.getCompensationInputEnrichedData(any())
            }
            verify(exactly = 0) {
                compensationRepository.getEnrichedCompensationRecordsWithFilters(any())
            }
        }

        @Test
        fun `should return APPLIED records by default when no record type is specified`() {
            // Given
            val filter = CompensationRecordsGrpcFilter(
                entityId = 1L,
            )
            val appliedRecord = mockk<CompensationEnriched>()

            every {
                compensationRepository.getEnrichedCompensationRecordsWithFilters(filter)
            } returns listOf(appliedRecord)

            // When
            val result = compensationService.getEnrichedCompensationRecordsWithFilters(filter)

            // Then
            assertEquals(1, result.size)
            assertEquals(appliedRecord, result[0])
            verify(exactly = 1) {
                compensationRepository.getEnrichedCompensationRecordsWithFilters(filter)
            }
        }
    }
}
