package com.multiplier.compensation.service.compensation.extrapolation

import assertk.assertThat
import assertk.assertions.hasSize
import assertk.assertions.isEqualTo
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.util.UUID

class CompensationFrequencyConverterTest {
    private lateinit var compensationFrequencyConverter: CompensationFrequencyConverter
    private lateinit var context: CompensationValidatorContext

    private val schemaItemId1 = UUID.randomUUID()
    private val schemaItemId2 = UUID.randomUUID()
    private val schemaItemId3 = UUID.randomUUID()

    @BeforeEach
    fun setUp() {
        compensationFrequencyConverter = CompensationFrequencyConverter()
        context = mockk(relaxed = true)
    }

    @Nested
    inner class AdjustBillingFrequencyOnDerivedDrafts {
        @Test
        fun `should return empty list when no derived drafts provided`() {
            // Given
            val derivedDrafts = emptyList<CompensationDraft>()
            val inputDrafts = emptyList<CompensationDraft>()

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnDerivedDrafts(
                derivedDrafts,
                context,
                inputDrafts,
            )

            // Then
            assertThat(result).hasSize(0)
        }

        @Test
        fun `should return original drafts when no target categories in schema`() {
            // Given
            val derivedDrafts = listOf(
                createCompensationDraft(
                    billingRate = 1000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = "OTHER_CATEGORY",
                ),
            )
            val inputDrafts = emptyList<CompensationDraft>()
            val schemaItems = mapOf(
                "Other Component" to createSchemaItem(
                    schemaItemId1,
                    "Other Component",
                    "OTHER_CATEGORY",
                    BillingFrequency.MONTHLY,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnDerivedDrafts(
                derivedDrafts,
                context,
                inputDrafts,
            )

            // Then - no TOTAL_COST_TO_COMPANY category, so no conversion
            assertThat(result).hasSize(1)
            assertThat(result[0]).isEqualTo(derivedDrafts[0])
        }

        @Test
        fun `should convert derived drafts from schema frequency to input frequency`() {
            // Given
            val derivedDrafts = listOf(
                createCompensationDraft(
                    billingRate = 12000.0,
                    billingFrequency = BillingFrequency.ANNUALLY,
                    schemaCategory = "DERIVED_CATEGORY",
                ),
            )
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 1000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.ANNUALLY, // Schema frequency
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnDerivedDrafts(
                derivedDrafts,
                context,
                inputDrafts,
            )

            // Then - should convert from ANNUALLY (schema) to MONTHLY (input)
            assertThat(result).hasSize(1)
            assertThat(result[0].billingRate).isEqualTo(1000.0) // 12000 / 12
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.MONTHLY)
        }

        @Test
        fun `should handle derived drafts when no input frequency found`() {
            // Given
            val derivedDrafts = listOf(
                createCompensationDraft(
                    billingRate = 12000.0,
                    billingFrequency = BillingFrequency.ANNUALLY,
                ),
            )
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 1000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = "OTHER_CATEGORY", // Not in CATEGORIES_TO_BASE_FREQUENCY_CONVERSION
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.ANNUALLY,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnDerivedDrafts(
                derivedDrafts,
                context,
                inputDrafts,
            )

            // Then - should return original drafts (no conversion due to null input frequency)
            assertThat(result).hasSize(1)
            assertThat(result[0]).isEqualTo(derivedDrafts[0])
        }

        @Test
        fun `should handle derived drafts with null billing rate`() {
            // Given
            val derivedDrafts = listOf(
                createCompensationDraft(
                    billingRate = null,
                    billingFrequency = BillingFrequency.ANNUALLY,
                ),
            )
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 1000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.ANNUALLY,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnDerivedDrafts(
                derivedDrafts,
                context,
                inputDrafts,
            )

            // Then - should return original draft unchanged
            assertThat(result).hasSize(1)
            assertThat(result[0].billingRate).isEqualTo(null)
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.ANNUALLY)
        }
    }

    @Nested
    inner class AdjustBillingFrequencyOnInputDrafts {
        @Test
        fun `should convert monthly to annual correctly`() {
            // Given
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 1000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.ANNUALLY, // Target frequency
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnInputDrafts(
                inputDrafts,
                context,
            )

            // Then
            assertThat(result).hasSize(1)
            assertThat(result[0].billingRate).isEqualTo(12000.0) // 1000 * 12
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.ANNUALLY)
        }

        @Test
        fun `should convert annual to monthly correctly`() {
            // Given
            val derivedDrafts = listOf(
                createCompensationDraft(
                    billingRate = 12000.0,
                    billingFrequency = BillingFrequency.ANNUALLY,
                ),
            )
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 1000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.ANNUALLY,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnDerivedDrafts(
                derivedDrafts,
                context,
                inputDrafts,
            )

            // Then
            assertThat(result).hasSize(1)
            assertThat(result[0].billingRate).isEqualTo(1000.0) // 12000 / 12
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.MONTHLY)
        }

        @Test
        fun `should skip conversion when no TOTAL_COST_TO_COMPANY in schema`() {
            // Given
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 1000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = "OTHER_CATEGORY",
                ),
            )
            val schemaItems = mapOf(
                "Other Component" to createSchemaItem(
                    schemaItemId1,
                    "Other Component",
                    "OTHER_CATEGORY",
                    BillingFrequency.ANNUALLY,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnInputDrafts(
                inputDrafts,
                context,
            )

            // Then - should return original drafts unchanged (no TOTAL_COST_TO_COMPANY)
            assertThat(result).hasSize(1)
            assertThat(result[0]).isEqualTo(inputDrafts[0])
        }

        @Test
        fun `should convert semiannually to quarterly correctly`() {
            // Given
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 6000.0,
                    billingFrequency = BillingFrequency.SEMIANNUALLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.QUARTERLY,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnInputDrafts(
                inputDrafts,
                context,
            )

            // Then
            assertThat(result).hasSize(1)
            assertThat(result[0].billingRate).isEqualTo(3000.0) // 6000 * (2/4) = 3000
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.QUARTERLY)
        }

        @Test
        fun `should convert daily to weekly correctly`() {
            // Given
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 100.0,
                    billingFrequency = BillingFrequency.DAILY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.WEEKLY,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnInputDrafts(
                inputDrafts,
                context,
            )

            // Then
            assertThat(result).hasSize(1)
            assertThat(result[0].billingRate).isEqualTo(701.92) // 100 * (365/52) = 701.92
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.WEEKLY)
        }

        @Test
        fun `should convert quarterly to monthly correctly`() {
            // Given
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 3000.0,
                    billingFrequency = BillingFrequency.QUARTERLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.MONTHLY,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnInputDrafts(
                inputDrafts,
                context,
            )

            // Then
            assertThat(result).hasSize(1)
            assertThat(result[0].billingRate).isEqualTo(1000.0) // 3000 * (4/12) = 1000
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.MONTHLY)
        }

        @Test
        fun `should convert weekly to biweekly correctly`() {
            // Given
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 500.0,
                    billingFrequency = BillingFrequency.WEEKLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.BIWEEKLY,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnInputDrafts(
                inputDrafts,
                context,
            )

            // Then
            assertThat(result).hasSize(1)
            assertThat(result[0].billingRate).isEqualTo(1000.0) // 500 * (52/26) = 1000
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.BIWEEKLY)
        }

        @Test
        fun `should handle null billing rate correctly`() {
            // Given
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = null, // Missing billing rate
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.ANNUALLY,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnInputDrafts(
                inputDrafts,
                context,
            )

            // Then - should return original draft unchanged
            assertThat(result).hasSize(1)
            assertThat(result[0].billingRate).isEqualTo(null)
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.MONTHLY)
        }

        @Test
        fun `should handle multiple drafts correctly`() {
            // Given
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 1000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                ),
                createCompensationDraft(
                    billingRate = 2000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = "OTHER_CATEGORY",
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.ANNUALLY,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnInputDrafts(
                inputDrafts,
                context,
            )

            // Then
            assertThat(result).hasSize(2)
            assertThat(result[0].billingRate).isEqualTo(12000.0) // 1000 * 12
            assertThat(result[1].billingRate).isEqualTo(24000.0) // 2000 * 12
            result.forEach { draft ->
                assertThat(draft.billingFrequency).isEqualTo(BillingFrequency.ANNUALLY)
            }
        }
    }

    @Nested
    inner class MultipleInputsProcessing {
        @Test
        fun `should process multiple drafts with same conversion correctly`() {
            // Given - all drafts will be converted from MONTHLY to ANNUALLY
            // because adjustBillingFrequencyOnInputDrafts applies single conversion to all drafts
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 1000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                ),
                createCompensationDraft(
                    billingRate = 2000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                ),
                createCompensationDraft(
                    billingRate = 500.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = "OTHER_CATEGORY",
                ),
            )

            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.ANNUALLY, // Target frequency
                ),
                "Base Pay" to createSchemaItem(
                    schemaItemId2,
                    "Base Pay",
                    CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                    BillingFrequency.ANNUALLY,
                ),
                "Other Component" to createSchemaItem(
                    schemaItemId3,
                    "Other Component",
                    "OTHER_CATEGORY",
                    BillingFrequency.ANNUALLY,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnInputDrafts(
                inputDrafts,
                context,
            )

            // Then - all drafts converted from MONTHLY to ANNUALLY
            assertThat(result).hasSize(3)

            // All drafts: MONTHLY to ANNUALLY (multiply by 12)
            assertThat(result[0].billingRate).isEqualTo(12000.0) // 1000 * 12
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.ANNUALLY)

            assertThat(result[1].billingRate).isEqualTo(24000.0) // 2000 * 12
            assertThat(result[1].billingFrequency).isEqualTo(BillingFrequency.ANNUALLY)

            assertThat(result[2].billingRate).isEqualTo(6000.0) // 500 * 12
            assertThat(result[2].billingFrequency).isEqualTo(BillingFrequency.ANNUALLY)
        }

        @Test
        fun `should skip conversion for FIXED item types`() {
            // Given
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 1000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    schemaItemId = schemaItemId1,
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.ANNUALLY,
                    itemType = ItemType.FIXED, // This should cause skipping
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems
            every { context.getCompensationSchemaItem(schemaItemId1) } returns schemaItems["Total Cost"]

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnInputDrafts(
                inputDrafts,
                context,
            )

            // Then - should return original draft unchanged (no conversion for FIXED)
            assertThat(result).hasSize(1)
            assertThat(result[0].billingRate).isEqualTo(1000.0) // Original rate
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.MONTHLY) // Original frequency
        }

        @Test
        fun `should skip conversion when schemaItemId is null`() {
            // Given
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 1000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    schemaItemId = null,
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.ANNUALLY,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnInputDrafts(
                inputDrafts,
                context,
            )

            // Then - should return original draft unchanged (null schemaItemId)
            assertThat(result).hasSize(1)
            assertThat(result[0].billingRate).isEqualTo(1000.0) // Original rate
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.MONTHLY) // Original frequency
        }

        @Test
        fun `should convert INPUT item types normally`() {
            // Given
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 1000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    schemaItemId = schemaItemId1,
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.ANNUALLY,
                    itemType = ItemType.INPUT, // This should allow conversion
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems
            every { context.getCompensationSchemaItem(schemaItemId1) } returns schemaItems["Total Cost"]

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnInputDrafts(
                inputDrafts,
                context,
            )

            // Then - should convert frequency and amount
            assertThat(result).hasSize(1)
            assertThat(result[0].billingRate).isEqualTo(12000.0) // 1000 * 12
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.ANNUALLY)
        }
    }

    @Nested
    inner class AdjustBillingFrequencyOnDerivedDraftsFixedItemTypes {
        @Test
        fun `should skip conversion for FIXED item types in derived drafts`() {
            // Given
            val derivedDrafts = listOf(
                createCompensationDraft(
                    billingRate = 12000.0,
                    billingFrequency = BillingFrequency.ANNUALLY,
                    schemaItemId = schemaItemId1,
                ),
            )
            val inputDrafts = listOf(
                createCompensationDraft(
                    billingRate = 1000.0,
                    billingFrequency = BillingFrequency.MONTHLY,
                    schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                ),
            )
            val schemaItems = mapOf(
                "Total Cost" to createSchemaItem(
                    schemaItemId1,
                    "Total Cost",
                    CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
                    BillingFrequency.ANNUALLY,
                    itemType = ItemType.FIXED,
                ),
            )

            every { context.getCompensationSchemaItems() } returns schemaItems
            every { context.getCompensationSchemaItem(schemaItemId1) } returns schemaItems["Total Cost"]

            // When
            val result = compensationFrequencyConverter.adjustBillingFrequencyOnDerivedDrafts(
                derivedDrafts,
                context,
                inputDrafts,
            )

            // Then - should return original draft unchanged (FIXED type)
            assertThat(result).hasSize(1)
            assertThat(result[0].billingRate).isEqualTo(12000.0) // Original rate
            assertThat(result[0].billingFrequency).isEqualTo(BillingFrequency.ANNUALLY) // Original frequency
        }
    }

    private fun createSchemaItem(
        id: UUID,
        componentName: String,
        category: String,
        billingFrequency: BillingFrequency?,
        itemType: ItemType = ItemType.FIXED,
        isPartOfCtc: Boolean = false,
        isOvertimeEligible: Boolean = false,
    ): CompensationSchemaItem = CompensationSchemaItem(
        id = id,
        schemaId = UUID.randomUUID(),
        componentName = componentName,
        category = category,
        isTaxable = true,
        isProrated = false,
        isFixed = true,
        isActive = true,
        isMandatory = false,
        isPartOfBasePay = false,
        label = componentName,
        itemType = itemType,
        validation = null,
        calculation = null,
        billingRateType = BillingRateType.VALUE,
        isOvertimeEligible = isOvertimeEligible,
        description = "Test component description",
        billingFrequency = billingFrequency,
        payScheduleId = null,
        currency = "USD",
        createdOn = java.time.LocalDateTime.now(),
        createdBy = 1L,
        updatedOn = java.time.LocalDateTime.now(),
        updatedBy = 1L,
        isPartOfCtc = isPartOfCtc,
    )

    private fun createCompensationDraft(
        billingRate: Double?,
        billingFrequency: BillingFrequency,
        companyId: Long = 1L,
        employeeId: String = "EMP001",
        contractId: Long = 1L,
        schemaItemId: UUID? = UUID.randomUUID(),
        schemaCategory: String = "CONTRACT_BASE_PAY",
        currency: String = "USD",
        billingRateType: BillingRateType = BillingRateType.VALUE,
        payScheduleId: UUID = UUID.randomUUID(),
        startDate: java.time.LocalDate = java.time.LocalDate.now(),
        endDate: java.time.LocalDate? = java.time.LocalDate.now().plusYears(1),
        isInstallment: Boolean = false,
        noOfInstallments: Int? = null,
    ) = CompensationDraft(
        companyId = companyId,
        employeeId = employeeId,
        contractId = contractId,
        schemaItemId = schemaItemId,
        schemaCategory = schemaCategory,
        currency = currency,
        billingRateType = billingRateType,
        billingRate = billingRate,
        billingFrequency = billingFrequency,
        payScheduleId = payScheduleId,
        startDate = startDate,
        endDate = endDate,
        isInstallment = isInstallment,
        noOfInstallments = noOfInstallments,
        reasonCode = null,
    )
}
