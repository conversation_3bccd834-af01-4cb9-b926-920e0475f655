package com.multiplier.compensation.service.compensation.extrapolation.utils

import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.util.stream.Stream

class AmountCalculationUtilTest {
    @Test
    fun `evaluate should calculate simple arithmetic expression without variables`() {
        val request = FormulaRequest(
            formula = "2 + 3 * 4",
            variables = emptyMap(),
        )

        val result = evaluateFormula(request)

        assertEquals(14.0, result)
    }

    @Test
    fun `evaluate should calculate expression with variables`() {
        val request = FormulaRequest(
            formula = "a + b * c",
            variables = mapOf(
                "a" to BigDecimal("2.0"),
                "b" to BigDecimal("3.0"),
                "c" to BigDecimal("4.0"),
            ),
        )

        val result = evaluateFormula(request)

        assertEquals(14.0, result)
    }

    @Test
    fun `evaluate should handle decimal results with proper formatting`() {
        val request = FormulaRequest(
            formula = "a / b",
            variables = mapOf(
                "a" to BigDecimal("10.0"),
                "b" to BigDecimal("3.0"),
            ),
        )

        val result = evaluateFormula(request)

        assertEquals(3.0, result)
    }

    @Test
    fun `evaluate should round decimal results properly`() {
        val request = FormulaRequest(
            formula = "a / b",
            variables = mapOf(
                "a" to BigDecimal("2.0"),
                "b" to BigDecimal("3.0"),
            ),
        )

        val result = evaluateFormula(request)

        assertEquals(1.0, result)
    }

    @Test
    fun `evaluate should handle complex expressions`() {
        val request = FormulaRequest(
            formula = "(a + b) * (c - d) / e",
            variables = mapOf(
                "a" to BigDecimal("5.0"),
                "b" to BigDecimal("3.0"),
                "c" to BigDecimal("10.0"),
                "d" to BigDecimal("2.0"),
                "e" to BigDecimal("4.0"),
            ),
        )

        val result = evaluateFormula(request)

        assertEquals(16.0, result)
    }

    @Test
    fun `evaluate should handle expressions with functions`() {
        val request = FormulaRequest(
            formula = "SQRT(a^2 + b^2)",
            variables = mapOf(
                "a" to BigDecimal("3.0"),
                "b" to BigDecimal("4.0"),
            ),
        )

        val result = evaluateFormula(request)

        assertEquals(5.0, result)
    }

    @Test
    fun `evaluate should throw exception for undefined variables`() {
        val request = FormulaRequest(
            formula = "a + b + c",
            variables = mapOf(
                "a" to BigDecimal("1.0"),
                "b" to BigDecimal("2.0"),
                // c is missing
            ),
        )

        assertThrows<InvalidArgumentException> {
            evaluateFormula(request)
        }
    }

    @Test
    fun `evaluate should throw exception for invalid formula`() {
        val request = FormulaRequest(
            formula = "a + * b",
            variables = mapOf(
                "a" to BigDecimal("1.0"),
                "b" to BigDecimal("2.0"),
            ),
        )

        assertThrows<InvalidArgumentException> {
            evaluateFormula(request)
        }
    }

    @ParameterizedTest
    @MethodSource("provideFormulasAndExpectedResults")
    fun `evaluate should calculate various expressions correctly`(
        formula: String,
        variables: Map<String, BigDecimal>,
        expectedResult: Double,
    ) {
        val request = FormulaRequest(formula, variables)

        val result = evaluateFormula(request)

        assertEquals(expectedResult, result)
    }

    companion object {
        @JvmStatic
        fun provideFormulasAndExpectedResults(): Stream<Arguments> = Stream.of(
            Arguments.of("total_ctc - 21600", mapOf("total_ctc" to BigDecimal("200000.0")), 178400.0),
            Arguments.of("0.4* total_ctc", mapOf("total_ctc" to BigDecimal("200000.0")), 80000.0),
            Arguments.of("0.16*total_ctc", mapOf("total_ctc" to BigDecimal("200000.0")), 32000.0),
            Arguments.of("0.033*total_ctc", mapOf("total_ctc" to BigDecimal("200000.0")), 6600.0),
            Arguments.of("if(total_ctc>=1000000,12000,0)", mapOf("total_ctc" to BigDecimal("200000.0")), 0.0),
            Arguments.of("if(total_ctc>=1000000,24000,0)", mapOf("total_ctc" to BigDecimal("2000000.0")), 24000.0),
            Arguments.of(
                "if(total_ctc >= 2000000, (0.407 * total_ctc - 105600), if(total_ctc >= 1000000, (0.407 * total_ctc - 81600), (0.407 * total_ctc - 21600)))",
                mapOf(
                    "total_ctc" to BigDecimal("200000.0"),
                ),
                59800.0,
            ),
            Arguments.of(
                "(input >= xx) && (input <=yy)",
                mapOf(
                    "input" to BigDecimal("10.0"),
                    "xx" to BigDecimal("5.0"),
                    "yy" to BigDecimal("20.0"),
                ),
                1.0,
            ),
            Arguments.of("a * b", mapOf("a" to BigDecimal("5.0"), "b" to BigDecimal("6.0")), 30.0),
            Arguments.of("(a + b) / 2", mapOf("a" to BigDecimal("10.0"), "b" to BigDecimal("20.0")), 15.0),
            Arguments.of("a % b", mapOf("a" to BigDecimal("10.0"), "b" to BigDecimal("3.0")), 1.0),
            Arguments.of("min(a,b)", mapOf("a" to BigDecimal("10.234"), "b" to BigDecimal("3.12")), 3.0),
            Arguments.of("max(a,b)", mapOf("a" to BigDecimal("10.6"), "b" to BigDecimal("3.3")), 11.0),
            Arguments.of(
                "max(a,max(b,c))",
                mapOf(
                    "a" to BigDecimal("10.6"),
                    "b" to BigDecimal("3.3"),
                    "c" to BigDecimal("10.7"),
                ),
                11.0,
            ),
            Arguments.of("round(3.14159, 2)", emptyMap<String, BigDecimal>(), 3.0),
            Arguments.of("21600", emptyMap<String, BigDecimal>(), 21600.0), // Fixed Item Type
            Arguments.of("(total_ctc-75)*0.5/1.0625", mapOf("total_ctc" to BigDecimal("3719650")), 1750388),
            Arguments.of(
                "if(total_ctc>=2000000, ((total_ctc-75)*0.25834/1.0625)-84000, if(total_ctc>=1000000, ((total_ctc-75)*0.25834/1.0625)-60000, (total_ctc-75)*0.25834/1.0625))",
                mapOf(
                    "total_ctc" to BigDecimal("4018692"),
                ),
                893101.0,
            ),
            Arguments.of("total_ctc/2", mapOf("total_ctc" to BigDecimal("21.0")), 11),
        )
    }

    @Test
    fun `evaluate should handle zero division gracefully`() {
        val request = FormulaRequest(
            formula = "a / b",
            variables = mapOf(
                "a" to BigDecimal("10.0"),
                "b" to BigDecimal("0.0"),
            ),
        )

        assertThrows<InvalidArgumentException> {
            evaluateFormula(request)
        }
    }

    @Test
    fun `evaluate should handle very large numbers`() {
        val request = FormulaRequest(
            formula = "a * b",
            variables = mapOf(
                "a" to BigDecimal("1000000.0"),
                "b" to BigDecimal("1000000.0"),
            ),
        )

        val result = evaluateFormula(request)

        assertEquals(1000000000000.0, result)
    }

    @Test
    fun `evaluate should handle very small numbers`() {
        val request = FormulaRequest(
            formula = "a * b",
            variables = mapOf(
                "a" to BigDecimal("0.0000001"),
                "b" to BigDecimal("0.0000001"),
            ),
        )

        val result = evaluateFormula(request)

        assertEquals(0.0, result)
    }

    @Test
    fun `evaluate should handle negative numbers`() {
        val request = FormulaRequest(
            formula = "a + b",
            variables = mapOf(
                "a" to BigDecimal("-5.0"),
                "b" to BigDecimal("3.0"),
            ),
        )

        val result = evaluateFormula(request)

        assertEquals(-2.0, result)
    }

    @Test
    fun `evaluate should handle complex formula`() {
        val request = FormulaRequest(
            formula =
                "if(total_ctc>=2000000,(0.407*total_ctc-105600), (if (total_ctc>=1000000,(0.407*total_ctc-81600)," +
                    "(0.407*total_ctc-21600))))",
            variables = mapOf(
                "total_ctc" to BigDecimal("100000"),
            ),
        )

        val result = evaluateFormula(request)

        assertEquals(19100.0, result)
    }

    @Test
    fun `parseFixedAmount should parse valid amount string`() {
        val result = parseFixedAmount("1000.50", "Test Item")
        assertEquals(1000.50, result)
    }

    @Test
    fun `parseFixedAmount should parse integer amount string`() {
        val result = parseFixedAmount("1000", "Test Item")
        assertEquals(1000.0, result)
    }

    @Test
    fun `parseFixedAmount should parse negative amount string`() {
        val result = parseFixedAmount("-500.25", "Test Item")
        assertEquals(-500.25, result)
    }

    @Test
    fun `parseFixedAmount should throw exception for null amount`() {
        assertThrows<InvalidArgumentException> {
            parseFixedAmount(null, "Test Item")
        }
    }

    @Test
    fun `parseFixedAmount should throw exception for invalid amount string`() {
        assertThrows<InvalidArgumentException> {
            parseFixedAmount("invalid", "Test Item")
        }
    }

    @Test
    fun `parseFixedAmount should throw exception for empty amount string`() {
        assertThrows<InvalidArgumentException> {
            parseFixedAmount("", "Test Item")
        }
    }
}
