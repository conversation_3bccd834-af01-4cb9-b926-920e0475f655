package com.multiplier.compensation.service.compensation.validation.stages

import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.validators.CompensationCountryGroupValidator
import com.multiplier.compensation.service.compensation.validation.validators.CompensationEntityGroupValidator
import com.multiplier.compensation.service.compensation.validation.validators.SchemaCountryGroupValidator
import com.multiplier.compensation.service.compensation.validation.validators.SchemaResolutionGroupValidator
import com.multiplier.compensation.service.compensation.validation.validators.SchemaTagGroupValidator
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifyAll
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import kotlin.test.assertFalse

@ExtendWith(MockKExtension::class)
class CompensationRevisionBackFillGroupValidationStageTest {
    private lateinit var groupValidationStage: CompensationRevisionBackFillGroupValidationStage
    private lateinit var schemaResolutionGroupValidator: SchemaResolutionGroupValidator
    private lateinit var schemaTagGroupValidator: SchemaTagGroupValidator
    private lateinit var schemaCountryGroupValidator: SchemaCountryGroupValidator
    private lateinit var compensationCountryGroupValidator: CompensationCountryGroupValidator
    private lateinit var compensationEntityGroupValidator: CompensationEntityGroupValidator
    private lateinit var context: CompensationValidatorContext

    @BeforeEach
    fun setUp() {
        schemaResolutionGroupValidator = mockk()
        schemaTagGroupValidator = mockk()
        schemaCountryGroupValidator = mockk()
        compensationCountryGroupValidator = mockk()
        compensationEntityGroupValidator = mockk()

        groupValidationStage = CompensationRevisionBackFillGroupValidationStage(
            schemaResolutionGroupValidator,
            schemaTagGroupValidator,
            schemaCountryGroupValidator,
            compensationCountryGroupValidator,
            compensationEntityGroupValidator,
        )

        context = mockk()
    }

    @Test
    fun `should process group validation stage successfully`() {
        every { schemaResolutionGroupValidator.validate(any(), context, any()) } returns true
        every { schemaTagGroupValidator.validate(any(), context, any()) } returns true
        every { schemaCountryGroupValidator.validate(any(), context, any()) } returns true
        every { compensationCountryGroupValidator.validate(any(), context, any()) } returns true
        every { compensationEntityGroupValidator.validate(any(), context, any()) } returns true

        assertTrue { groupValidationStage.process(emptyList(), context, mockk()) }

        verifyAll {
            schemaResolutionGroupValidator.validate(any(), context, any())
            schemaTagGroupValidator.validate(any(), context, any())
            schemaCountryGroupValidator.validate(any(), context, any())
            compensationCountryGroupValidator.validate(any(), context, any())
            compensationEntityGroupValidator.validate(any(), context, any())
        }
    }

    @Test
    fun `should stop validation when one validator fails`() {
        every { schemaResolutionGroupValidator.validate(any(), context, any()) } returns true
        every { schemaTagGroupValidator.validate(any(), context, any()) } returns true
        every { schemaCountryGroupValidator.validate(any(), context, any()) } returns false

        assertFalse { groupValidationStage.process(emptyList(), context, mockk()) }

        verifyAll {
            schemaResolutionGroupValidator.validate(any(), context, any())
            schemaTagGroupValidator.validate(any(), context, any())
            schemaCountryGroupValidator.validate(any(), context, any())
        }
    }

    @Test
    fun `should stop validation when first validator fails`() {
        every { schemaResolutionGroupValidator.validate(any(), context, any()) } returns false
        every { schemaTagGroupValidator.validate(any(), context, any()) } returns false
        every { schemaCountryGroupValidator.validate(any(), context, any()) } returns false
        every { compensationCountryGroupValidator.validate(any(), context, any()) } returns false
        every { compensationEntityGroupValidator.validate(any(), context, any()) } returns false

        assertFalse { groupValidationStage.process(emptyList(), context, mockk()) }

        verify {
            schemaResolutionGroupValidator.validate(any(), context, any())
        }
        verify(exactly = 0) {
            schemaTagGroupValidator.validate(any(), context, any())
            schemaCountryGroupValidator.validate(any(), context, any())
            compensationCountryGroupValidator.validate(any(), context, any())
            compensationEntityGroupValidator.validate(any(), context, any())
        }
    }

    @Test
    fun `should stop validation when middle validator fails`() {
        every { schemaResolutionGroupValidator.validate(any(), context, any()) } returns true
        every { schemaTagGroupValidator.validate(any(), context, any()) } returns true
        every { schemaCountryGroupValidator.validate(any(), context, any()) } returns false
        every { compensationCountryGroupValidator.validate(any(), context, any()) } returns true
        every { compensationEntityGroupValidator.validate(any(), context, any()) } returns true

        assertFalse { groupValidationStage.process(emptyList(), context, mockk()) }

        verifyAll {
            schemaResolutionGroupValidator.validate(any(), context, any())
            schemaTagGroupValidator.validate(any(), context, any())
            schemaCountryGroupValidator.validate(any(), context, any())
        }

        verify(exactly = 0) {
            compensationCountryGroupValidator.validate(any(), context, any())
            compensationEntityGroupValidator.validate(any(), context, any())
        }
    }
}
