package com.multiplier.compensation.service.compensation.validation.stages

import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.validators.SchemaCountryGroupValidator
import com.multiplier.compensation.service.compensation.validation.validators.SchemaResolutionGroupValidator
import com.multiplier.compensation.service.compensation.validation.validators.SchemaTagGroupValidator
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifyAll
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import kotlin.test.assertFalse

@ExtendWith(MockKExtension::class)
class DeductionGroupValidationStageTest {
    private lateinit var groupValidationStage: DeductionGroupValidationStage
    private lateinit var schemaResolutionGroupValidator: SchemaResolutionGroupValidator
    private lateinit var schemaTagGroupValidator: SchemaTagGroupValidator
    private lateinit var schemaCountryGroupValidator: SchemaCountryGroupValidator
    private lateinit var context: CompensationValidatorContext

    @BeforeEach
    fun setUp() {
        schemaResolutionGroupValidator = mockk()
        schemaTagGroupValidator = mockk()
        schemaCountryGroupValidator = mockk()

        groupValidationStage = DeductionGroupValidationStage(
            schemaResolutionGroupValidator,
            schemaTagGroupValidator,
            schemaCountryGroupValidator,
        )

        context = mockk()
    }

    @Test
    fun `should process group validation stage successfully`() {
        every { schemaResolutionGroupValidator.validate(any(), context, any()) } returns true
        every { schemaTagGroupValidator.validate(any(), context, any()) } returns true
        every { schemaCountryGroupValidator.validate(any(), context, any()) } returns true

        assertTrue { groupValidationStage.process(emptyList(), context, mockk()) }

        verifyAll {
            schemaResolutionGroupValidator.validate(any(), context, any())
            schemaTagGroupValidator.validate(any(), context, any())
            schemaCountryGroupValidator.validate(any(), context, any())
        }
    }

    @Test
    fun `should stop validation when one validator fails`() {
        every { schemaResolutionGroupValidator.validate(any(), context, any()) } returns true
        every { schemaTagGroupValidator.validate(any(), context, any()) } returns true
        every { schemaCountryGroupValidator.validate(any(), context, any()) } returns false

        assertFalse { groupValidationStage.process(emptyList(), context, mockk()) }

        verifyAll {
            schemaResolutionGroupValidator.validate(any(), context, any())
            schemaTagGroupValidator.validate(any(), context, any())
            schemaCountryGroupValidator.validate(any(), context, any())
        }
    }

    @Test
    fun `should stop validation when the first validator fails`() {
        every { schemaResolutionGroupValidator.validate(any(), context, any()) } returns false

        assertFalse { groupValidationStage.process(emptyList(), context, mockk()) }

        verify(exactly = 1) { schemaResolutionGroupValidator.validate(any(), context, any()) }
        verify(exactly = 0) { schemaTagGroupValidator.validate(any(), context, any()) }
        verify(exactly = 0) { schemaCountryGroupValidator.validate(any(), context, any()) }
    }
}
