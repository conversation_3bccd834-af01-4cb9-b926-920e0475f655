package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.common.validationpipeline.ValidationDraftObject
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate

@Suppress("UNCHECKED_CAST")
class CompensationEntityGroupValidatorTest {
    private lateinit var validator: CompensationEntityGroupValidator
    private lateinit var context: CompensationValidatorContext
    private lateinit var collector: ValidationDataCollector<CompensationDraft>
    private val skeleton = mockk<Skeleton>(relaxed = true)

    private val draftId = "1"
    private val contractId = 123L
    private val entityId = 100L

    @BeforeEach
    fun setup() {
        validator = CompensationEntityGroupValidator()
        context = mockCompensationValidatorContext()
        collector = mockValidationDataCollector()
    }

    @Test
    fun `should pass when all contracts match entity ID`() {
        val inputs = listOf(mockk<ValidationInputItem>(relaxed = true))
        val result = validator.validate(inputs, context, collector)
        assertTrue(result)
    }

    @Test
    fun `should fail when a contract does not match entity ID`() {
        val inputs = mockValidationInputItems()

        val context = context.copy(
            entityId = 200L,
        )

        val result = validator.validate(inputs, context, collector)
        assertFalse(result)
        val error = collector.rowValidationResult[draftId]?.first()?.message
        assertTrue(error?.contains("does not match with the given entity id") == true)
    }

    @Test
    fun `should return true when offering type in GLOBAL_PAYROLL`() {
        val inputs = mockValidationInputItems()
        val context = context.copy(
            customParams = mapOf(
                "OFFERING_TYPE" to OfferingType.GLOBAL_PAYROLL.name,
            ),
        )

        val result = validator.validate(inputs, context, collector)
        assertTrue(result)
    }

    @Test
    fun `should return false when context entity id is different than contract entity id`() {
        val inputs = mockValidationInputItems()
        val context = context.copy(
            entityId = 200L,
        )

        val result = validator.validate(inputs, context, collector)
        assertFalse(result)
    }

    @Test
    fun `should fail when one of the contract has different entity id`() {
        val inputs = mockValidationInputItems().toMutableList()
        inputs.add(
            ValidationInputItem(
                id = "3",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(
                        key = "ENTITY_ID",
                        value = "200",
                    ),
                    KeyValuePair(
                        key = "CONTRACT_ID",
                        value = "234",
                    ),
                ),
            ),
        )

        val drafts = collector.drafts.toMutableMap()
        drafts["3"] = CompensationDraft(
            contractId = 234,
            companyId = 1L,
            employeeId = "1",
            schemaItemId = null,
            schemaCategory = null,
            currency = null,
            billingRateType = null,
            billingRate = null,
            billingFrequency = null,
            payScheduleId = null,
            startDate = null,
            endDate = null,
            isInstallment = false,
            noOfInstallments = null,
            reasonCode = null,
        )
        val collector = collector.copy(
            drafts = drafts,
        )

        val result = validator.validate(inputs, context, collector)
        assertFalse(result)
        val error = collector.rowValidationResult["3"]?.first()?.message
        assertTrue(error?.contains("does not match with the given entity id") == true)
    }

    @Test
    fun `should return true when setup validation is true`() {
        val inputs = mockValidationInputItems()
        val collector = ValidationDataCollector(
            rowValidationResult = mutableMapOf(),
            drafts = mutableMapOf(
                "0" to CompensationDraft(
                    contractId = 0L,
                    companyId = 1L,
                    employeeId = "1",
                    schemaItemId = null,
                    schemaCategory = null,
                    currency = null,
                    billingRateType = null,
                    billingRate = null,
                    billingFrequency = null,
                    payScheduleId = null,
                    startDate = null,
                    endDate = null,
                    isInstallment = false,
                    noOfInstallments = null,
                    reasonCode = null,
                ),
            ),
            inputPlusDerivedRows = emptyMap(),
        )

        val result = validator.validate(inputs, context, collector)
        assertTrue(result)
    }

    private fun <T : ValidationDraftObject> mockValidationDataCollector(): ValidationDataCollector<T> =
        ValidationDataCollector<T>(
            rowValidationResult = mutableMapOf(),
            drafts = mutableMapOf(
                draftId to CompensationDraft(
                    contractId = contractId,
                    companyId = 1L,
                    employeeId = "1",
                    schemaItemId = null,
                    schemaCategory = null,
                    currency = null,
                    billingRateType = null,
                    billingRate = null,
                    billingFrequency = null,
                    payScheduleId = null,
                    startDate = null,
                    endDate = null,
                    isInstallment = false,
                    noOfInstallments = null,
                    reasonCode = null,
                ) as T,
            ),
            inputPlusDerivedRows = emptyMap(),
        )

    private fun mockCompensationValidatorContext(): CompensationValidatorContext = CompensationValidatorContext(
        entityId = entityId,
        contracts = mapOf(
            contractId to Contract(
                id = contractId,
                companyId = 20L,
                memberId = 30L,
                employeeId = "123",
                status = ContractStatus.ACTIVE,
                startOn = LocalDate.of(2023, 1, 1),
                currency = "USD",
                countryCode = CountryCode.USA,
                workplaceEntityId = entityId,
                stateCode = "CA",
                contractType = ContractType.HR_MEMBER,
            ),
            234L to Contract(
                id = 234L,
                companyId = 20L,
                memberId = 30L,
                employeeId = "123",
                status = ContractStatus.ACTIVE,
                startOn = LocalDate.of(2023, 1, 1),
                currency = "USD",
                countryCode = CountryCode.USA,
                workplaceEntityId = 200L,
                stateCode = "CA",
                contractType = ContractType.HR_MEMBER,
            ),
        ),
        skeleton = skeleton,
        contextSource = RequestType.COMPENSATION_SETUP,
        contextUseCase = ContextUseCase.VALIDATE,
        customParams = mapOf(
            "OFFERING_TYPE" to OfferingType.EOR.name,
        ),
    )

    private fun mockValidationInputItems(): List<ValidationInputItem> = listOf(
        ValidationInputItem(
            id = draftId,
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = "ENTITY_ID",
                    value = entityId.toString(),
                ),
                KeyValuePair(
                    key = "CONTRACT_ID",
                    value = contractId.toString(),
                ),
            ),
        ),
        ValidationInputItem(
            id = "2",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = "ENTITY_ID",
                    value = entityId.toString(),
                ),
                KeyValuePair(
                    key = "CONTRACT_ID",
                    value = "456",
                ),
            ),
        ),
    )
}
