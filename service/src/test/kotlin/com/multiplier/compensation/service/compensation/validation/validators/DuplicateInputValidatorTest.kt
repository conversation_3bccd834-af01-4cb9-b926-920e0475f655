package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import io.mockk.mockk
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class DuplicateInputValidatorTest {
    private lateinit var validator: DuplicateInputValidator
    private lateinit var context: CompensationValidatorContext
    private lateinit var collector: ValidationDataCollector<CompensationDraft>
    private val skeleton = mockk<Skeleton>(relaxed = true)

    private val draftId1 = "1"
    private val draftId2 = "2"
    private val contractId = 123L
    private val countryCode = CountryCode.USA
    private val entityId = 100L
    private val monthlyPayScheduleId = UUID.randomUUID()
    private val monthlyInstallmentPayScheduleId = UUID.randomUUID()

    @BeforeEach
    fun setup() {
        validator = DuplicateInputValidator()
        context = CompensationValidatorContext(
            entityId = entityId,
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 20L,
                    memberId = 30L,
                    employeeId = "123",
                    status = ContractStatus.ACTIVE,
                    startOn = LocalDate.of(2023, 1, 1),
                    currency = "USD",
                    countryCode = countryCode,
                    workplaceEntityId = 100L,
                    stateCode = "CA",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            skeleton = skeleton,
            contextSource = RequestType.COMPENSATION_REVISION_BACKFILL,
            contextUseCase = ContextUseCase.UPSERT,
            customParams = emptyMap(),
            paySchedules = mapOf(
                "MONTHLY_SCHEDULE" to createPaySchedule(monthlyPayScheduleId, false),
                "MONTHLY_INSTALLMENT_SCHEDULE" to createPaySchedule(monthlyInstallmentPayScheduleId, true),
            ),
        )
        collector = ValidationDataCollector(
            rowValidationResult = mutableMapOf(),
            drafts = mutableMapOf(
                draftId1 to CompensationDraft(
                    contractId = contractId,
                    companyId = 1L,
                    employeeId = "1",
                    schemaItemId = null,
                    schemaCategory = null,
                    currency = null,
                    billingRateType = null,
                    billingRate = null,
                    billingFrequency = null,
                    payScheduleId = null,
                    startDate = null,
                    endDate = null,
                    isInstallment = false,
                    noOfInstallments = null,
                    reasonCode = null,
                ),
            ),
            inputPlusDerivedRows = emptyMap(),
        )
    }

    @Test
    fun `should detect duplicate inputs and fail validation`() {
        // Create input with the same contract ID
        val input = ValidationInputItem(
            id = draftId1,
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.CONTRACT_ID.id,
                    value = contractId.toString(),
                ),
                KeyValuePair(
                    key = CompensationSkeletonField.COMPONENT_NAME.id,
                    value = "Base Pay",
                ),
            ),
        )

        // Add a duplicate draft to the collector
        val draftsWithDuplicates = collector.drafts.toMutableMap()
        draftsWithDuplicates[draftId2] = CompensationDraft(
            contractId = contractId, // Same contract ID
            companyId = 1L,
            employeeId = "1",
            schemaItemId = null,
            schemaCategory = null,
            currency = null,
            billingRateType = null,
            billingRate = null,
            billingFrequency = null,
            payScheduleId = null,
            startDate = null,
            endDate = null,
            isInstallment = false,
            noOfInstallments = null,
            reasonCode = null,
        )

        val collectorWithDuplicates = collector.copy(drafts = draftsWithDuplicates)

        // Run validation
        val result = validator.validate(input, context, collectorWithDuplicates)

        // Validation should fail due to duplicate inputs
        Assertions.assertFalse(result)

        // Check that error message was added
        val error = collectorWithDuplicates.rowValidationResult[draftId1]?.firstOrNull()?.message
        Assertions.assertTrue(error?.contains("Duplicate inputs found") == true)
    }

    @Test
    fun `should detect overlapping dates and fail validation`() {
        // Create input with the same contract ID and schema item ID but different dates
        val input = ValidationInputItem(
            id = draftId1,
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.CONTRACT_ID.id,
                    value = contractId.toString(),
                ),
                KeyValuePair(
                    key = CompensationSkeletonField.COMPONENT_NAME.id,
                    value = "Base Pay",
                ),
            ),
        )

        val schemaItemId = UUID.randomUUID()

        // Add two drafts with the same schema item ID and overlapping dates
        val draftsWithOverlap = collector.drafts.toMutableMap()
        draftsWithOverlap[draftId1] = CompensationDraft(
            contractId = contractId,
            companyId = 1L,
            employeeId = "1",
            schemaItemId = schemaItemId,
            schemaCategory = "CONTRACT_BASE_PAY",
            currency = "USD",
            billingRateType = null,
            billingRate = null,
            billingFrequency = null,
            payScheduleId = null,
            startDate = LocalDate.of(2023, 1, 1),
            endDate = LocalDate.of(2023, 12, 31),
            isInstallment = false,
            noOfInstallments = null,
            reasonCode = null,
        )

        draftsWithOverlap[draftId2] = CompensationDraft(
            contractId = contractId,
            companyId = 1L,
            employeeId = "1",
            schemaItemId = schemaItemId, // Same schema item ID
            schemaCategory = "CONTRACT_BASE_PAY",
            currency = "USD",
            billingRateType = null,
            billingRate = null,
            billingFrequency = null,
            payScheduleId = null,
            startDate = LocalDate.of(2023, 6, 1), // Overlapping date range
            endDate = LocalDate.of(2024, 5, 31),
            isInstallment = false,
            noOfInstallments = null,
            reasonCode = null,
        )

        val collectorWithOverlap = collector.copy(drafts = draftsWithOverlap)

        // Run validation
        val result = validator.validate(input, context, collectorWithOverlap)

        // Validation should fail due to overlapping dates
        Assertions.assertFalse(result)

        // Check that error message was added
        val error = collectorWithOverlap.rowValidationResult[draftId1]?.firstOrNull()?.message
        Assertions.assertTrue(error?.contains("Overlapping dates found") == true)
    }

    @Test
    fun `should pass validation when no duplicates or overlaps exist`() {
        // Create input with a unique contract ID
        val input = ValidationInputItem(
            id = draftId1,
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.CONTRACT_ID.id,
                    value = contractId.toString(),
                ),
                KeyValuePair(
                    key = CompensationSkeletonField.COMPONENT_NAME.id,
                    value = "Base Pay",
                ),
            ),
        )

        // Run validation with only one draft
        val result = validator.validate(input, context, collector)

        // Validation should pass
        Assertions.assertTrue(result)

        // No error messages should be added
        Assertions.assertTrue(collector.rowValidationResult.isEmpty())
    }

    @Test
    fun `should detect overlap when all dates are the same`() {
        // Create input with the same contract ID and schema item ID but identical dates
        val input = ValidationInputItem(
            id = draftId1,
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.CONTRACT_ID.id,
                    value = contractId.toString(),
                ),
                KeyValuePair(
                    key = CompensationSkeletonField.COMPONENT_NAME.id,
                    value = "Base Pay",
                ),
            ),
        )

        val schemaItemId = UUID.randomUUID()
        val sameDate = LocalDate.of(2023, 5, 15)

        // Add two drafts with the same schema item ID and identical dates
        val draftsWithSameDates = mutableMapOf<String, CompensationDraft>()
        draftsWithSameDates[draftId1] = CompensationDraft(
            contractId = contractId,
            companyId = 1L,
            employeeId = "1",
            schemaItemId = schemaItemId,
            schemaCategory = "CONTRACT_BASE_PAY",
            currency = "USD",
            billingRateType = null,
            billingRate = null,
            billingFrequency = null,
            payScheduleId = null,
            startDate = sameDate,
            endDate = sameDate,
            isInstallment = false,
            noOfInstallments = null,
            reasonCode = null,
        )

        draftsWithSameDates[draftId2] = CompensationDraft(
            contractId = contractId,
            companyId = 1L,
            employeeId = "1",
            schemaItemId = schemaItemId, // Same schema item ID
            schemaCategory = "CONTRACT_BASE_PAY",
            currency = "USD",
            billingRateType = null,
            billingRate = null,
            billingFrequency = null,
            payScheduleId = null,
            startDate = sameDate, // Same start date
            endDate = sameDate, // Same end date
            isInstallment = false,
            noOfInstallments = null,
            reasonCode = null,
        )

        val collectorWithSameDates = ValidationDataCollector(
            drafts = draftsWithSameDates,
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        // Run validation
        val result = validator.validate(input, context, collectorWithSameDates)

        // Validation should fail due to overlapping dates
        Assertions.assertFalse(result)
    }

    private fun createPaySchedule(
        id: UUID,
        isInstallment: Boolean,
    ): PaySchedule = PaySchedule(
        id = id,
        entityId = entityId,
        companyId = 20L,
        name = if (isInstallment) "Monthly Installment Schedule" else "Monthly Schedule",
        frequency = PayScheduleFrequency.MONTHLY,
        configurationScope = ConfigurationScope.COMPANY,
        country = countryCode,
        startDateReference = LocalDate.of(2023, 1, 1),
        endDateReference = LocalDate.of(2023, 1, 31),
        relativePayDays = 5,
        payDateReferenceType = PayDateReference.PAY_SCHEDULE_END_DATE,
        isInstallment = isInstallment,
        isActive = true,
        createdOn = LocalDateTime.now(),
        createdBy = 1L,
        updatedOn = LocalDateTime.now(),
        updatedBy = 1L,
        label = if (isInstallment) "Monthly Installment Pay Schedule" else "Monthly Pay Schedule",
    )

    @Nested
    inner class InstallmentTypeTests {
        @Test
        fun `should detect overlapping dates for installment types and fail validation`() {
            // Create input with the same contract ID and schema item ID but different dates
            val input = ValidationInputItem(
                id = draftId1,
                fieldKeyValuePairs = listOf(
                    KeyValuePair(
                        key = CommonSkeletonField.CONTRACT_ID.id,
                        value = contractId.toString(),
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.COMPONENT_NAME.id,
                        value = "Base Pay",
                    ),
                ),
            )

            val schemaItemId = UUID.randomUUID()

            // Add two drafts with the same schema item ID and installment type
            val draftsWithOverlap = collector.drafts.toMutableMap()
            draftsWithOverlap[draftId1] = CompensationDraft(
                contractId = contractId,
                companyId = 1L,
                employeeId = "1",
                schemaItemId = schemaItemId,
                schemaCategory = "CONTRACT_BASE_PAY",
                currency = "USD",
                billingRateType = BillingRateType.VALUE,
                billingRate = 1000.0,
                billingFrequency = BillingFrequency.MONTHLY,
                payScheduleId = monthlyInstallmentPayScheduleId,
                startDate = LocalDate.of(2023, 1, 1),
                endDate = null, // End date will be calculated based on installments
                isInstallment = true,
                noOfInstallments = 3, // 3 months installment
                reasonCode = null,
            )

            draftsWithOverlap[draftId2] = CompensationDraft(
                contractId = contractId,
                companyId = 1L,
                employeeId = "1",
                schemaItemId = schemaItemId, // Same schema item ID
                schemaCategory = "CONTRACT_BASE_PAY",
                currency = "USD",
                billingRateType = BillingRateType.VALUE,
                billingRate = 1000.0,
                billingFrequency = BillingFrequency.MONTHLY,
                payScheduleId = monthlyInstallmentPayScheduleId,
                startDate = LocalDate.of(2023, 2, 15), // Overlapping with first installment's calculated end date
                endDate = null, // End date will be calculated based on installments
                isInstallment = true,
                noOfInstallments = 2, // 2 months installment
                reasonCode = null,
            )

            val collectorWithOverlap = collector.copy(drafts = draftsWithOverlap)

            // Run validation
            val result = validator.validate(input, context, collectorWithOverlap)

            // Validation should fail due to overlapping dates
            Assertions.assertFalse(result)

            // Check that error message was added
            val error = collectorWithOverlap.rowValidationResult[draftId1]?.firstOrNull()?.message
            Assertions.assertTrue(error?.contains("Overlapping dates found") == true)
        }

        @Test
        fun `should pass validation when installment types do not have overlapping dates`() {
            // Create input with the same contract ID and schema item ID but different dates
            val input = ValidationInputItem(
                id = draftId1,
                fieldKeyValuePairs = listOf(
                    KeyValuePair(
                        key = CommonSkeletonField.CONTRACT_ID.id,
                        value = contractId.toString(),
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.COMPONENT_NAME.id,
                        value = "Base Pay",
                    ),
                ),
            )

            val schemaItemId = UUID.randomUUID()

            // Add two drafts with the same schema item ID but non-overlapping installment periods
            val draftsWithoutOverlap = collector.drafts.toMutableMap()
            draftsWithoutOverlap[draftId1] = CompensationDraft(
                contractId = contractId,
                companyId = 1L,
                employeeId = "1",
                schemaItemId = schemaItemId,
                schemaCategory = "CONTRACT_BASE_PAY",
                currency = "USD",
                billingRateType = BillingRateType.VALUE,
                billingRate = 1000.0,
                billingFrequency = BillingFrequency.MONTHLY,
                payScheduleId = monthlyInstallmentPayScheduleId,
                startDate = LocalDate.of(2023, 1, 1),
                endDate = null, // End date will be calculated based on installments
                isInstallment = true,
                noOfInstallments = 3, // 3 months installment (Jan, Feb, Mar)
                reasonCode = null,
            )

            draftsWithoutOverlap[draftId2] = CompensationDraft(
                contractId = contractId,
                companyId = 1L,
                employeeId = "1",
                schemaItemId = schemaItemId, // Same schema item ID
                schemaCategory = "CONTRACT_BASE_PAY",
                currency = "USD",
                billingRateType = BillingRateType.VALUE,
                billingRate = 1000.0,
                billingFrequency = BillingFrequency.MONTHLY,
                payScheduleId = monthlyInstallmentPayScheduleId,
                startDate = LocalDate.of(2023, 4, 1), // Starts after first installment ends
                endDate = null, // End date will be calculated based on installments
                isInstallment = true,
                noOfInstallments = 2, // 2 months installment (Apr, May)
                reasonCode = null,
            )

            val collectorWithoutOverlap = collector.copy(drafts = draftsWithoutOverlap)

            // Run validation
            val result = validator.validate(input, context, collectorWithoutOverlap)

            // Validation should pass
            Assertions.assertTrue(result)

            // No error messages should be added
            Assertions.assertTrue(collectorWithoutOverlap.rowValidationResult.isEmpty())
        }

        @Test
        fun `should detect overlapping dates between installment and non-installment types`() {
            // Create input with the same contract ID and schema item ID but different dates
            val input = ValidationInputItem(
                id = draftId1,
                fieldKeyValuePairs = listOf(
                    KeyValuePair(
                        key = CommonSkeletonField.CONTRACT_ID.id,
                        value = contractId.toString(),
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.COMPONENT_NAME.id,
                        value = "Base Pay",
                    ),
                ),
            )

            val schemaItemId = UUID.randomUUID()

            // Add one installment draft and one non-installment draft with overlapping dates
            val draftsWithMixedOverlap = collector.drafts.toMutableMap()
            draftsWithMixedOverlap[draftId1] = CompensationDraft(
                contractId = contractId,
                companyId = 1L,
                employeeId = "1",
                schemaItemId = schemaItemId,
                schemaCategory = "CONTRACT_BASE_PAY",
                currency = "USD",
                billingRateType = BillingRateType.VALUE,
                billingRate = 1000.0,
                billingFrequency = BillingFrequency.MONTHLY,
                payScheduleId = monthlyInstallmentPayScheduleId,
                startDate = LocalDate.of(2023, 1, 1),
                endDate = null, // End date will be calculated based on installments
                isInstallment = true,
                noOfInstallments = 3, // 3 months installment (Jan, Feb, Mar)
                reasonCode = null,
            )

            draftsWithMixedOverlap[draftId2] = CompensationDraft(
                contractId = contractId,
                companyId = 1L,
                employeeId = "1",
                schemaItemId = schemaItemId, // Same schema item ID
                schemaCategory = "CONTRACT_BASE_PAY",
                currency = "USD",
                billingRateType = BillingRateType.VALUE,
                billingRate = 1000.0,
                billingFrequency = BillingFrequency.MONTHLY,
                payScheduleId = monthlyPayScheduleId,
                startDate = LocalDate.of(2023, 3, 15), // Overlaps with the end of the installment period
                endDate = LocalDate.of(2023, 6, 30),
                isInstallment = false,
                noOfInstallments = null,
                reasonCode = null,
            )

            val collectorWithMixedOverlap = collector.copy(drafts = draftsWithMixedOverlap)

            // Run validation
            val result = validator.validate(input, context, collectorWithMixedOverlap)

            // Validation should fail due to overlapping dates
            Assertions.assertFalse(result)

            // Check that error message was added
            val error = collectorWithMixedOverlap.rowValidationResult[draftId1]?.firstOrNull()?.message
            Assertions.assertTrue(error?.contains("Overlapping dates found") == true)
        }
    }
}
