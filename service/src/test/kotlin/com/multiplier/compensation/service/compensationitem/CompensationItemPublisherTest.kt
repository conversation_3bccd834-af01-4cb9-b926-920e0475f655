package com.multiplier.compensation.service.compensationitem

import com.multiplier.common.concurrent.MplExecutor
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemUpdateMessage
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationItemBuilder
import com.multiplier.messaging.api.defer.AsyncJobPublisher
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.concurrent.CountDownLatch
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import kotlin.test.assertTrue

class CompensationItemPublisherTest {
    private lateinit var asyncJobPublisher: AsyncJobPublisher
    private lateinit var compensationItemPublisher: CompensationItemPublisher
    private lateinit var compensationItems: List<CompensationItem>

    @BeforeEach
    fun setUp() {
        asyncJobPublisher = mockk()
        compensationItemPublisher = CompensationItemPublisher(asyncJobPublisher)
        compensationItems = List(3) { TestCompensationItemBuilder().build() }
    }

    @Test
    fun `publish should successfully publish all compensation items`() {
        every { asyncJobPublisher.publish(any<CompensationItemUpdateMessage>()) } just Runs

        compensationItemPublisher.publish(compensationItems)

        verify(exactly = compensationItems.size) { asyncJobPublisher.publish(any<CompensationItemUpdateMessage>()) }
    }

    @Test
    fun `publish should log and count failures when publishing some items fails`() {
        val failingItem = compensationItems[1]

        every { asyncJobPublisher.publish(any<CompensationItemUpdateMessage>()) } just Runs

        every {
            asyncJobPublisher.publish(
                match { message ->
                    message is CompensationItemUpdateMessage && message.compensationItem.id == failingItem.id
                },
            )
        } throws RuntimeException("Publish failed")

        compensationItemPublisher.publish(compensationItems)

        verify(exactly = compensationItems.size) {
            asyncJobPublisher.publish(any<CompensationItemUpdateMessage>())
        }

        verify(exactly = 1) {
            asyncJobPublisher.publish(
                match { message ->
                    message is CompensationItemUpdateMessage && message.compensationItem.id == failingItem.id
                },
            )
        }
    }

    @Test
    fun `publishInBackground should call publish asynchronously and complete`() {
        mockkObject(MplExecutor)
        val testExecutor = Executors.newSingleThreadExecutor()
        val latch = CountDownLatch(1)

        try {
            MplExecutor::class.java.getDeclaredField("globalExecutor").apply {
                isAccessible = true
                set(null, testExecutor)
            }

            every { MplExecutor.runAsync(any(), any<Runnable>()) } answers {
                secondArg<Runnable>().run()
                latch.countDown()
                mockk()
            }

            compensationItemPublisher.publishInBackground(compensationItems)

            assertTrue(latch.await(2, TimeUnit.SECONDS), "Async execution did not complete in time")

            verify { MplExecutor.runAsync(any(), any<Runnable>()) }
            verify(exactly = compensationItems.size) {
                asyncJobPublisher.publish(any<CompensationItemUpdateMessage>())
            }
        } finally {
            testExecutor.shutdown()
            unmockkObject(MplExecutor)
        }
    }
}
