package com.multiplier.compensation.service.compensationitem

import com.multiplier.compensation.database.repository.compensationitem.CompensationItemRepository
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.domain.compensationitem.CompensationItemEnriched
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.common.dto.OperationStatus
import com.multiplier.compensation.service.common.dto.ResultType
import com.multiplier.compensation.service.compensationitem.dto.RevokeCompensationItemRequest
import com.multiplier.compensation.service.compensationitem.dto.RevokeCompensationItemsRequest
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import com.multiplier.transaction.database.jooq.transaction.Transactional
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class CompensationItemRevokeServiceTest {
    private val transactional: Transactional = mockk()
    private val compensationItemRepository: CompensationItemRepository = mockk()
    private lateinit var service: CompensationItemRevokeService

    private val itemId = UUID.randomUUID()

    @BeforeEach
    fun setUp() {
        service = CompensationItemRevokeService(
            compensationItemRepository,
            transactional,
        )
    }

    @Test
    fun `should successfully revoke compensation item`() {
        // Given
        val revokeRequest = RevokeCompensationItemRequest(
            compensationItemId = sampleItem.id,
        )
        val request = RevokeCompensationItemsRequest(
            validRequests = listOf(revokeRequest),
            inaccessibleRequests = emptyList(),
        )

        // Mock transactional.invoke to return RevokeCompensationItemResponse
        every {
            transactional.invoke(any<TransactionContext.() -> Any>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> Any>()
            lambda.invoke(mockk<TransactionContext>())
        }

        // Mock repository calls
        every { compensationItemRepository.findById(sampleItem.id, any()) } returns sampleItem
        every {
            compensationItemRepository.getEnrichedCompensationItems(
                match { filter ->
                    filter.ids.contains(sampleItem.id)
                },
                any(),
            )
        } returns listOf(sampleItemEnriched)
        every { compensationItemRepository.update(any(), any()) } just runs

        // When
        val response = service.revoke(request)

        // Then
        assertEquals(OperationStatus.SUCCESS, response.status)
        assertEquals(1, response.results.size)
        assertEquals(ResultType.INFO, response.results[0].resultType)
        assertEquals("Item Revoked Successfully", response.results[0].message)

        // Verify that update was called with REVOKED status
        verify {
            compensationItemRepository.update(
                match { item ->
                    item.id == sampleItem.id &&
                        item.status == CompensationItemStatus.REVOKED
                },
                any(),
            )
        }
    }

    @Test
    fun `should fail when compensation item not found`() {
        // Given
        val itemId = UUID.randomUUID()
        val revokeRequest = RevokeCompensationItemRequest(
            compensationItemId = itemId,
        )
        val request = RevokeCompensationItemsRequest(
            validRequests = listOf(revokeRequest),
            inaccessibleRequests = emptyList(),
        )

        // Mock transactional.invoke
        every {
            transactional.invoke(any<TransactionContext.() -> Any>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> Any>()
            lambda.invoke(mockk<TransactionContext>())
        }

        // Mock repository calls
        every { compensationItemRepository.findById(itemId, any()) } returns null
        every {
            compensationItemRepository.getEnrichedCompensationItems(
                match { filter ->
                    filter.ids.contains(itemId)
                },
                any(),
            )
        } returns emptyList()

        // When
        val response = service.revoke(request)

        // Then
        assertEquals(OperationStatus.FAILURE, response.status)
        assertEquals(1, response.results.size)
        assertEquals(ResultType.ERROR, response.results[0].resultType)
        assertEquals("Compensation item not found", response.results[0].message)
    }

    @Test
    fun `should fail when item has invalid category`() {
        // Given
        val invalidCategoryItem = sampleItem.copy(category = "INVALID_CATEGORY")
        val invalidItemEnriched = sampleItemEnriched.copy(category = "INVALID_CATEGORY")
        val revokeRequest = RevokeCompensationItemRequest(
            compensationItemId = invalidCategoryItem.id,
        )
        val request = RevokeCompensationItemsRequest(
            validRequests = listOf(revokeRequest),
            inaccessibleRequests = emptyList(),
        )

        // Mock transactional.invoke
        every {
            transactional.invoke(any<TransactionContext.() -> Any>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> Any>()
            lambda.invoke(mockk<TransactionContext>())
        }

        // Mock repository calls
        every { compensationItemRepository.findById(invalidCategoryItem.id, any()) } returns invalidCategoryItem
        every {
            compensationItemRepository.getEnrichedCompensationItems(
                match { filter ->
                    filter.ids.contains(invalidCategoryItem.id)
                },
                any(),
            )
        } returns listOf(invalidItemEnriched)

        // When
        val response = service.revoke(request)

        // Then
        assertEquals(OperationStatus.FAILURE, response.status)
        assertEquals(1, response.results.size)
        assertEquals(ResultType.ERROR, response.results[0].resultType)
        assertEquals("Revocation not supported for category [INVALID_CATEGORY]", response.results[0].message)
    }

    @Test
    fun `should fail when item is not eligible for revoke`() {
        // Given
        val nonDeletableItemEnriched = sampleItemEnriched.copy(isDeletable = false)
        val revokeRequest = RevokeCompensationItemRequest(
            compensationItemId = sampleItem.id,
        )
        val request = RevokeCompensationItemsRequest(
            validRequests = listOf(revokeRequest),
            inaccessibleRequests = emptyList(),
        )

        // Mock transactional.invoke
        every {
            transactional.invoke(any<TransactionContext.() -> Any>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> Any>()
            lambda.invoke(mockk<TransactionContext>())
        }

        // Mock repository calls
        every { compensationItemRepository.findById(sampleItem.id, any()) } returns sampleItem
        every {
            compensationItemRepository.getEnrichedCompensationItems(
                match { filter ->
                    filter.ids.contains(sampleItem.id)
                },
                any(),
            )
        } returns listOf(nonDeletableItemEnriched)

        // When
        val response = service.revoke(request)

        // Then
        assertEquals(OperationStatus.FAILURE, response.status)
        assertEquals(1, response.results.size)
        assertEquals(ResultType.ERROR, response.results[0].resultType)
        assertEquals("Item is not eligible for revocation", response.results[0].message)
    }

    @Test
    fun `should handle multiple items with mixed results`() {
        // Given
        val validItem = sampleItem
        val validItemEnriched = sampleItemEnriched

        val invalidItem = sampleItem.copy(
            id = UUID.randomUUID(),
            category = "INVALID_CATEGORY",
        )
        val invalidItemEnriched = sampleItemEnriched.copy(
            id = invalidItem.id,
            category = "INVALID_CATEGORY",
        )

        val inaccessibleItemId = UUID.randomUUID()

        val request = RevokeCompensationItemsRequest(
            validRequests = listOf(
                RevokeCompensationItemRequest(validItem.id),
                RevokeCompensationItemRequest(invalidItem.id),
            ),
            inaccessibleRequests = listOf(
                RevokeCompensationItemRequest(inaccessibleItemId, "Access denied"),
            ),
        )

        // Mock transactional.invoke
        every {
            transactional.invoke(any<TransactionContext.() -> Any>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> Any>()
            lambda.invoke(mockk<TransactionContext>())
        }

        // Mock repository calls for valid item
        every { compensationItemRepository.findById(validItem.id, any()) } returns validItem
        every {
            compensationItemRepository.getEnrichedCompensationItems(
                match { filter ->
                    filter.ids.contains(validItem.id)
                },
                any(),
            )
        } returns listOf(validItemEnriched)
        every { compensationItemRepository.update(any(), any()) } just runs

        // Mock repository calls for invalid item
        every { compensationItemRepository.findById(invalidItem.id, any()) } returns invalidItem
        every {
            compensationItemRepository.getEnrichedCompensationItems(
                match { filter ->
                    filter.ids.contains(invalidItem.id)
                },
                any(),
            )
        } returns listOf(invalidItemEnriched)

        // When
        val response = service.revoke(request)

        // Then
        assertEquals(OperationStatus.PARTIAL_SUCCESS, response.status)
        assertEquals(3, response.results.size)

        // Check that we have one success and two failures
        val successResults = response.results.filter { it.resultType == ResultType.INFO }
        val errorResults = response.results.filter { it.resultType == ResultType.ERROR }

        assertEquals(1, successResults.size)
        assertEquals(2, errorResults.size)

        // Verify the success result
        val successResult = successResults.first()
        assertEquals(validItem.id, successResult.compensationItemId)
        assertEquals("Item Revoked Successfully", successResult.message)

        // Verify that one error is for the terminated item
        assertTrue(
            errorResults.any {
                it.compensationItemId == invalidItem.id &&
                    it.message == "Revocation not supported for category [INVALID_CATEGORY]"
            },
        )

        // Verify that one error is for the inaccessible item
        assertTrue(errorResults.any { it.compensationItemId == inaccessibleItemId && it.message == "Access denied" })

        // Verify that update was called only for the valid item
        verify(exactly = 1) {
            compensationItemRepository.update(
                match { item ->
                    item.id == validItem.id &&
                        item.status == CompensationItemStatus.REVOKED
                },
                any(),
            )
        }
    }

    @Test
    fun `should support all valid categories`() {
        // Test each valid category
        val validCategories = listOf(
            CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE,
            CategoryConstants.CATEGORY_PAY_SUPPLEMENT,
            CategoryConstants.CATEGORY_EMPLOYEE_DEDUCTION,
        )

        for (category in validCategories) {
            // Given
            val item = sampleItem.copy(category = category)
            val itemEnriched = sampleItemEnriched.copy(category = category)
            val revokeRequest = RevokeCompensationItemRequest(item.id)
            val request = RevokeCompensationItemsRequest(
                validRequests = listOf(revokeRequest),
                inaccessibleRequests = emptyList(),
            )

            // Mock transactional.invoke
            every {
                transactional.invoke(any<TransactionContext.() -> Any>())
            } answers {
                val lambda = firstArg<TransactionContext.() -> Any>()
                lambda.invoke(mockk<TransactionContext>())
            }

            // Mock repository calls
            every { compensationItemRepository.findById(item.id, any()) } returns item
            every {
                compensationItemRepository.getEnrichedCompensationItems(
                    match { filter ->
                        filter.ids.contains(item.id)
                    },
                    any(),
                )
            } returns listOf(itemEnriched)
            every { compensationItemRepository.update(any(), any()) } just runs

            // When
            val response = service.revoke(request)

            // Then
            assertEquals(OperationStatus.SUCCESS, response.status)
            assertEquals(1, response.results.size)
            assertEquals(ResultType.INFO, response.results[0].resultType)
            assertEquals("Item Revoked Successfully", response.results[0].message)
        }
    }

    @Test
    fun `should successfully revoke multiple valid compensation items`() {
        // Given
        val item1 = sampleItem
        val item1Enriched = sampleItemEnriched

        val item2 = sampleItem.copy(
            id = UUID.randomUUID(),
            compensationId = UUID.randomUUID(),
        )
        val item2Enriched = sampleItemEnriched.copy(
            id = item2.id,
        )

        val item3 = sampleItem.copy(
            id = UUID.randomUUID(),
            compensationId = UUID.randomUUID(),
            category = CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE,
        )
        val item3Enriched = sampleItemEnriched.copy(
            id = item3.id,
            category = CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE,
        )

        val request = RevokeCompensationItemsRequest(
            validRequests = listOf(
                RevokeCompensationItemRequest(item1.id),
                RevokeCompensationItemRequest(item2.id),
                RevokeCompensationItemRequest(item3.id),
            ),
            inaccessibleRequests = emptyList(),
        )

        // Mock transactional.invoke
        every {
            transactional.invoke(any<TransactionContext.() -> Any>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> Any>()
            lambda.invoke(mockk<TransactionContext>())
        }

        // Mock repository calls for all items
        every { compensationItemRepository.findById(item1.id, any()) } returns item1
        every {
            compensationItemRepository.getEnrichedCompensationItems(
                match { filter ->
                    filter.ids.contains(item1.id)
                },
                any(),
            )
        } returns listOf(item1Enriched)

        every { compensationItemRepository.findById(item2.id, any()) } returns item2
        every {
            compensationItemRepository.getEnrichedCompensationItems(
                match { filter ->
                    filter.ids.contains(item2.id)
                },
                any(),
            )
        } returns listOf(item2Enriched)

        every { compensationItemRepository.findById(item3.id, any()) } returns item3
        every {
            compensationItemRepository.getEnrichedCompensationItems(
                match { filter ->
                    filter.ids.contains(item3.id)
                },
                any(),
            )
        } returns listOf(item3Enriched)

        every { compensationItemRepository.update(any(), any()) } just runs

        // When
        val response = service.revoke(request)

        // Then
        assertEquals(OperationStatus.SUCCESS, response.status)
        assertEquals(3, response.results.size)

        // All results should be successful
        response.results.forEach { result ->
            assertEquals(ResultType.INFO, result.resultType)
            assertEquals("Item Revoked Successfully", result.message)
        }

        // Verify that update was called for each item
        verify(exactly = 1) {
            compensationItemRepository.update(
                match { item -> item.id == item1.id && item.status == CompensationItemStatus.REVOKED },
                any(),
            )
        }

        verify(exactly = 1) {
            compensationItemRepository.update(
                match { item -> item.id == item2.id && item.status == CompensationItemStatus.REVOKED },
                any(),
            )
        }

        verify(exactly = 1) {
            compensationItemRepository.update(
                match { item -> item.id == item3.id && item.status == CompensationItemStatus.REVOKED },
                any(),
            )
        }
    }

    @Test
    fun `should handle batch with all invalid items`() {
        // Given
        val item1 = sampleItem.copy(
            id = UUID.randomUUID(),
            category = "INVALID_CATEGORY_1",
        )
        val item1Enriched = sampleItemEnriched.copy(
            id = item1.id,
            category = "INVALID_CATEGORY_1",
        )

        val item2 = sampleItem.copy(
            id = UUID.randomUUID(),
            category = "INVALID_CATEGORY_2",
        )
        val item2Enriched = sampleItemEnriched.copy(
            id = item2.id,
            category = "INVALID_CATEGORY_2",
        )

        val item3 = sampleItem.copy(
            id = UUID.randomUUID(),
        )
        val item3Enriched = sampleItemEnriched.copy(
            id = item3.id,
            isDeletable = false,
        )

        val request = RevokeCompensationItemsRequest(
            validRequests = listOf(
                RevokeCompensationItemRequest(item1.id),
                RevokeCompensationItemRequest(item2.id),
                RevokeCompensationItemRequest(item3.id),
            ),
            inaccessibleRequests = emptyList(),
        )

        // Mock transactional.invoke
        every {
            transactional.invoke(any<TransactionContext.() -> Any>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> Any>()
            lambda.invoke(mockk<TransactionContext>())
        }

        // Mock repository calls for all items
        every { compensationItemRepository.findById(item1.id, any()) } returns item1
        every {
            compensationItemRepository.getEnrichedCompensationItems(
                match { filter ->
                    filter.ids.contains(item1.id)
                },
                any(),
            )
        } returns listOf(item1Enriched)

        every { compensationItemRepository.findById(item2.id, any()) } returns item2
        every {
            compensationItemRepository.getEnrichedCompensationItems(
                match { filter ->
                    filter.ids.contains(item2.id)
                },
                any(),
            )
        } returns listOf(item2Enriched)

        every { compensationItemRepository.findById(item3.id, any()) } returns item3
        every {
            compensationItemRepository.getEnrichedCompensationItems(
                match { filter ->
                    filter.ids.contains(item3.id)
                },
                any(),
            )
        } returns listOf(item3Enriched)

        // When
        val response = service.revoke(request)

        // Then
        assertEquals(OperationStatus.FAILURE, response.status)
        assertEquals(3, response.results.size)

        // All results should be errors
        response.results.forEach { result ->
            assertEquals(ResultType.ERROR, result.resultType)
        }

        // Verify specific error messages
        assertTrue(
            response.results.any {
                it.compensationItemId == item1.id &&
                    it.message == "Revocation not supported for category [INVALID_CATEGORY_1]"
            },
        )
        assertTrue(
            response.results.any {
                it.compensationItemId == item2.id &&
                    it.message == "Revocation not supported for category [INVALID_CATEGORY_2]"
            },
        )
        assertTrue(
            response.results.any {
                it.compensationItemId == item3.id &&
                    it.message == "Item is not eligible for revocation"
            },
        )

        // Verify that update was never called
        verify(exactly = 0) {
            compensationItemRepository.update(any(), any())
        }
    }

    private val sampleItem = CompensationItem(
        id = itemId,
        companyId = 1L,
        entityId = 2L,
        contractId = 3L,
        compensationId = UUID.randomUUID(),
        schemaItemId = UUID.randomUUID(),
        category = CategoryConstants.CATEGORY_PAY_SUPPLEMENT,
        currency = "USD",
        billingRateType = BillingRateType.VALUE,
        billingRate = 5000.0,
        billingFrequency = BillingFrequency.MONTHLY,
        payScheduleId = UUID.randomUUID(),
        compensationStartDate = LocalDate.now(),
        compensationEndDate = LocalDate.now().plusMonths(12),
        compensationStatus = CompensationStatus.NEW,
        startDate = LocalDate.now(),
        endDate = LocalDate.now().plusMonths(12),
        isInstallment = false,
        noOfInstallments = null,
        currentInstallment = null,
        cutOffDate = null,
        expectedPayDate = LocalDate.now().plusMonths(1),
        calculatedAmount = 5000.0,
        payDate = null,
        previousId = null,
        status = CompensationItemStatus.NEW,
        isArrear = false,
        arrearOf = null,
        arrearTriggerReference = null,
        updateTriggerReference = null,
        createdOn = LocalDateTime.now(),
        createdBy = 1L,
        updatedOn = LocalDateTime.now(),
        updatedBy = 1L,
    )

    private val sampleItemEnriched = CompensationItemEnriched(
        id = itemId,
        companyId = 1L,
        entityId = 2L,
        contractId = 3L,
        category = CategoryConstants.CATEGORY_PAY_SUPPLEMENT,
        schemaComponentName = "Bonus",
        currency = "USD",
        billingRateType = BillingRateType.VALUE,
        billingRate = 5000.0,
        billingFrequency = BillingFrequency.MONTHLY,
        payScheduleName = "Monthly",
        payScheduleFrequency = PayScheduleFrequency.MONTHLY,
        startDate = LocalDate.now(),
        endDate = LocalDate.now().plusMonths(12),
        isInstallment = false,
        noOfInstallments = null,
        currentInstallment = null,
        isTaxable = true,
        isFixed = true,
        isProrated = false,
        isMandatory = false,
        isPartOfBasePay = false,
        calculatedAmount = 5000.0,
        cutOffDate = null,
        expectedPayDate = LocalDate.now().plusMonths(1),
        payDate = null,
        previousId = null,
        status = CompensationItemStatus.NEW,
        isArrear = false,
        arrearOf = null,
        arrearTriggerReference = null,
        updateTriggerReference = null,
        createdOn = LocalDateTime.now(),
        createdBy = 1L,
        updatedOn = LocalDateTime.now(),
        updatedBy = 1L,
        isDeletable = true,
        label = "Bonus",
        isOvertimeEligible = false,
        isPartOfCtc = true,
    )
}
