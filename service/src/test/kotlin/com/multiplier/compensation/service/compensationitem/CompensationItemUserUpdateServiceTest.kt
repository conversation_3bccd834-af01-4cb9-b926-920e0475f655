package com.multiplier.compensation.service.compensationitem

import com.multiplier.compensation.database.repository.compensationitem.CompensationItemRepository
import com.multiplier.compensation.database.repository.compensationschema.CompensationSchemaItemRepository
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import com.multiplier.compensation.service.common.dto.ResultType
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemUserUpdateRequest
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemUserUpdateResponse
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemsUserUpdateRequest
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationItemBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationSchemaItemBuilder
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import com.multiplier.transaction.database.jooq.transaction.Transactional
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate

class CompensationItemUserUpdateServiceTest {
    private val transactional: Transactional = mockk()
    private lateinit var service: CompensationItemUserUpdateService
    private val compensationItemRepository = mockk<CompensationItemRepository>()
    private val compensationSchemaItemRepository = mockk<CompensationSchemaItemRepository>()

    private val sampleItem = TestCompensationItemBuilder()
        .withStatus(CompensationItemStatus.NEW)
        .withCutOffDate(LocalDate.now().plusDays(1))
        .build()

    private val sampleSchemaItem = TestCompensationSchemaItemBuilder()
        .withIsFixed(false)
        .build()

    @BeforeEach
    fun setUp() {
        service = CompensationItemUserUpdateService(
            compensationItemRepository,
            compensationSchemaItemRepository,
            transactional,
        )
    }

    @Test
    fun `should successfully update compensation item`() {
        val updateRequest = CompensationItemUserUpdateRequest(
            compensationItemId = sampleItem.id,
            billingRate = 100.0,
        )
        val request = CompensationItemsUserUpdateRequest(
            validRequests = listOf(updateRequest),
            invalidRequests = emptyList(),
            inaccessibleRequest = emptyList(),
        )

        every {
            transactional.invoke(any<TransactionContext.() -> CompensationItemUserUpdateResponse>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> CompensationItemUserUpdateResponse>()
            lambda.invoke(mockk<TransactionContext>())
        }

        every { compensationItemRepository.findById(sampleItem.id, any()) } returns sampleItem
        every { compensationSchemaItemRepository.findBySchemaItemId(sampleItem.schemaItemId) } returns sampleSchemaItem
        every { compensationItemRepository.update(any(), any()) } just Runs

        val response = service.update(request)

        assertEquals(1, response.results.size)
        assertEquals(ResultType.INFO, response.results.first().resultType)
    }

    @Test
    fun `should successfully update compensation item for DRAFT`() {
        val eligibleItem = sampleItem.copy(status = CompensationItemStatus.DRAFT)
        val updateRequest = CompensationItemUserUpdateRequest(
            compensationItemId = eligibleItem.id,
            billingRate = 100.0,
        )
        val request = CompensationItemsUserUpdateRequest(
            validRequests = listOf(updateRequest),
            invalidRequests = emptyList(),
            inaccessibleRequest = emptyList(),
        )

        every {
            transactional.invoke(any<TransactionContext.() -> CompensationItemUserUpdateResponse>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> CompensationItemUserUpdateResponse>()
            lambda.invoke(mockk<TransactionContext>())
        }

        every { compensationItemRepository.findById(eligibleItem.id, any()) } returns eligibleItem
        every { compensationSchemaItemRepository.findBySchemaItemId(eligibleItem.schemaItemId) } returns
            sampleSchemaItem
        every { compensationItemRepository.update(any(), any()) } just Runs

        val response = service.update(request)

        assertEquals(1, response.results.size)
        assertEquals(ResultType.INFO, response.results.first().resultType)
    }

    @Test
    fun `should successfully update compensation item when cutoff date is today`() {
        val eligibleItem = sampleItem.copy(cutOffDate = LocalDate.now(), status = CompensationItemStatus.DRAFT)
        val updateRequest = CompensationItemUserUpdateRequest(
            compensationItemId = eligibleItem.id,
            billingRate = 100.0,
        )
        val request = CompensationItemsUserUpdateRequest(
            validRequests = listOf(updateRequest),
            invalidRequests = emptyList(),
            inaccessibleRequest = emptyList(),
        )

        every {
            transactional.invoke(any<TransactionContext.() -> CompensationItemUserUpdateResponse>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> CompensationItemUserUpdateResponse>()
            lambda.invoke(mockk<TransactionContext>())
        }

        every { compensationItemRepository.findById(eligibleItem.id, any()) } returns eligibleItem
        every { compensationSchemaItemRepository.findBySchemaItemId(eligibleItem.schemaItemId) } returns
            sampleSchemaItem
        every { compensationItemRepository.update(any(), any()) } just Runs

        val response = service.update(request)

        assertEquals(1, response.results.size)
        assertEquals(ResultType.INFO, response.results.first().resultType)
    }

    @Test
    fun `should successfully update compensation item when cutoff date is null`() {
        val eligibleItem = sampleItem.copy(cutOffDate = null, status = CompensationItemStatus.DRAFT)
        val updateRequest = CompensationItemUserUpdateRequest(
            compensationItemId = eligibleItem.id,
            billingRate = 100.0,
        )
        val request = CompensationItemsUserUpdateRequest(
            validRequests = listOf(updateRequest),
            invalidRequests = emptyList(),
            inaccessibleRequest = emptyList(),
        )

        every {
            transactional.invoke(any<TransactionContext.() -> CompensationItemUserUpdateResponse>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> CompensationItemUserUpdateResponse>()
            lambda.invoke(mockk<TransactionContext>())
        }

        every { compensationItemRepository.findById(eligibleItem.id, any()) } returns eligibleItem
        every { compensationSchemaItemRepository.findBySchemaItemId(eligibleItem.schemaItemId) } returns
            sampleSchemaItem
        every { compensationItemRepository.update(any(), any()) } just Runs

        val response = service.update(request)

        assertEquals(1, response.results.size)
        assertEquals(ResultType.INFO, response.results.first().resultType)
    }

    @Test
    fun `should fail when compensation item not found`() {
        val updateRequest = CompensationItemUserUpdateRequest(sampleItem.id, 100.0)
        val request = CompensationItemsUserUpdateRequest(emptyList(), emptyList(), listOf(updateRequest))

        every {
            transactional.invoke(any<TransactionContext.() -> CompensationItemUserUpdateResponse>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> CompensationItemUserUpdateResponse>()
            lambda.invoke(mockk<TransactionContext>())
        }

        every { compensationItemRepository.findById(sampleItem.id, any()) } returns null

        val response = service.update(request)

        assertEquals(1, response.results.size)
        assertEquals(ResultType.ERROR, response.results.first().resultType)
        assertEquals("Access denied", response.results.first().message)
    }

    @Test
    fun `should fail when schema item not found`() {
        val updateRequest = CompensationItemUserUpdateRequest(sampleItem.id, 100.0)
        val request = CompensationItemsUserUpdateRequest(listOf(updateRequest), emptyList(), emptyList())

        // Mock transactional.invoke to return CompensationItemUserUpdateResponse
        every {
            transactional.invoke(any<TransactionContext.() -> CompensationItemUserUpdateResponse>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> CompensationItemUserUpdateResponse>()
            lambda.invoke(mockk())
        }

        every { compensationItemRepository.findById(sampleItem.id, any()) } returns sampleItem
        every { compensationSchemaItemRepository.findBySchemaItemId(sampleItem.schemaItemId) } returns null

        val response = service.update(request)

        assertEquals(1, response.results.size)
        assertEquals(ResultType.ERROR, response.results.first().resultType)
        assertEquals("Compensation schema item not found", response.results.first().message)
    }

    @Test
    fun `should fail when schema item is fixed`() {
        val fixedSchemaItem = sampleSchemaItem.copy(isFixed = true)
        val updateRequest = CompensationItemUserUpdateRequest(sampleItem.id, 100.0)
        val request = CompensationItemsUserUpdateRequest(listOf(updateRequest), emptyList(), emptyList())

        // Mock transactional.invoke to return CompensationItemUserUpdateResponse
        every {
            transactional.invoke(any<TransactionContext.() -> CompensationItemUserUpdateResponse>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> CompensationItemUserUpdateResponse>()
            lambda.invoke(mockk())
        }

        every { compensationItemRepository.findById(sampleItem.id, any()) } returns sampleItem
        every { compensationSchemaItemRepository.findBySchemaItemId(sampleItem.schemaItemId) } returns fixedSchemaItem

        val response = service.update(request)

        assertEquals(1, response.results.size)
        assertEquals(ResultType.ERROR, response.results.first().resultType)
        assertEquals("Item not eligible for update.", response.results.first().message)
    }

    @Test
    fun `should fail when item status is not NEW or DRAFT`() {
        val item = sampleItem.copy(status = CompensationItemStatus.ABORTED)
        val updateRequest = CompensationItemUserUpdateRequest(item.id, 100.0)
        val request = CompensationItemsUserUpdateRequest(listOf(updateRequest), emptyList(), emptyList())

        // Mock transactional.invoke to return CompensationItemUserUpdateResponse
        every {
            transactional.invoke(any<TransactionContext.() -> CompensationItemUserUpdateResponse>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> CompensationItemUserUpdateResponse>()
            lambda.invoke(mockk())
        }

        every { compensationItemRepository.findById(item.id, any()) } returns item
        every { compensationSchemaItemRepository.findBySchemaItemId(item.schemaItemId) } returns sampleSchemaItem

        val response = service.update(request)

        assertEquals(1, response.results.size)
        assertEquals(ResultType.ERROR, response.results.first().resultType)
        assertEquals("Item not eligible for update.", response.results.first().message)
    }

    @Test
    fun `should fail when cutoff date is not eligible`() {
        val ineligibleItem = sampleItem.copy(cutOffDate = LocalDate.now().minusDays(1))
        val updateRequest = CompensationItemUserUpdateRequest(ineligibleItem.id, 100.0)
        val request = CompensationItemsUserUpdateRequest(listOf(updateRequest), emptyList(), emptyList())

        // Mock transactional.invoke to return CompensationItemUserUpdateResponse
        every {
            transactional.invoke(any<TransactionContext.() -> CompensationItemUserUpdateResponse>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> CompensationItemUserUpdateResponse>()
            lambda.invoke(mockk())
        }

        every { compensationItemRepository.findById(ineligibleItem.id, any()) } returns ineligibleItem
        every { compensationSchemaItemRepository.findBySchemaItemId(ineligibleItem.schemaItemId) } returns
            sampleSchemaItem

        val response = service.update(request)

        assertEquals(1, response.results.size)
        assertEquals(ResultType.ERROR, response.results.first().resultType)
        assertEquals("Item not eligible for update.", response.results.first().message)
    }

    @Test
    fun `should fail when billing rate is not positive`() {
        val updateRequest = CompensationItemUserUpdateRequest(sampleItem.id, 0.0)
        val request = CompensationItemsUserUpdateRequest(listOf(updateRequest), emptyList(), emptyList())

        // Mock transactional.invoke to return CompensationItemUserUpdateResponse
        every {
            transactional.invoke(any<TransactionContext.() -> CompensationItemUserUpdateResponse>())
        } answers {
            val lambda = firstArg<TransactionContext.() -> CompensationItemUserUpdateResponse>()
            lambda.invoke(mockk())
        }

        every { compensationItemRepository.findById(sampleItem.id, any()) } returns sampleItem
        every { compensationSchemaItemRepository.findBySchemaItemId(sampleItem.schemaItemId) } returns sampleSchemaItem

        val response = service.update(request)

        assertEquals(1, response.results.size)
        assertEquals(ResultType.ERROR, response.results.first().resultType)
        assertEquals("Billing rate must be positive", response.results.first().message)
    }
}
