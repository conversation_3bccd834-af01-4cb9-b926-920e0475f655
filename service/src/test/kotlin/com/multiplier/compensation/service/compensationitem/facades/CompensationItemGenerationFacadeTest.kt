package com.multiplier.compensation.service.compensationitem.facades

import com.multiplier.common.exception.MplSystemException
import com.multiplier.compensation.database.repository.compensation.CompensationRepository
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensationitem.GenerateCompensationItemsForCompensationsRequest
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemGenerationRequest
import com.multiplier.compensation.service.compensationitem.generation.pipeline.CompensationItemGenerationPipelineProvider
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertFailsWith

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CompensationItemGenerationFacadeTest {
    private lateinit var pipelineProvider: CompensationItemGenerationPipelineProvider
    private lateinit var compensationRepository: CompensationRepository
    private lateinit var facade: CompensationItemGenerationFacade

    private val generationPeriodInDays = 30L
    private val batchSize = 100

    private val jobId = UUID.randomUUID()
    private val defaultRequest = CompensationItemGenerationRequest(
        toDate = LocalDate.now().plusDays(generationPeriodInDays),
        shouldPersist = true,
    )

    @BeforeEach
    fun setUp() {
        pipelineProvider = mockk()
        compensationRepository = mockk()
        facade = CompensationItemGenerationFacade(pipelineProvider, compensationRepository, generationPeriodInDays)
    }

    @ParameterizedTest(name = "{index} => Process {1} batches with scheduled request")
    @MethodSource("provideScheduledRequestTestCases")
    fun `test generateCompensationItems for scheduled requests`(
        compensationsPerBatch: List<List<Compensation>>,
        expectedBatchCount: Int,
    ) {
        mockFetchAllEligibleForItemGeneration(compensationsPerBatch)
        coJustRun { pipelineProvider.getNewInstance(any()).execute() }

        // when
        runBlocking {
            facade.generateCompensationItems(defaultRequest, jobId)
        }

        // then
        coVerify(exactly = expectedBatchCount) { pipelineProvider.getNewInstance(any()).execute() }
    }

    @Test
    fun `test generateCompensationItems skips empty compensationIds`() = runBlocking {
        val request = GenerateCompensationItemsForCompensationsRequest(
            compensationIds = emptyList(),
            shouldPersist = true,
        )

        // when
        facade.generateCompensationItems(request)

        // then
        coVerify(exactly = 0) { compensationRepository.findForIds(any()) }
        coVerify(exactly = 0) { pipelineProvider.getNewInstance(any()) }
    }

    @Test
    fun `test generateCompensationItems handles no compensations from findForIds`() = runBlocking {
        val request = GenerateCompensationItemsForCompensationsRequest(
            compensationIds = listOf(UUID.randomUUID()),
            shouldPersist = true,
        )

        coEvery { compensationRepository.findForIds(any()) } returns emptyList()

        // when
        facade.generateCompensationItems(request)

        // then
        coVerify { compensationRepository.findForIds(any()) }
        coVerify(exactly = 0) { pipelineProvider.getNewInstance(any()) }
    }

    @Test
    fun `test generateCompensationItems handles invalid compensationIds`() = runBlocking {
        val invalidId = UUID.randomUUID()
        val request = GenerateCompensationItemsForCompensationsRequest(
            compensationIds = listOf(invalidId),
            shouldPersist = true,
        )

        coEvery { compensationRepository.findForIds(listOf(invalidId)) } returns emptyList()

        // when
        facade.generateCompensationItems(request)

        // then
        coVerify { compensationRepository.findForIds(listOf(invalidId)) }
        coVerify(exactly = 0) { pipelineProvider.getNewInstance(any()) }
    }

    @ParameterizedTest(name = "{index} => Process {1} batches with batch processing")
    @MethodSource("provideBatchProcessingTestCases")
    fun `test processCompensationBatch with multiple batches`(
        compensationsPerBatch: List<List<Compensation>>,
        expectedBatchCount: Int,
    ) {
        mockFetchAllEligibleForItemGeneration(compensationsPerBatch)
        coJustRun { pipelineProvider.getNewInstance(any()).execute() }

        // when
        runBlocking {
            facade.generateCompensationItems(defaultRequest, jobId)
        }

        // then
        coVerify(exactly = expectedBatchCount) { pipelineProvider.getNewInstance(any()).execute() }
    }

    @Test
    fun `test processCompensationBatch handles timeout exception`() = runBlocking {
        mockFetchAllEligibleForItemGeneration(
            listOf(listOf(mockk { every { id } returns UUID.randomUUID() })),
        )

        every { pipelineProvider.getNewInstance(any()) } returns mockk {
            coEvery { execute() } coAnswers {
                withTimeout(1000) {
                    delay(2000)
                }
            }
        }

        // when
        val exception = assertFailsWith<MplSystemException> {
            facade.generateCompensationItems(defaultRequest, jobId)
        }

        // then
        assertEquals("Timed out waiting for 1000 ms", exception.message)
    }

    @Test
    fun `test processCompensationBatch handles generic exceptions`() = runBlocking {
        mockFetchAllEligibleForItemGeneration(
            listOf(
                listOf(mockk { every { id } returns UUID.randomUUID() }),
                emptyList(),
            ),
        )

        every { pipelineProvider.getNewInstance(any()) } returns mockk {
            coEvery { execute() } throws RuntimeException("Unexpected error")
        }

        var capturedException: Throwable? = null
        facade.exceptionHandler = { capturedException = it }

        // when
        facade.generateCompensationItems(defaultRequest, jobId)

        // then
        assertNotNull(capturedException, "Exception should be captured by the exceptionHandler")
        assertTrue(capturedException is RuntimeException, "Captured exception should be a RuntimeException")
        assertEquals("Unexpected error", capturedException?.message)
    }

    fun provideScheduledRequestTestCases(): List<Arguments> = listOf(
        Arguments.of(
            listOf(
                listOf(
                    mockk<Compensation> { every { id } returns UUID.randomUUID() },
                    mockk<Compensation> { every { id } returns UUID.randomUUID() },
                ),
                listOf(
                    mockk<Compensation> { every { id } returns UUID.randomUUID() },
                ),
                emptyList(),
            ),
            2,
        ),
        Arguments.of(
            listOf(emptyList<Compensation>()),
            0,
        ),
    )

    fun provideBatchProcessingTestCases(): List<Arguments> = listOf(
        Arguments.of(
            listOf(
                List(100) { mockk<Compensation> { every { id } returns UUID.randomUUID() } },
                List(50) { mockk<Compensation> { every { id } returns UUID.randomUUID() } },
                emptyList(),
            ),
            2,
        ),
        Arguments.of(
            listOf(
                List(100) { mockk<Compensation> { every { id } returns UUID.randomUUID() } },
                emptyList(),
            ),
            1,
        ),
    )

    private fun mockFetchAllEligibleForItemGeneration(batches: List<List<Compensation>>) {
        coEvery {
            compensationRepository.fetchAllEligibleForItemGeneration(batchSize, any())
        } returnsMany batches
    }
}
