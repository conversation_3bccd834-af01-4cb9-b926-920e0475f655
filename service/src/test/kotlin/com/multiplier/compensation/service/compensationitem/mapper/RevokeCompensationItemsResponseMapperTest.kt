package com.multiplier.compensation.service.compensationitem.mapper

import com.multiplier.compensation.service.common.dto.OperationStatus
import com.multiplier.compensation.service.common.dto.ResultType
import com.multiplier.compensation.service.compensationitem.dto.RevokeCompensationItemRequest
import com.multiplier.compensation.service.compensationitem.dto.RevokeCompensationItemResponse
import com.multiplier.compensation.service.compensationitem.dto.RevokeCompensationItemsRequest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.util.UUID

class RevokeCompensationItemsResponseMapperTest {
    @Test
    fun `toFailure should create error response with provided message`() {
        // Given
        val itemId = UUID.randomUUID()
        val request = RevokeCompensationItemRequest(
            compensationItemId = itemId,
            reason = "Some reason",
        )
        val errorMessage = "Item cannot be revoked"

        // When
        val response = request.toFailure(errorMessage)

        // Then
        assertEquals(itemId, response.compensationItemId)
        assertEquals(ResultType.ERROR, response.resultType)
        assertEquals(errorMessage, response.message)
    }

    @Test
    fun `toSuccess should create success response with default message`() {
        // Given
        val itemId = UUID.randomUUID()
        val request = RevokeCompensationItemRequest(
            compensationItemId = itemId,
        )

        // When
        val response = request.toSuccess()

        // Then
        assertEquals(itemId, response.compensationItemId)
        assertEquals(ResultType.INFO, response.resultType)
        assertEquals("Item Revoked Successfully", response.message)
    }

    @Test
    fun `buildRevokeCompensationItemsResponse should return SUCCESS when all results are successful`() {
        // Given
        val itemId1 = UUID.randomUUID()
        val itemId2 = UUID.randomUUID()

        val results = listOf(
            RevokeCompensationItemResponse(
                compensationItemId = itemId1,
                resultType = ResultType.INFO,
                message = "Item Revoked Successfully",
            ),
            RevokeCompensationItemResponse(
                compensationItemId = itemId2,
                resultType = ResultType.INFO,
                message = "Item Revoked Successfully",
            ),
        )

        val request = RevokeCompensationItemsRequest(
            validRequests = listOf(
                RevokeCompensationItemRequest(itemId1),
                RevokeCompensationItemRequest(itemId2),
            ),
            inaccessibleRequests = emptyList(),
        )

        // When
        val response = buildRevokeCompensationItemsResponse(results, request)

        // Then
        assertEquals(OperationStatus.SUCCESS, response.status)
        assertEquals(2, response.results.size)
        assertEquals(2, response.results.count { it.resultType == ResultType.INFO })
        assertEquals(0, response.results.count { it.resultType == ResultType.ERROR })
    }

    @Test
    fun `buildRevokeCompensationItemsResponse should return FAILURE when all results are errors`() {
        // Given
        val itemId1 = UUID.randomUUID()
        val itemId2 = UUID.randomUUID()

        val results = listOf(
            RevokeCompensationItemResponse(
                compensationItemId = itemId1,
                resultType = ResultType.ERROR,
                message = "Item cannot be revoked",
            ),
        )

        val request = RevokeCompensationItemsRequest(
            validRequests = listOf(
                RevokeCompensationItemRequest(itemId1),
            ),
            inaccessibleRequests = listOf(
                RevokeCompensationItemRequest(itemId2, "Access denied"),
            ),
        )

        // When
        val response = buildRevokeCompensationItemsResponse(results, request)

        // Then
        assertEquals(OperationStatus.FAILURE, response.status)
        assertEquals(2, response.results.size)
        assertEquals(0, response.results.count { it.resultType == ResultType.INFO })
        assertEquals(2, response.results.count { it.resultType == ResultType.ERROR })
    }

    @Test
    fun `buildRevokeCompensationItemsResponse should return PARTIAL_SUCCESS when there are both success and error results`() {
        // Given
        val itemId1 = UUID.randomUUID()
        val itemId2 = UUID.randomUUID()
        val itemId3 = UUID.randomUUID()

        val results = listOf(
            RevokeCompensationItemResponse(
                compensationItemId = itemId1,
                resultType = ResultType.INFO,
                message = "Item Revoked Successfully",
            ),
            RevokeCompensationItemResponse(
                compensationItemId = itemId2,
                resultType = ResultType.ERROR,
                message = "Item cannot be revoked",
            ),
        )

        val request = RevokeCompensationItemsRequest(
            validRequests = listOf(
                RevokeCompensationItemRequest(itemId1),
                RevokeCompensationItemRequest(itemId2),
            ),
            inaccessibleRequests = listOf(
                RevokeCompensationItemRequest(itemId3, "Access denied"),
            ),
        )

        // When
        val response = buildRevokeCompensationItemsResponse(results, request)

        // Then
        assertEquals(OperationStatus.PARTIAL_SUCCESS, response.status)
        assertEquals(3, response.results.size)
        assertEquals(1, response.results.count { it.resultType == ResultType.INFO })
        assertEquals(2, response.results.count { it.resultType == ResultType.ERROR })
    }

    @Test
    fun `buildRevokeCompensationItemsResponse should include inaccessible requests as error results`() {
        // Given
        val itemId1 = UUID.randomUUID()
        val itemId2 = UUID.randomUUID()

        val results = listOf(
            RevokeCompensationItemResponse(
                compensationItemId = itemId1,
                resultType = ResultType.INFO,
                message = "Item Revoked Successfully",
            ),
        )

        val request = RevokeCompensationItemsRequest(
            validRequests = listOf(
                RevokeCompensationItemRequest(itemId1),
            ),
            inaccessibleRequests = listOf(
                RevokeCompensationItemRequest(itemId2, "Some reason"),
            ),
        )

        // When
        val response = buildRevokeCompensationItemsResponse(results, request)

        // Then
        assertEquals(OperationStatus.PARTIAL_SUCCESS, response.status)
        assertEquals(2, response.results.size)

        // Verify the inaccessible request was included with the correct error message
        val inaccessibleResult = response.results.find { it.compensationItemId == itemId2 }
        assertEquals(ResultType.ERROR, inaccessibleResult?.resultType)
        assertEquals("Access denied", inaccessibleResult?.message)
    }

    @Test
    fun `buildRevokeCompensationItemsResponse should handle empty results and requests`() {
        // Given
        val results = emptyList<RevokeCompensationItemResponse>()
        val request = RevokeCompensationItemsRequest(
            validRequests = emptyList(),
            inaccessibleRequests = emptyList(),
        )

        // When
        val response = buildRevokeCompensationItemsResponse(results, request)

        // Then
        assertEquals(OperationStatus.SUCCESS, response.status)
        assertEquals(0, response.results.size)
    }

    @Test
    fun `buildRevokeCompensationItemsResponse should handle only inaccessible requests`() {
        // Given
        val itemId = UUID.randomUUID()
        val results = emptyList<RevokeCompensationItemResponse>()
        val request = RevokeCompensationItemsRequest(
            validRequests = emptyList(),
            inaccessibleRequests = listOf(
                RevokeCompensationItemRequest(itemId, "Access denied"),
            ),
        )

        // When
        val response = buildRevokeCompensationItemsResponse(results, request)

        // Then
        assertEquals(OperationStatus.FAILURE, response.status)
        assertEquals(1, response.results.size)
        assertEquals(ResultType.ERROR, response.results[0].resultType)
        assertEquals("Access denied", response.results[0].message)
    }
}
