package com.multiplier.compensation.service.compensationitem.testdatagenerator

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import java.time.LocalDate
import java.util.UUID

class TestCompensationBuilder {
    private companion object TestData {
        val DEFAULT_START_DATE: LocalDate = LocalDate.of(2024, 1, 1)
        val DEFAULT_END_DATE: LocalDate? = null
        val DEFAULT_BILLING_RATE_TYPE = BillingRateType.VALUE
        val DEFAULT_BILLING_FREQUENCY = BillingFrequency.MONTHLY
        val DEFAULT_PAY_SCHEDULE_ID: UUID = UUID.randomUUID()
        val DEFAULT_SCHEMA_ITEM_ID: UUID = UUID.randomUUID()
        val DEFAULT_COMPENSATION_STATUS = CompensationStatus.NEW
        val DEFAULT_NO_OF_INSTALLMENTS = null

        const val DEFAULT_COMPANY_ID = 1L
        const val DEFAULT_ENTITY_ID = 1L
        const val DEFAULT_CONTRACT_ID = 1L
        const val DEFAULT_BILLING_RATE = 100.0
        const val DEFAULT_CURRENCY = "USD"
        const val DEFAULT_CATEGORY = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY
        const val DEFAULT_IS_INSTALLMENT = false
    }

    private var startDate: LocalDate = DEFAULT_START_DATE
    private var endDate: LocalDate? = DEFAULT_END_DATE
    private var billingRateType: BillingRateType = DEFAULT_BILLING_RATE_TYPE
    private var billingFrequency: BillingFrequency = DEFAULT_BILLING_FREQUENCY
    private var billingRate: Double = DEFAULT_BILLING_RATE
    private var isInstallment: Boolean = DEFAULT_IS_INSTALLMENT
    private var noOfInstallments: Int? = DEFAULT_NO_OF_INSTALLMENTS
    private var generatedInstallments: Int? = null
    private var processedUntilDate: LocalDate? = null
    private var status: CompensationStatus = DEFAULT_COMPENSATION_STATUS
    private var schemaItemId: UUID = DEFAULT_SCHEMA_ITEM_ID
    private var category: String = DEFAULT_CATEGORY
    private var payScheduleId: UUID = DEFAULT_PAY_SCHEDULE_ID

    fun withStartDate(date: LocalDate) = apply { this.startDate = date }

    fun withEndDate(date: LocalDate?) = apply { this.endDate = date }

    fun withBillingRateType(billingRateType: BillingRateType) = apply { this.billingRateType = billingRateType }

    fun withBillingFrequency(billingFrequency: BillingFrequency) = apply { this.billingFrequency = billingFrequency }

    fun withBillingRate(billingRate: Double) = apply { this.billingRate = billingRate }

    fun withIsInstallment(isInstallment: Boolean) = apply { this.isInstallment = isInstallment }

    fun withNoOfInstallments(numberOfInstallments: Int?) = apply { this.noOfInstallments = numberOfInstallments }

    fun withGeneratedInstallments(count: Int?) = apply { this.generatedInstallments = count }

    fun withProcessedUntilDate(date: LocalDate?) = apply { this.processedUntilDate = date }

    fun withStatus(status: CompensationStatus) = apply { this.status = status }

    fun withSchemaItemId(schemaItemId: UUID) = apply { this.schemaItemId = schemaItemId }

    fun withCategory(category: String) = apply { this.category = category }

    fun withPayScheduleId(payScheduleId: UUID) = apply { this.payScheduleId = payScheduleId }

    fun build() = Compensation(
        id = UUID.randomUUID(),
        companyId = DEFAULT_COMPANY_ID,
        entityId = DEFAULT_ENTITY_ID,
        contractId = DEFAULT_CONTRACT_ID,
        schemaItemId = schemaItemId,
        category = category,
        currency = DEFAULT_CURRENCY,
        billingRateType = billingRateType,
        billingRate = billingRate,
        billingFrequency = billingFrequency,
        payScheduleId = payScheduleId,
        startDate = startDate,
        endDate = endDate,
        isInstallment = isInstallment,
        noOfInstallments = noOfInstallments,
        generatedInstallments = generatedInstallments,
        processedUntilDate = processedUntilDate,
        status = status,
    )
}
