package com.multiplier.compensation.service.compensationitem.update.adjusters

import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.service.compensationitem.generation.CompensationItemGenerationManager
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationItemBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationUpdateContextBuilder
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.update.managers.CancellationArrearManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemConsolidationManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemUpdateManager
import com.multiplier.compensation.service.compensationitem.update.managers.SpillOverItemManager
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isAbortedItem
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isCancellationArrear
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class BillingRateAdjusterForNonInstallmentTest {
    private companion object TestData {
        val oldImage = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withBillingRate(100.00)
            .build()
        val newImage = oldImage.copy(
            endDate = LocalDate.of(2024, 2, 14),
        )
        val newRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 2, 15))
            .withBillingRate(200.00)
            .build()

        fun preUpdateItemBuilder() = TestCompensationItemBuilder()
            .withCompensation(oldImage)

        fun postUpdateItemBuilder() = TestCompensationItemBuilder()
            .withCompensation(newRecord)
    }

    private lateinit var itemGenerationManager: CompensationItemGenerationManager
    private lateinit var itemUpdateManager: CompensationItemUpdateManager
    private lateinit var cancellationArrearManager: CancellationArrearManager
    private lateinit var spillOverItemManager: SpillOverItemManager
    private lateinit var consolidationManager: CompensationItemConsolidationManager
    private val generationPeriodInDays = 60L

    private lateinit var billingRateAdjuster: BillingRateUpdateAdjuster

    @BeforeEach
    fun setup() {
        itemGenerationManager = mockk()
        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns emptyList()

        itemUpdateManager = CompensationItemUpdateManager(
            compensationItemRepository = mockk(),
            transactional = mockk(),
        )
        cancellationArrearManager = CancellationArrearManager()
        spillOverItemManager = SpillOverItemManager()
        consolidationManager = CompensationItemConsolidationManager()

        billingRateAdjuster = BillingRateUpdateAdjuster(
            itemGenerationManager,
            itemUpdateManager,
            cancellationArrearManager,
            spillOverItemManager,
            consolidationManager,
            generationPeriodInDays,
        )
    }

    @ParameterizedTest(name = "{index} => {2}")
    @MethodSource("provideContextValidationTestCases")
    fun `should throw exception when context is invalid`(
        context: CompensationUpdateContext,
        exceptionMessage: String,
        testCaseName: String,
    ) {
        val exception = assertThrows<IllegalArgumentException> {
            billingRateAdjuster.adjust(context)
        }
        Assertions.assertEquals(exceptionMessage, exception.message)
    }

    fun provideContextValidationTestCases(): List<Arguments> = listOf(
        createInvalidOldImageTestCase(),
        createInvalidNewImageTestCase(),
        createInvalidNewRecordTestCase(),
        createInvalidNewRecordStartDateTestCase(),
        createInvalidParentUpdateContextTestCase(),
    )

    fun createInvalidOldImageTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withNewImage(mockk())
            .withNewRecord(mockk())
            .build(),
        "Old image should not be null.",
        "should throw exception when OldImage is null",
    )

    fun createInvalidNewImageTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withOldImage(mockk())
            .withNewRecord(mockk())
            .build(),
        "New image should not be null.",
        "should throw exception when NewImage is null",
    )

    fun createInvalidNewRecordTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withOldImage(mockk())
            .withNewImage(mockk())
            .build(),
        "New record should not be null.",
        "should throw exception when NewRecord is null",
    )

    fun createInvalidNewRecordStartDateTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(mockk())
            .withNewRecord(newRecord.copy(startDate = oldImage.startDate.minusMonths(1)))
            .build(),
        "Invalid request: Attempted to modify the start date to an earlier value.",
        "should throw exception when NewRecord's start date is before that of the existing record",
    )

    fun createInvalidParentUpdateContextTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withOldImage(mockk())
            .withNewImage(mockk())
            .withNewRecord(mockk())
            .withParentUpdateContext(mockk())
            .build(),
        "Parent update context should be null.",
        "should throw exception when ParentUpdateContext is not null",
    )

    @Test
    fun `should adjust when overlapping items exist and a processed spillover item is found`() {
        val item1 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .build()
        val item2 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .withCutOffDate(LocalDate.of(2024, 2, 15))
            .build()
        val item3 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .build()
        val existingItems = listOf(item1, item2, item3)
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(newImage)
            .withNewRecord(newRecord)
            .withExistingItems(existingItems)
            .build()

        every {
            itemGenerationManager.generateItems(newRecord, any<LocalDate>())
        } returns listOf(
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 2, 15))
                .withEndDate(LocalDate.of(2024, 2, 29))
                .build(),
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 3, 1))
                .withEndDate(LocalDate.of(2024, 3, 31))
                .build(),
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 4, 1))
                .withEndDate(LocalDate.of(2024, 4, 30))
                .build(),
        )

        val updatedContext = billingRateAdjuster.adjust(initialContext)

        assertTrue { initialContext != updatedContext }
        assertTrue { updatedContext.adjustedItems.oldImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isNotEmpty() }
        assertEquals(1, updatedContext.adjustedItems.oldImageItems.count())
        assertEquals(3, updatedContext.adjustedItems.newRecordItems.count())

        val oldImageAbortedItems = updatedContext.adjustedItems.oldImageItems.filter { isAbortedItem(it) }
        Assertions.assertEquals(1, oldImageAbortedItems.count())
        Assertions.assertEquals(item3.id, oldImageAbortedItems[0].id)

        val oldImageCancellationArrears = updatedContext.adjustedItems.oldImageItems.filter { isCancellationArrear(it) }
        assertTrue { oldImageCancellationArrears.isEmpty() }

        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                eq(newRecord),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }

        verify(exactly = 0) {
            itemGenerationManager.generateTruncatedItem(
                any<Compensation>(),
                any<CompensationItem>(),
                any<CompensationSchemaItem>(),
            )
        }

        val adjustmentArrears = updatedContext.adjustedItems.newRecordItems.filter { it.isArrear }
        assertEquals(1, adjustmentArrears.count())
        assertEquals(LocalDate.of(2024, 2, 15), adjustmentArrears[0].startDate)
        assertEquals(LocalDate.of(2024, 2, 29), adjustmentArrears[0].endDate)
        assertEquals(item2.id, adjustmentArrears[0].arrearOf)
        assertEquals(newRecord.id.toString(), adjustmentArrears[0].arrearTriggerReference)
        assertEquals(newRecord.billingRate, adjustmentArrears[0].billingRate)

        val newItems = updatedContext.adjustedItems.newRecordItems.filter { !it.isArrear }
        assertEquals(2, newItems.count())
    }

    @Test
    fun `should adjust when overlapping items exist and an unprocessed spillover item is found`() {
        val item1 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .build()
        val item2 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .build()
        val item3 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .build()

        val existingItems = listOf(item1, item2, item3)
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(newImage)
            .withNewRecord(newRecord)
            .withExistingItems(existingItems)
            .build()

        every {
            itemGenerationManager.generateItems(newRecord, any<LocalDate>())
        } returns listOf(
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 2, 15))
                .withEndDate(LocalDate.of(2024, 2, 29))
                .build(),
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 3, 1))
                .withEndDate(LocalDate.of(2024, 3, 31))
                .build(),
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 4, 1))
                .withEndDate(LocalDate.of(2024, 4, 30))
                .build(),
        )

        every {
            itemGenerationManager.generateTruncatedItem(
                any<Compensation>(),
                any<CompensationItem>(),
                any<CompensationSchemaItem>(),
            )
        } returns preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 14))
            .build()

        val updatedContext = billingRateAdjuster.adjust(initialContext)

        assertTrue { initialContext != updatedContext }
        assertTrue { updatedContext.adjustedItems.oldImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isNotEmpty() }
        assertEquals(2, updatedContext.adjustedItems.oldImageItems.count())
        assertEquals(1, updatedContext.adjustedItems.newImageItems.count())
        assertEquals(3, updatedContext.adjustedItems.newRecordItems.count())

        val oldImageAbortedItems = updatedContext.adjustedItems.oldImageItems.filter { isAbortedItem(it) }
        Assertions.assertEquals(2, oldImageAbortedItems.count())
        val abortedItem1 = updatedContext.adjustedItems.oldImageItems.firstOrNull { it.id == item2.id }
        val abortedItem2 = updatedContext.adjustedItems.oldImageItems.firstOrNull { it.id == item3.id }
        assertNotNull(abortedItem1)
        assertNotNull(abortedItem2)

        val oldImageCancellationArrears = updatedContext.adjustedItems.oldImageItems.filter { isCancellationArrear(it) }
        assertTrue { oldImageCancellationArrears.isEmpty() }

        verify(exactly = 1) {
            itemGenerationManager.generateTruncatedItem(
                eq(newImage),
                eq(item2),
                any(),
            )
        }
        val truncatedItem = updatedContext.adjustedItems.newImageItems[0]
        assertEquals(LocalDate.of(2024, 2, 1), truncatedItem.startDate)
        assertEquals(LocalDate.of(2024, 2, 14), truncatedItem.endDate)

        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                eq(newRecord),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }

        val adjustmentArrears = updatedContext.adjustedItems.newRecordItems.filter { it.isArrear }
        assertTrue(adjustmentArrears.isEmpty())
    }

    @Test
    fun `should adjust when the term is shortened leading to items getting aborted`() {
        val item1 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .build()
        val item2 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .build()
        val item3 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .build()
        val item4 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 4, 1))
            .withEndDate(LocalDate.of(2024, 4, 30))
            .build()
        val existingItems = listOf(item1, item2, item3, item4)
        val newImage = oldImage.copy(
            endDate = LocalDate.of(2024, 1, 31),
        )
        val newRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withBillingRate(200.00)
            .build()

        fun postUpdateItemBuilder() = TestCompensationItemBuilder()
            .withCompensation(newRecord)
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(newImage)
            .withNewRecord(newRecord)
            .withExistingItems(existingItems)
            .build()

        every {
            itemGenerationManager.generateItems(newRecord, any<LocalDate>())
        } returns listOf(
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 2, 1))
                .withEndDate(LocalDate.of(2024, 2, 29))
                .build(),
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 3, 1))
                .withEndDate(LocalDate.of(2024, 3, 31))
                .build(),
        )

        val updatedContext = billingRateAdjuster.adjust(initialContext)

        assertTrue { initialContext != updatedContext }
        assertTrue { updatedContext.adjustedItems.oldImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isNotEmpty() }
        assertEquals(3, updatedContext.adjustedItems.oldImageItems.count())
        assertEquals(2, updatedContext.adjustedItems.newRecordItems.count())

        val oldImageAbortedItems = updatedContext.adjustedItems.oldImageItems.filter { isAbortedItem(it) }
        assertEquals(3, oldImageAbortedItems.count())
        val abortedItem1 = oldImageAbortedItems.firstOrNull { it.id == item2.id }
        val abortedItem2 = oldImageAbortedItems.firstOrNull { it.id == item3.id }
        val abortedItem3 = oldImageAbortedItems.firstOrNull { it.id == item4.id }
        assertNotNull(abortedItem1)
        assertNotNull(abortedItem2)
        assertNotNull(abortedItem3)

        val oldImageCancellationArrears = updatedContext.adjustedItems.oldImageItems.filter { isCancellationArrear(it) }
        assertTrue(oldImageCancellationArrears.isEmpty())

        verify(exactly = 0) {
            itemGenerationManager.generateTruncatedItem(
                any<Compensation>(),
                any<CompensationItem>(),
                any<CompensationSchemaItem>(),
            )
        }

        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                eq(newRecord),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }

        val adjustmentArrears = updatedContext.adjustedItems.newRecordItems.filter { it.isArrear }
        assertTrue(adjustmentArrears.isEmpty())
    }

    @Test
    fun `should adjust when the term is shortened leading to cancellation arrear generation`() {
        val item1 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .build()
        val item2 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .withCutOffDate(LocalDate.of(2024, 2, 15))
            .build()
        val item3 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withCutOffDate(LocalDate.of(2024, 3, 15))
            .build()
        val item4 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 4, 1))
            .withEndDate(LocalDate.of(2024, 4, 30))
            .withCutOffDate(LocalDate.of(2024, 4, 15))
            .build()
        val existingItems = listOf(item1, item2, item3, item4)
        val newImage = oldImage.copy(
            endDate = LocalDate.of(2024, 1, 31),
        )
        val newRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 3, 15))
            .withBillingRate(200.00)
            .build()

        fun postUpdateItemBuilder() = TestCompensationItemBuilder()
            .withCompensation(newRecord)
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(newImage)
            .withNewRecord(newRecord)
            .withExistingItems(existingItems)
            .build()

        every {
            itemGenerationManager.generateItems(newRecord, any<LocalDate>())
        } returns listOf(
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 2, 1))
                .withEndDate(LocalDate.of(2024, 2, 29))
                .build(),
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 3, 1))
                .withEndDate(LocalDate.of(2024, 3, 15))
                .build(),
        )

        val updatedContext = billingRateAdjuster.adjust(initialContext)

        assertTrue { initialContext != updatedContext }
        assertTrue { updatedContext.adjustedItems.oldImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isNotEmpty() }
        assertEquals(2, updatedContext.adjustedItems.oldImageItems.count())
        assertEquals(2, updatedContext.adjustedItems.newRecordItems.count())

        val oldImageAbortedItems = updatedContext.adjustedItems.oldImageItems.filter { isAbortedItem(it) }
        assertTrue(oldImageAbortedItems.isEmpty())

        val oldImageCancellationArrears = updatedContext.adjustedItems.oldImageItems.filter { isCancellationArrear(it) }
        assertEquals(2, oldImageCancellationArrears.count())
        val spillOverCancellationArrear = oldImageCancellationArrears.firstOrNull { it.arrearOf == item3.id }
        val cancellationArrear = oldImageCancellationArrears.firstOrNull { it.arrearOf == item4.id }
        assertNotNull(spillOverCancellationArrear)
        assertNotNull(cancellationArrear)
        assertEquals(LocalDate.of(2024, 3, 16), spillOverCancellationArrear.startDate)
        assertEquals(LocalDate.of(2024, 3, 31), spillOverCancellationArrear.endDate)

        verify(exactly = 0) {
            itemGenerationManager.generateTruncatedItem(
                any<Compensation>(),
                any<CompensationItem>(),
                any<CompensationSchemaItem>(),
            )
        }

        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                eq(newRecord),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }

        val adjustmentArrears = updatedContext.adjustedItems.newRecordItems.filter { it.isArrear }
        assertEquals(2, adjustmentArrears.count())
        val adjustmentArrear1 = adjustmentArrears.firstOrNull { it.arrearOf == item2.id }
        val adjustmentArrear2 = adjustmentArrears.firstOrNull { it.arrearOf == item3.id }
        assertNotNull(adjustmentArrear1)
        assertNotNull(adjustmentArrear2)
        assertEquals(adjustmentArrear1.billingRate, newRecord.billingRate)
        assertEquals(adjustmentArrear2.billingRate, newRecord.billingRate)
    }

    @Test
    fun `should not adjust when new record is not eligible for item generation`() {
        val item1 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .build()
        val item2 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .build()
        val item3 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withCutOffDate(LocalDate.of(2024, 3, 15))
            .build()
        val existingItems = listOf(item1, item2, item3)
        val newImage = oldImage.copy(
            endDate = LocalDate.now().plusDays(89),
        )
        val newRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.now().plusDays(90))
            .withBillingRate(200.00)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(newImage)
            .withNewRecord(newRecord)
            .withExistingItems(existingItems)
            .build()

        val updatedContext = billingRateAdjuster.adjust(initialContext)

        assertTrue { updatedContext.adjustedItems.oldImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isEmpty() }

        verify(exactly = 0) {
            itemGenerationManager.generateTruncatedItem(
                any<Compensation>(),
                any<CompensationItem>(),
                any<CompensationSchemaItem>(),
            )
        }

        verify(exactly = 0) {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        }
    }

    @Test
    fun `should adjust when multiple overlapping processed items are found for a new item`() {
        val item1 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .build()
        val partialItem1 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 10))
            .withCutOffDate(LocalDate.of(2024, 2, 15))
            .build()
        val partialItem2 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 11))
            .withEndDate(LocalDate.of(2024, 2, 15))
            .withCutOffDate(LocalDate.of(2024, 2, 15))
            .build()
        val partialItem3 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 16))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .withCutOffDate(LocalDate.of(2024, 2, 15))
            .build()
        val item4 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withCutOffDate(LocalDate.of(2024, 3, 15))
            .build()
        val existingItems = listOf(item1, partialItem1, partialItem2, partialItem3, item4)
        val newImage = oldImage.copy(
            endDate = LocalDate.of(2024, 1, 31),
        )
        val newRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withBillingRate(200.00)
            .build()

        fun postUpdateItemBuilder() = TestCompensationItemBuilder()
            .withCompensation(newRecord)
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(newImage)
            .withNewRecord(newRecord)
            .withExistingItems(existingItems)
            .build()

        every {
            itemGenerationManager.generateItems(newRecord, any<LocalDate>())
        } returns listOf(
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 2, 1))
                .withEndDate(LocalDate.of(2024, 2, 29))
                .build(),
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 3, 1))
                .withEndDate(LocalDate.of(2024, 3, 30))
                .build(),
        )

        val updatedContext = billingRateAdjuster.adjust(initialContext)

        assertTrue { updatedContext.adjustedItems.oldImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isNotEmpty() }
        assertEquals(2, updatedContext.adjustedItems.newRecordItems.count())

        verify(exactly = 0) {
            itemGenerationManager.generateTruncatedItem(
                any<Compensation>(),
                any<CompensationItem>(),
                any<CompensationSchemaItem>(),
            )
        }

        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                eq(newRecord),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }

        val adjustmentArrears = updatedContext.adjustedItems.newRecordItems.filter { it.isArrear }
        assertEquals(2, adjustmentArrears.count())
        val adjustmentArrear1 = adjustmentArrears.firstOrNull { it.arrearOf == partialItem1.id }
        val adjustmentArrear2 = adjustmentArrears.firstOrNull { it.arrearOf == item4.id }
        assertNotNull(adjustmentArrear1)
        assertNotNull(adjustmentArrear2)
        assertEquals(adjustmentArrear1.billingRate, newRecord.billingRate)
        assertEquals(adjustmentArrear2.billingRate, newRecord.billingRate)
    }

    @org.junit.jupiter.api.Test
    fun `should adjust when parent compensation is entirely replaced`() {
        val item0 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2023, 12, 1))
            .withEndDate(LocalDate.of(2023, 12, 31))
            .withCutOffDate(LocalDate.of(2023, 12, 15))
            .build()
        val item0CancellationArrear = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2023, 12, 1))
            .withEndDate(LocalDate.of(2023, 12, 31))
            .withCutOffDate(LocalDate.of(2023, 12, 15))
            .withIsArrear(true)
            .withCompensation(oldImage.copy(billingRate = 0.00))
            .withArrearOf(id = item0.id)
            .build()
        val item1 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .build()
        val item2 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .build()
        val item3 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .build()
        val item4 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 4, 1))
            .withEndDate(LocalDate.of(2024, 4, 30))
            .build()
        val existingItems = listOf(item0, item0CancellationArrear, item1, item2, item3, item4)
        val newImage = oldImage.copy(
            status = CompensationStatus.ABORTED,
        )
        val newRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withBillingRate(200.00)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(newImage)
            .withNewRecord(newRecord)
            .withExistingItems(existingItems)
            .build()

        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns listOf(
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 1, 1))
                .withEndDate(LocalDate.of(2024, 1, 31))
                .build(),
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 2, 1))
                .withEndDate(LocalDate.of(2024, 2, 29))
                .build(),
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 3, 1))
                .withEndDate(LocalDate.of(2024, 3, 31))
                .build(),
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 4, 1))
                .withEndDate(LocalDate.of(2024, 4, 30))
                .build(),
        )

        val updatedContext = billingRateAdjuster.adjust(initialContext)

        assertTrue { updatedContext.adjustedItems.oldImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isNotEmpty() }
        assertEquals(4, updatedContext.adjustedItems.oldImageItems.count())
        assertEquals(4, updatedContext.adjustedItems.newRecordItems.count())

        val oldImageAbortedItems = updatedContext.adjustedItems.oldImageItems.filter { isAbortedItem(it) }
        assertEquals(4, oldImageAbortedItems.count())
        val abortedItem1 = oldImageAbortedItems.firstOrNull { it.id == item1.id }
        val abortedItem2 = oldImageAbortedItems.firstOrNull { it.id == item2.id }
        val abortedItem3 = oldImageAbortedItems.firstOrNull { it.id == item3.id }
        val abortedItem4 = oldImageAbortedItems.firstOrNull { it.id == item4.id }
        assertNotNull(abortedItem1)
        assertNotNull(abortedItem2)
        assertNotNull(abortedItem3)
        assertNotNull(abortedItem4)

        val oldImageCancellationArrears = updatedContext.adjustedItems.oldImageItems.filter { isCancellationArrear(it) }
        assertTrue(oldImageCancellationArrears.isEmpty())

        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                eq(newRecord),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }

        val adjustmentArrears = updatedContext.adjustedItems.newImageItems.filter { it.isArrear }
        assertTrue(adjustmentArrears.isEmpty())
    }
}
