package com.multiplier.compensation.service.compensationitem.update.adjusters

import com.multiplier.compensation.service.compensationitem.generation.CompensationItemGenerationManager
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationItemBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationUpdateContextBuilder
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.update.managers.CancellationArrearManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemConsolidationManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemUpdateManager
import com.multiplier.compensation.service.compensationitem.update.managers.SpillOverItemManager
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isAbortedItem
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isCancellationArrear
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EarlyTerminationAdjusterTest {
    private companion object TestData {
        val oldImage = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .build()
        val newImage = oldImage.copy(
            endDate = LocalDate.of(2024, 4, 15),
        )

        fun itemBuilder() = TestCompensationItemBuilder()
            .withCompensation(oldImage)
    }

    private lateinit var itemGenerationManager: CompensationItemGenerationManager
    private lateinit var itemUpdateManager: CompensationItemUpdateManager
    private lateinit var cancellationArrearManager: CancellationArrearManager
    private lateinit var spillOverItemManager: SpillOverItemManager
    private lateinit var consolidationManager: CompensationItemConsolidationManager

    private lateinit var earlyTerminationAdjuster: EarlyTerminationAdjuster

    @BeforeEach
    fun setup() {
        itemGenerationManager = CompensationItemGenerationManager(
            payScheduleService = mockk(),
            compensationSchemaService = mockk(),
        )
        itemUpdateManager = CompensationItemUpdateManager(
            compensationItemRepository = mockk(),
            transactional = mockk(),
        )
        cancellationArrearManager = CancellationArrearManager()
        spillOverItemManager = SpillOverItemManager()
        consolidationManager = CompensationItemConsolidationManager()

        earlyTerminationAdjuster = EarlyTerminationAdjuster(
            itemGenerationManager,
            itemUpdateManager,
            cancellationArrearManager,
            spillOverItemManager,
            consolidationManager,
        )
    }

    @ParameterizedTest(name = "{index} => {2}")
    @MethodSource("provideContextValidationTestCases")
    fun `should throw exception when context is invalid`(
        context: CompensationUpdateContext,
        exceptionMessage: String,
        testCaseName: String,
    ) {
        val exception = assertThrows<IllegalArgumentException> {
            earlyTerminationAdjuster.adjust(context)
        }
        assertEquals(exceptionMessage, exception.message)
    }

    fun provideContextValidationTestCases(): List<Arguments> = listOf(
        createInvalidOldImageTestCase(),
        createInvalidNewImageTestCase(),
        createInvalidNewRecordTestCase(),
        createInvalidParentUpdateContextTestCase(),
    )

    fun createInvalidOldImageTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withNewImage(mockk())
            .build(),
        "Old image should not be null.",
        "should throw exception when OldImage is null",
    )

    fun createInvalidNewImageTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withOldImage(mockk())
            .build(),
        "New image should not be null.",
        "should throw exception when NewImage is null",
    )

    fun createInvalidNewRecordTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withOldImage(mockk())
            .withNewImage(mockk())
            .withNewRecord(mockk())
            .build(),
        "New record should be null.",
        "should throw exception when NewRecord is not null",
    )

    fun createInvalidParentUpdateContextTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withOldImage(mockk())
            .withNewImage(mockk())
            .withParentUpdateContext(mockk())
            .build(),
        "Parent update context should be null.",
        "should throw exception when ParentUpdateContext is not null",
    )

    @Test
    fun `should invalidate all eligible items including a processed spillover item`() {
        val processedItem1 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .build()
        val processedItem2 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .withCutOffDate(LocalDate.of(2024, 2, 15))
            .build()
        val processedItem3 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withCutOffDate(LocalDate.of(2024, 3, 15))
            .build()
        val processedItem4 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 4, 1))
            .withEndDate(LocalDate.of(2024, 4, 30))
            .withCutOffDate(LocalDate.of(2024, 4, 15))
            .build()
        val processedItem5 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 5, 1))
            .withEndDate(LocalDate.of(2024, 5, 31))
            .withCutOffDate(LocalDate.of(2024, 5, 15))
            .build()
        val unprocessedItem1 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 6, 1))
            .withEndDate(LocalDate.of(2024, 6, 30))
            .build()
        val existingItems = listOf(
            processedItem1,
            processedItem2,
            processedItem3,
            processedItem4,
            processedItem5,
            unprocessedItem1,
        )
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(newImage)
            .withExistingItems(existingItems)
            .build()

        val updatedContext = earlyTerminationAdjuster.adjust(initialContext)

        assertTrue { initialContext != updatedContext }
        assertTrue { updatedContext.adjustedItems.oldImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isEmpty() }
        assertEquals(3, updatedContext.adjustedItems.oldImageItems.count())

        val oldImageAbortedItems = updatedContext.adjustedItems.oldImageItems.filter { isAbortedItem(it) }
        assertEquals(1, oldImageAbortedItems.count())
        assertEquals(unprocessedItem1.id, oldImageAbortedItems[0].id)

        val oldImageCancellationArrears = updatedContext.adjustedItems.oldImageItems.filter { isCancellationArrear(it) }
        assertEquals(2, oldImageCancellationArrears.count())
        val spillOverItemCancellationArrear = oldImageCancellationArrears
            .firstOrNull { it.arrearOf == processedItem4.id }
        val cancellationArrear = oldImageCancellationArrears
            .firstOrNull { it.arrearOf == processedItem5.id }
        assertNotNull(spillOverItemCancellationArrear)
        assertNotNull(cancellationArrear)
        assertEquals(newImage.endDate!!.plusDays(1), spillOverItemCancellationArrear.startDate)
        assertEquals(processedItem4.endDate, spillOverItemCancellationArrear.endDate)
        assertEquals(processedItem5.startDate, cancellationArrear.startDate)
        assertEquals(processedItem5.endDate, cancellationArrear.endDate)
    }

    @Test
    fun `should invalidate all eligible items including an unprocessed spillover item`() {
        val processedItem1 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .build()
        val processedItem2 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .withCutOffDate(LocalDate.of(2024, 2, 15))
            .build()
        val processedItem3 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withCutOffDate(LocalDate.of(2024, 3, 15))
            .build()
        val unprocessedItem1 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 4, 1))
            .withEndDate(LocalDate.of(2024, 4, 30))
            .build()
        val unprocessedItem2 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 5, 1))
            .withEndDate(LocalDate.of(2024, 5, 31))
            .build()
        val existingItems = listOf(
            processedItem1,
            processedItem2,
            processedItem3,
            unprocessedItem1,
            unprocessedItem2,
        )
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(newImage)
            .withExistingItems(existingItems)
            .build()

        val updatedContext = earlyTerminationAdjuster.adjust(initialContext)

        assertTrue { initialContext != updatedContext }
        assertTrue { updatedContext.adjustedItems.oldImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isEmpty() }
        assertEquals(2, updatedContext.adjustedItems.oldImageItems.count())
        assertEquals(1, updatedContext.adjustedItems.newImageItems.count())

        val oldImageAbortedItems = updatedContext.adjustedItems.oldImageItems.filter { isAbortedItem(it) }
        assertEquals(2, oldImageAbortedItems.count())
        val abortedItem1 = oldImageAbortedItems.firstOrNull { it.id == unprocessedItem1.id }
        val abortedItem2 = oldImageAbortedItems.firstOrNull { it.id == unprocessedItem2.id }
        assertNotNull(abortedItem1)
        assertNotNull(abortedItem2)

        val oldImageCancellationArrears = updatedContext.adjustedItems.oldImageItems.filter { isCancellationArrear(it) }
        assertTrue { oldImageCancellationArrears.isEmpty() }
    }
}
