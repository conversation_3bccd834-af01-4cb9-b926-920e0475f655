package com.multiplier.compensation.service.compensationitem.update.adjusters

import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.service.compensationitem.generation.CompensationItemGenerationManager
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationItemBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationUpdateContextBuilder
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.update.managers.CancellationArrearManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemConsolidationManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemUpdateManager
import com.multiplier.compensation.service.compensationitem.update.managers.SpillOverItemManager
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import kotlin.test.assertNotEquals
import kotlin.test.assertTrue

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class NewComponentAdjusterTest {
    private lateinit var itemGenerationManager: CompensationItemGenerationManager
    private lateinit var itemUpdateManager: CompensationItemUpdateManager
    private lateinit var cancellationArrearManager: CancellationArrearManager
    private lateinit var spillOverItemManager: SpillOverItemManager
    private lateinit var consolidationManager: CompensationItemConsolidationManager
    private val generationPeriodInDays = 60L

    private lateinit var newComponentAdjuster: NewComponentAdjuster

    @BeforeEach
    fun setup() {
        itemGenerationManager = mockk()
        itemUpdateManager = mockk()
        cancellationArrearManager = mockk()
        spillOverItemManager = mockk()
        consolidationManager = mockk()

        newComponentAdjuster = NewComponentAdjuster(
            itemGenerationManager,
            itemUpdateManager,
            cancellationArrearManager,
            spillOverItemManager,
            consolidationManager,
            generationPeriodInDays,
        )
    }

    @ParameterizedTest(name = "{index} => {2}")
    @MethodSource("provideContextValidationTestCases")
    fun `should throw exception when context is invalid`(
        context: CompensationUpdateContext,
        exceptionMessage: String,
        testCaseName: String,
    ) {
        val exception = assertThrows<IllegalArgumentException> {
            newComponentAdjuster.adjust(context)
        }
        assertEquals(exceptionMessage, exception.message)
    }

    fun provideContextValidationTestCases(): List<Arguments> = listOf(
        createInvalidOldImageTestCase(),
        createInvalidNewImageTestCase(),
        createInvalidNewRecordTestCase(),
        createInvalidParentUpdateContextTestCase(),
    )

    fun createInvalidOldImageTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withOldImage(mockk())
            .withNewRecord(mockk())
            .build(),
        "Old image should be null.",
        "should throw exception when OldImage is not null",
    )

    fun createInvalidNewImageTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withNewImage(mockk())
            .withNewRecord(mockk())
            .build(),
        "New image should be null.",
        "should throw exception when NewImage is not null",
    )

    fun createInvalidNewRecordTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder().build(),
        "New record should not be null.",
        "should throw exception when NewRecord is null",
    )

    fun createInvalidParentUpdateContextTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withNewRecord(mockk())
            .withParentUpdateContext(mockk())
            .build(),
        "Parent update context should be null.",
        "should throw exception when ParentUpdateContext is not null",
    )

    @Test
    fun `should update context with adjusted items when valid context`() {
        val newRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.now().plusDays(15))
            .withEndDate(null)
            .build()
        val initialContext = buildContext(newRecord)
        val newRecordItems = listOf(
            TestCompensationItemBuilder().withStartDate(
                newRecord.startDate,
            ).withEndDate(newRecord.startDate.plusMonths(1)).build(),
        )

        val toDateSlot = slot<LocalDate>()
        every {
            itemGenerationManager.generateItems(newRecord, capture(toDateSlot))
        } returns newRecordItems

        val updatedContext = newComponentAdjuster.adjust(initialContext)

        verify(exactly = 1) {
            itemGenerationManager.generateItems(newRecord, any<LocalDate>())
        }
        assertEquals(LocalDate.now().plusDays(generationPeriodInDays), toDateSlot.captured)
        assertNotEquals(updatedContext, initialContext)
        assertTrue { updatedContext.adjustedItems.oldImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isNotEmpty() }
    }

    @Test
    fun `should return generated items when newRecord is eligible for item generation`() {
        val newRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.now())
            .withEndDate(null)
            .build()
        val initialContext = buildContext(newRecord)
        val newRecordItems = listOf(
            TestCompensationItemBuilder()
                .withStartDate(newRecord.startDate)
                .withEndDate(newRecord.startDate.plusMonths(1))
                .build(),
        )

        val toDateSlot = slot<LocalDate>()
        every {
            itemGenerationManager.generateItems(newRecord, capture(toDateSlot))
        } returns newRecordItems

        newComponentAdjuster.generateNewRecordItems(initialContext)

        verify(exactly = 1) {
            itemGenerationManager.generateItems(newRecord, any<LocalDate>())
        }
        assertEquals(LocalDate.now().plusDays(generationPeriodInDays), toDateSlot.captured)
    }

    @Test
    fun `generateNewRecordItems should return empty list when new record ineligible for item generation`() {
        val newRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.now().plusDays(65))
            .withEndDate(null)
            .build()
        val initialContext = buildContext(newRecord)

        val generatedItems = newComponentAdjuster.generateNewRecordItems(initialContext)

        verify { itemGenerationManager wasNot Called }
        assertTrue { generatedItems.isEmpty() }
    }

    private fun buildContext(newRecord: Compensation) = TestCompensationUpdateContextBuilder()
        .withNewRecord(newRecord)
        .build()
}
