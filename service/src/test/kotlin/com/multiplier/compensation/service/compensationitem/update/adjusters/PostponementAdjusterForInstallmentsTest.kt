package com.multiplier.compensation.service.compensationitem.update.adjusters

import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.service.compensationitem.generation.CompensationItemGenerationManager
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationItemBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationUpdateContextBuilder
import com.multiplier.compensation.service.compensationitem.update.managers.CancellationArrearManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemConsolidationManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemUpdateManager
import com.multiplier.compensation.service.compensationitem.update.managers.SpillOverItemManager
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isAbortedItem
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isCancellationArrear
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PostponementAdjusterForInstallmentsTest {
    private companion object TestData {
        val oldImage = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 12, 31))
            .withIsInstallment(true)
            .withNoOfInstallments(12)
            .build()
        val newImage1 = oldImage.copy(
            startDate = LocalDate.of(2024, 2, 15),
        )
        val newImage2 = oldImage.copy(
            startDate = LocalDate.of(2024, 2, 1),
        )

        fun installmentItemBuilder() = TestCompensationItemBuilder()
            .withCompensation(oldImage)
    }

    private lateinit var itemGenerationManager: CompensationItemGenerationManager
    private lateinit var itemUpdateManager: CompensationItemUpdateManager
    private lateinit var cancellationArrearManager: CancellationArrearManager
    private lateinit var spillOverItemManager: SpillOverItemManager
    private lateinit var consolidationManager: CompensationItemConsolidationManager
    private val generationPeriodInDays = 60L

    private lateinit var postponementAdjuster: PostponementAdjuster

    @BeforeEach
    fun setup() {
        itemGenerationManager = mockk()
        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns emptyList()
        itemUpdateManager = CompensationItemUpdateManager(
            compensationItemRepository = mockk(),
            transactional = mockk(),
        )
        cancellationArrearManager = CancellationArrearManager()
        spillOverItemManager = SpillOverItemManager()
        consolidationManager = CompensationItemConsolidationManager()

        postponementAdjuster = PostponementAdjuster(
            itemGenerationManager,
            itemUpdateManager,
            cancellationArrearManager,
            spillOverItemManager,
            consolidationManager,
            generationPeriodInDays,
        )
    }

    @Test
    fun `should invalidate all items when dates do not align`() {
        val processedItem1 = installmentItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .withCurrentInstallment(1)
            .build()
        val unprocessedItem1 = installmentItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .withCurrentInstallment(2)
            .build()
        val unprocessedItem2 = installmentItemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withCurrentInstallment(3)
            .build()

        val existingItems = listOf(
            processedItem1,
            unprocessedItem1,
            unprocessedItem2,
        )
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(newImage1)
            .withExistingItems(existingItems)
            .build()

        val updatedContext = postponementAdjuster.adjust(initialContext)

        verify(exactly = 1) {
            itemGenerationManager.generateItems(eq(newImage1), eq(LocalDate.now().plusDays(generationPeriodInDays)))
        }
        assertTrue { initialContext != updatedContext }
        assertTrue { updatedContext.adjustedItems.oldImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isEmpty() }
        assertEquals(3, updatedContext.adjustedItems.oldImageItems.count())

        val oldImageAbortedItems = updatedContext.adjustedItems.oldImageItems.filter { isAbortedItem(it) }
        assertEquals(2, oldImageAbortedItems.count())
        assertEquals(1, oldImageAbortedItems.count { it.id == unprocessedItem1.id })
        assertEquals(1, oldImageAbortedItems.count { it.id == unprocessedItem2.id })

        val oldImageCancellationArrears = updatedContext.adjustedItems.oldImageItems.filter { isCancellationArrear(it) }
        assertEquals(1, oldImageCancellationArrears.count())
        val cancellationArrear = oldImageCancellationArrears
            .firstOrNull { it.arrearOf == processedItem1.id }
        assertNotNull(cancellationArrear)
    }

    @Test
    fun `should invalidate eligible items and reset installment numbers when dates align`() {
        val processedItem1 = installmentItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .withCurrentInstallment(1)
            .build()
        val unprocessedItem1 = installmentItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .withCurrentInstallment(2)
            .build()
        val unprocessedItem2 = installmentItemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withCurrentInstallment(3)
            .build()
        val existingItems = listOf(processedItem1, unprocessedItem1, unprocessedItem2)
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(newImage2)
            .withExistingItems(existingItems)
            .build()

        val updatedContext = postponementAdjuster.adjust(initialContext)

        assertTrue { initialContext != updatedContext }
        assertTrue { updatedContext.adjustedItems.oldImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isEmpty() }
        assertEquals(1, updatedContext.adjustedItems.oldImageItems.count())

        val oldImageAbortedItems = updatedContext.adjustedItems.oldImageItems.filter { isAbortedItem(it) }
        assertTrue { oldImageAbortedItems.isEmpty() }

        val oldImageCancellationArrears = updatedContext.adjustedItems.oldImageItems.filter { isCancellationArrear(it) }
        assertEquals(1, oldImageCancellationArrears.count())
        val cancellationArrear = oldImageCancellationArrears
            .firstOrNull { it.arrearOf == processedItem1.id }
        assertNotNull(cancellationArrear)

        val newImageUsedForItemGenerationSlot = slot<Compensation>()
        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                capture(newImageUsedForItemGenerationSlot),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }
        assertEquals(2, newImageUsedForItemGenerationSlot.captured.generatedInstallments)

        assertEquals(2, updatedContext.adjustedItems.newImageItems.count())
        val updatedUnprocessedItem1 = updatedContext.adjustedItems.newImageItems
            .firstOrNull { it.id == unprocessedItem1.id }
        val updatedUnprocessedItem2 = updatedContext.adjustedItems.newImageItems
            .firstOrNull { it.id == unprocessedItem2.id }
        assertNotNull(updatedUnprocessedItem1)
        assertNotNull(updatedUnprocessedItem2)
        assertTrue { updatedUnprocessedItem1.currentInstallment == 1 }
        assertTrue { updatedUnprocessedItem2.currentInstallment == 2 }
    }
}
