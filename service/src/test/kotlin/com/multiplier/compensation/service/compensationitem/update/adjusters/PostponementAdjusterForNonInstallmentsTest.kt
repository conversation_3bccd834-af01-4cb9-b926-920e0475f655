package com.multiplier.compensation.service.compensationitem.update.adjusters

import com.multiplier.compensation.service.compensationitem.generation.CompensationItemGenerationManager
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationItemBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationUpdateContextBuilder
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.update.managers.CancellationArrearManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemConsolidationManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemUpdateManager
import com.multiplier.compensation.service.compensationitem.update.managers.SpillOverItemManager
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isAbortedItem
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isCancellationArrear
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PostponementAdjusterForNonInstallmentsTest {
    private companion object TestData {
        val nonInstallmentOldImage = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .build()
        val nonInstallmentNewImage = nonInstallmentOldImage.copy(
            startDate = LocalDate.of(2024, 2, 15),
        )
        val newRecord = null

        fun nonInstallmentItemBuilder() = TestCompensationItemBuilder()
            .withCompensation(nonInstallmentOldImage)
    }

    private lateinit var itemGenerationManager: CompensationItemGenerationManager
    private lateinit var itemUpdateManager: CompensationItemUpdateManager
    private lateinit var cancellationArrearManager: CancellationArrearManager
    private lateinit var spillOverItemManager: SpillOverItemManager
    private lateinit var consolidationManager: CompensationItemConsolidationManager
    private val generationPeriodInDays = 60L

    private lateinit var postponementAdjuster: PostponementAdjuster

    @BeforeEach
    fun setup() {
        itemGenerationManager = CompensationItemGenerationManager(
            payScheduleService = mockk(),
            compensationSchemaService = mockk(),
        )

        itemUpdateManager = CompensationItemUpdateManager(
            compensationItemRepository = mockk(),
            transactional = mockk(),
        )
        cancellationArrearManager = CancellationArrearManager()
        spillOverItemManager = SpillOverItemManager()
        consolidationManager = CompensationItemConsolidationManager()

        postponementAdjuster = PostponementAdjuster(
            itemGenerationManager,
            itemUpdateManager,
            cancellationArrearManager,
            spillOverItemManager,
            consolidationManager,
            generationPeriodInDays,
        )
    }

    @ParameterizedTest(name = "{index} => {2}")
    @MethodSource("provideContextValidationTestCases")
    fun `should throw exception when context is invalid`(
        context: CompensationUpdateContext,
        exceptionMessage: String,
        testCaseName: String,
    ) {
        val exception = assertThrows<IllegalArgumentException> {
            postponementAdjuster.adjust(context)
        }
        assertEquals(exceptionMessage, exception.message)
    }

    fun provideContextValidationTestCases(): List<Arguments> = listOf(
        createInvalidOldImageTestCase(),
        createInvalidNewImageTestCase(),
        createInvalidNewRecordTestCase(),
        createInvalidParentContextTestCase(),
    )

    fun createInvalidOldImageTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withNewImage(mockk())
            .build(),
        "Old image should not be null.",
        "should throw exception when OldImage is null",
    )

    fun createInvalidNewImageTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withOldImage(mockk())
            .build(),
        "New image should not be null.",
        "should throw exception when NewImage is null",
    )

    fun createInvalidNewRecordTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withOldImage(mockk())
            .withNewImage(mockk())
            .withNewRecord(mockk())
            .build(),
        "New record should be null.",
        "should throw exception when NewRecord is not null",
    )

    fun createInvalidParentContextTestCase(): Arguments = Arguments.of(
        TestCompensationUpdateContextBuilder()
            .withOldImage(mockk())
            .withNewImage(mockk())
            .withParentUpdateContext(mockk())
            .build(),
        "Parent update context should be null.",
        "should throw exception when ParentUpdateContext is not null",
    )

    @Test
    fun `should invalidate all eligible items for non-installment including a processed spillover item`() {
        val processedItem1 = nonInstallmentItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .build()
        val processedItem2 = nonInstallmentItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .withCutOffDate(LocalDate.of(2024, 2, 15))
            .build()
        val processedItem3 = nonInstallmentItemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withCutOffDate(LocalDate.of(2024, 3, 15))
            .build()
        val unprocessedItem1 = nonInstallmentItemBuilder()
            .withStartDate(LocalDate.of(2024, 4, 1))
            .withEndDate(LocalDate.of(2024, 4, 30))
            .build()

        val existingItems = listOf(
            processedItem1,
            processedItem2,
            processedItem3,
            unprocessedItem1,
        )
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(nonInstallmentOldImage)
            .withNewImage(nonInstallmentNewImage)
            .withNewRecord(newRecord)
            .withExistingItems(existingItems)
            .build()

        val updatedContext = postponementAdjuster.adjust(initialContext)

        assertTrue { initialContext != updatedContext }
        assertTrue { updatedContext.adjustedItems.oldImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isEmpty() }
        assertEquals(2, updatedContext.adjustedItems.oldImageItems.count())

        val oldImageAbortedItems = updatedContext.adjustedItems.oldImageItems.filter { isAbortedItem(it) }
        assertTrue { oldImageAbortedItems.isEmpty() }

        val oldImageCancellationArrears = updatedContext.adjustedItems.oldImageItems.filter { isCancellationArrear(it) }
        assertEquals(2, oldImageCancellationArrears.count())
        val spillOverItemCancellationArrear = oldImageCancellationArrears
            .firstOrNull { it.arrearOf == processedItem2.id }
        val cancellationArrear = oldImageCancellationArrears
            .firstOrNull { it.arrearOf == processedItem1.id }
        assertNotNull(spillOverItemCancellationArrear)
        assertNotNull(cancellationArrear)
        assertEquals(processedItem2.startDate, spillOverItemCancellationArrear.startDate)
        assertEquals(nonInstallmentNewImage.startDate.minusDays(1), spillOverItemCancellationArrear.endDate)
        assertEquals(processedItem1.startDate, cancellationArrear.startDate)
        assertEquals(processedItem1.endDate, cancellationArrear.endDate)
    }

    @Test
    fun `should invalidate all eligible items for non-installment including an unprocessed spillover item`() {
        val processedItem1 = nonInstallmentItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .build()
        val unprocessedItem1 = nonInstallmentItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .build()
        val unprocessedItem2 = nonInstallmentItemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .build()

        val existingItems = listOf(
            processedItem1,
            unprocessedItem1,
            unprocessedItem2,
        )
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(nonInstallmentOldImage)
            .withNewImage(nonInstallmentNewImage)
            .withNewRecord(newRecord)
            .withExistingItems(existingItems)
            .build()

        val updatedContext = postponementAdjuster.adjust(initialContext)

        assertTrue { initialContext != updatedContext }
        assertTrue { updatedContext.adjustedItems.oldImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isEmpty() }
        assertEquals(2, updatedContext.adjustedItems.oldImageItems.count())

        val oldImageAbortedItems = updatedContext.adjustedItems.oldImageItems.filter { isAbortedItem(it) }
        assertEquals(1, oldImageAbortedItems.count())
        assertEquals(unprocessedItem1.id, oldImageAbortedItems[0].id)

        val oldImageCancellationArrears = updatedContext.adjustedItems.oldImageItems.filter { isCancellationArrear(it) }
        assertEquals(1, oldImageCancellationArrears.count())
        val cancellationArrear = oldImageCancellationArrears
            .firstOrNull { it.arrearOf == processedItem1.id }
        assertNotNull(cancellationArrear)
        assertEquals(processedItem1.startDate, cancellationArrear.startDate)
        assertEquals(processedItem1.endDate, cancellationArrear.endDate)

        assertEquals(1, updatedContext.adjustedItems.newImageItems.count())
        val truncatedItem = updatedContext.adjustedItems.newImageItems[0]
        assertEquals(nonInstallmentNewImage.startDate, truncatedItem.startDate)
        assertEquals(unprocessedItem1.endDate, truncatedItem.endDate)
    }
}
