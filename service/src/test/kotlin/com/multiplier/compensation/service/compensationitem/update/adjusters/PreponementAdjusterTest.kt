package com.multiplier.compensation.service.compensationitem.update.adjusters

import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.service.compensationitem.generation.CompensationItemGenerationManager
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.update.managers.CancellationArrearManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemConsolidationManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemUpdateManager
import com.multiplier.compensation.service.compensationitem.update.managers.SpillOverItemManager
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals

class PreponementAdjusterTest {
    private lateinit var itemGenerationManager: CompensationItemGenerationManager
    private lateinit var itemUpdateManager: CompensationItemUpdateManager
    private lateinit var cancellationArrearManager: CancellationArrearManager
    private lateinit var spillOverItemManager: SpillOverItemManager
    private lateinit var consolidationManager: CompensationItemConsolidationManager
    private lateinit var preponementAdjuster: PreponementAdjuster

    @BeforeEach
    fun setUp() {
        itemGenerationManager = mockk()
        itemUpdateManager = mockk()
        cancellationArrearManager = CancellationArrearManager()
        spillOverItemManager = SpillOverItemManager()
        consolidationManager = CompensationItemConsolidationManager()
        preponementAdjuster = PreponementAdjuster(
            itemGenerationManager,
            itemUpdateManager,
            cancellationArrearManager,
            spillOverItemManager,
            consolidationManager,
        )
    }

    @Test
    fun `should throw an exception on invocation`() {
        val updateContext: CompensationUpdateContext = mockk()

        val exception = assertThrows<InvalidArgumentException> {
            preponementAdjuster.adjust(updateContext)
        }
        assertEquals("Attempted to modify the start date to an earlier value.", exception.message)
        assertEquals(ValidationErrorCode.InvalidItemAdjustmentRequest, exception.errorCode)
    }
}
