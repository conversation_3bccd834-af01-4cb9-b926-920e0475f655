package com.multiplier.compensation.service.compensationschema.validation

import com.multiplier.compensation.database.repository.compensationschema.CompensationSchemaRepository
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.compensationschema.CompensationSchema
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.grpc.client.adapter.CompanyServiceAdapter
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensationschema.mapper.CompensationSchemaSkeletonField
import com.multiplier.compensation.service.payschedule.PayScheduleService
import com.multiplier.compensation.service.skeleton.SkeletonService
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class CompensationSchemaContextProviderTest {
    private lateinit var skeletonService: SkeletonService
    private lateinit var companyServiceAdapter: CompanyServiceAdapter
    private lateinit var contextProvider: CompensationSchemaContextProvider
    private lateinit var compensationSchemaRepository: CompensationSchemaRepository
    private lateinit var payScheduleService: PayScheduleService

    @BeforeEach
    fun setUp() {
        skeletonService = mockk()
        companyServiceAdapter = mockk()
        compensationSchemaRepository = mockk()
        payScheduleService = mockk()

        contextProvider =
            CompensationSchemaContextProvider(skeletonService, compensationSchemaRepository, payScheduleService)
    }

    @Test
    fun `getValidationContext should return a valid CompensationSchemaValidationContext`() = runBlocking {
        val entityId = 1L
        val skeleton = mockk<Skeleton>()
        val compensationSchemaList = mutableListOf<CompensationSchema>(mockk())
        val paySchedules = listOf<PaySchedule>()

        every { skeletonService.getSkeleton(entityId, SkeletonType.COMPENSATION_SCHEMA, emptyMap()) } returns skeleton
        every { compensationSchemaRepository.findAllByEntityIds(listOf(entityId), false) } returns
            compensationSchemaList
        every { payScheduleService.getPaySchedules(any(), any(), any(), any()) } returns paySchedules

        val context = contextProvider.getValidationContext(entityId, emptyList())

        assertNotNull(context)
        assertEquals(entityId, context.entityId)
        assertEquals(skeleton, context.skeleton)
        assertEquals(paySchedules, context.paySchedules)
    }

    @Test
    fun `extractTagsFromFirstInputItem should return tags when present in first input item`() {
        val inputItems = listOf(
            ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(CompensationSchemaSkeletonField.TAGS.id, "GLOBAL_PAYROLL"),
                    KeyValuePair("OTHER_FIELD", "other_value"),
                ),
            ),
            ValidationInputItem(
                id = "2",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(CompensationSchemaSkeletonField.TAGS.id, "MULTIPLIER_EOR_OPS_APPROVED"),
                ),
            ),
        )

        val result = contextProvider.extractTagsFromFirstInputItem(inputItems)

        assertEquals("GLOBAL_PAYROLL", result)
    }

    @Test
    fun `extractTagsFromFirstInputItem should return null when tags not present in first input item`() {
        val inputItems = listOf(
            ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    KeyValuePair("OTHER_FIELD", "other_value"),
                ),
            ),
        )

        val result = contextProvider.extractTagsFromFirstInputItem(inputItems)

        assertNull(result)
    }

    @Test
    fun `extractTagsFromFirstInputItem should return null when input items list is empty`() {
        val result = contextProvider.extractTagsFromFirstInputItem(emptyList())

        assertNull(result)
    }

    @Test
    fun `extractCountryCodeFromFirstInputItem should return country code when present in first input item`() {
        val inputItems = listOf(
            ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(CommonSkeletonField.COUNTRY_CODE.id, "USA"),
                    KeyValuePair("OTHER_FIELD", "other_value"),
                ),
            ),
            ValidationInputItem(
                id = "2",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(CommonSkeletonField.COUNTRY_CODE.id, "CAN"),
                ),
            ),
        )

        val result = contextProvider.extractCountryCodeFromFirstInputItem(inputItems)

        assertEquals(CountryCode.USA, result)
    }

    @Test
    fun `extractCountryCodeFromFirstInputItem should return null when country code not present in first input item`() {
        val inputItems = listOf(
            ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    KeyValuePair("OTHER_FIELD", "other_value"),
                ),
            ),
        )

        val result = contextProvider.extractCountryCodeFromFirstInputItem(inputItems)

        assertNull(result)
    }

    @Test
    fun `extractCountryCodeFromFirstInputItem should return null when input items list is empty`() {
        val result = contextProvider.extractCountryCodeFromFirstInputItem(emptyList())

        assertNull(result)
    }

    @Test
    fun `extractCountryCodeFromFirstInputItem should handle case insensitive country codes`() {
        val inputItems = listOf(
            ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(CommonSkeletonField.COUNTRY_CODE.id, "usa"),
                ),
            ),
        )

        val result = contextProvider.extractCountryCodeFromFirstInputItem(inputItems)

        assertEquals(CountryCode.USA, result)
    }

    @Test
    fun `getValidationContext should use country code from first input item when available`() = runBlocking {
        val entityId = 1L
        val skeleton = mockk<Skeleton>()
        val compensationSchemaList = mutableListOf<CompensationSchema>(mockk())
        val paySchedules = listOf<PaySchedule>()

        val inputItems = listOf(
            ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(CommonSkeletonField.COUNTRY_CODE.id, "USA"),
                ),
            ),
        )

        val customParams = mapOf(
            "COUNTRY_CODE" to "CAN", // This should be ignored in favor of input item
        )

        every { skeletonService.getSkeleton(entityId, SkeletonType.COMPENSATION_SCHEMA, customParams) } returns skeleton
        every { compensationSchemaRepository.findAllByEntityIds(listOf(entityId), false) } returns
            compensationSchemaList
        every { payScheduleService.getPaySchedules(entityId, any(), CountryCode.USA, true) } returns paySchedules

        val context = contextProvider.getValidationContext(entityId, inputItems, customParams)

        assertNotNull(context)
        assertEquals(entityId, context.entityId)
        assertEquals(skeleton, context.skeleton)
        assertEquals(paySchedules, context.paySchedules)
    }
}
