package com.multiplier.compensation.service.compensationschema.validation.validators

import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaDraft
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaValidationContext
import io.mockk.mockk
import org.junit.jupiter.api.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class SchemaIsFixedValidatorTest {
    private val validator = SchemaIsFixedValidator()

    @Test
    fun `should pass validation when Contract Allowance is variable for EOR`() {
        val input = createValidationInputItem("1", "CONTRACT_ALLOWANCE", "No", "MULTIPLIER_EOR_OPS_APPROVED")
        val context = mockk<CompensationSchemaValidationContext>()
        val collector = ValidationDataCollector<CompensationSchemaDraft>(
            rowValidationResult = mockk(),
            drafts = mutableMapOf(
                "1" to mockk(),
            ),
            inputPlusDerivedRows = emptyMap(),
        )
        val result = validator.validate(input, context, collector)
        assertTrue(result)
    }

    @Test
    fun `should pass validation when Contract Allowance is Fixed for EOR`() {
        val input = createValidationInputItem("1", "CONTRACT_ALLOWANCE", "Yes", "MULTIPLIER_EOR_OPS_APPROVED")
        val context = mockk<CompensationSchemaValidationContext>()
        val collector = ValidationDataCollector<CompensationSchemaDraft>(
            rowValidationResult = mockk(),
            drafts = mutableMapOf(
                "1" to mockk(),
            ),
            inputPlusDerivedRows = emptyMap(),
        )
        val result = validator.validate(input, context, collector)
        assertTrue(result)
    }

    @Test
    fun `should fail validation when category is not Contract Allowance and is variable for EOR`() {
        val input = createValidationInputItem("1", "CONTRACT_BASE_PAY", "No", "MULTIPLIER_EOR_OPS_APPROVED")

        val context = mockk<CompensationSchemaValidationContext>()
        val collector = ValidationDataCollector<CompensationSchemaDraft>(
            rowValidationResult = mutableMapOf(
                "1" to mutableListOf(
                    CellValidationResult(
                        field = KeyValuePair("IS_FIXED", "No"),
                        type = ValidationResultType.ERROR,
                        message = "Variable type is not supported for this category.",
                    ),
                ),
            ),
            drafts = mutableMapOf(
                "1" to mockk(),
            ),
            inputPlusDerivedRows = emptyMap(),
        )

        val result = validator.validate(input, context, collector)

        assertFalse(result)
    }

    @Test
    fun `should return false when no draft found`() {
        val input = createValidationInputItem("2", "CONTRACT_BASE_PAY", "Yes", "MULTIPLIER_EOR_OPS_APPROVED")
        val collector = ValidationDataCollector<CompensationSchemaDraft>(
            rowValidationResult = mockk(),
            drafts = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )
        val result = validator.validate(input, mockk(), collector)
        assertFalse(result)
    }

    @Test
    fun `should fail validation if tags is GLOBAL PAYROLL and category is variable`() {
        val input = createValidationInputItem("3", "CONTRACT_ALLOWANCE", "No", "GLOBAL_PAYROLL")
        val context = mockk<CompensationSchemaValidationContext>()
        val collector = ValidationDataCollector<CompensationSchemaDraft>(
            rowValidationResult = mutableMapOf(
                "1" to mutableListOf(
                    CellValidationResult(
                        field = KeyValuePair("IS_FIXED", "No"),
                        type = ValidationResultType.ERROR,
                        message = "Variable type is not supported for this category.",
                    ),
                ),
            ),
            drafts = mutableMapOf(
                "3" to mockk(),
            ),
            inputPlusDerivedRows = emptyMap(),
        )
        val result = validator.validate(input, context, collector)
        assertFalse(result)
    }

    @Test
    fun `should pass validation if tags is GLOBAL PAYROLL and category is fixed`() {
        val input = createValidationInputItem("3", "CONTRACT_ALLOWANCE", "Yes", "GLOBAL_PAYROLL")
        val context = mockk<CompensationSchemaValidationContext>()
        val collector = ValidationDataCollector<CompensationSchemaDraft>(
            rowValidationResult = mockk(),
            drafts = mutableMapOf(
                "3" to mockk(),
            ),
            inputPlusDerivedRows = emptyMap(),
        )
        val result = validator.validate(input, context, collector)
        assertTrue(result)
    }

    private fun createValidationInputItem(
        id: String,
        category: String,
        isFixed: String,
        tags: String,
    ): ValidationInputItem {
        val keyValuePairs = mutableListOf<KeyValuePair>()
        keyValuePairs.add(KeyValuePair("CATEGORY_KEY", category))
        keyValuePairs.add(KeyValuePair("IS_FIXED", isFixed))
        keyValuePairs.add(KeyValuePair("TAGS", tags))
        return ValidationInputItem(id = id, fieldKeyValuePairs = keyValuePairs)
    }
}
