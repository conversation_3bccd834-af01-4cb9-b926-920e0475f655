package com.multiplier.compensation.service.payschedule.dto

import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.dto.OperationStatus
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.RowValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class PayScheduleBulkCreationOutputTest {
    @Test
    fun `payScheduleBulkCreationOutput should be created correctly`() {
        val entityId = 12234L
        val status = OperationStatus.SUCCESS
        val rowValidationResult = listOf(
            RowValidationResult(
                id = "12345",
                cellValidationResults = listOf(
                    CellValidationResult(
                        field = KeyValuePair("key1", "value1"),
                        type = ValidationResultType.INFO,
                        message = "Valid",
                    ),
                ),
            ),
        )
        val request = PayScheduleBulkCreationOutput(entityId, status, rowValidationResult)

        assertEquals(entityId, request.entityId)
        assertEquals(status, request.status)
        assertEquals(1, request.rowValidationResults.size)
    }

    @Test
    fun `payScheduleBulkCreationOutput should be created correctly for empty row validation results`() {
        val entityId = 12234L
        val status = OperationStatus.SUCCESS
        val rowValidationResult = emptyList<RowValidationResult>()

        val request = PayScheduleBulkCreationOutput(entityId, status, rowValidationResult)

        assertEquals(entityId, request.entityId)
        assertEquals(status, request.status)
        assertEquals(0, request.rowValidationResults.size)
    }

    @Test
    fun `payScheduleBulkCreationOutput should be created correctly for Failure operation Status`() {
        val entityId = 12234L
        val status = OperationStatus.FAILURE
        val rowValidationResult = listOf(
            RowValidationResult(
                id = "12345",
                cellValidationResults = listOf(
                    CellValidationResult(
                        field = KeyValuePair("key1", "value1"),
                        type = ValidationResultType.ERROR,
                        message = "Invalid",
                    ),
                ),
            ),
        )
        val request = PayScheduleBulkCreationOutput(entityId, status, rowValidationResult)

        assertEquals(entityId, request.entityId)
        assertEquals(status, request.status)
        assertEquals(1, request.rowValidationResults.size)
    }
}
