package com.multiplier.compensation.service.payschedule.schedulegeneration.strategy

import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.impl.monthly.InstallmentMonthlyPayScheduleStrategy
import com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.impl.monthly.NonInstallmentMonthlyPayScheduleStrategy
import com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.impl.quarterly.NonInstallmentQuarterlyPayScheduleStrategy
import com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.impl.semimonthly.InstallmentSemiMonthlyPayScheduleStrategy
import com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.impl.weekly.WeeklyPayScheduleStrategy
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import java.time.LocalDate

class PayScheduleStrategyTest {
    @Test
    fun `estimatePayDate with compensation start date reference`() {
        val startDate = LocalDate.of(2023, 1, 1)
        val endDate = LocalDate.of(2023, 1, 31)
        val payDateReference = PayDateReference.COMPENSATION_START_DATE
        val relativePayDays = 10L
        val expectedPayDate = LocalDate.of(2023, 1, 11)
        val actualPayDate = InstallmentMonthlyPayScheduleStrategy()
            .estimatePayDate(startDate, endDate, payDateReference, relativePayDays)
        Assertions.assertEquals(expectedPayDate, actualPayDate)
    }

    @Test
    fun `estimatePayDate with pay schedule start date reference`() {
        val startDate = LocalDate.of(2023, 1, 1)
        val endDate = LocalDate.of(2023, 1, 14)
        val payDateReference = PayDateReference.PAY_SCHEDULE_START_DATE
        val relativePayDays = 5L
        val expectedPayDate = LocalDate.of(2023, 1, 6)
        val actualPayDate = InstallmentSemiMonthlyPayScheduleStrategy()
            .estimatePayDate(startDate, endDate, payDateReference, relativePayDays)
        Assertions.assertEquals(expectedPayDate, actualPayDate)
    }

    @Test
    fun `estimatePayDate with end date reference`() {
        val startDate = LocalDate.of(2023, 1, 1)
        val endDate = LocalDate.of(2023, 1, 7)
        val payDateReference = PayDateReference.PAY_SCHEDULE_END_DATE
        val relativePayDays = 3L
        val expectedPayDate = LocalDate.of(2023, 1, 10)
        val actualPayDate = WeeklyPayScheduleStrategy()
            .estimatePayDate(startDate, endDate, payDateReference, relativePayDays)
        Assertions.assertEquals(expectedPayDate, actualPayDate)
    }

    @Test
    fun `estimatePayDate with negative relative pay days`() {
        val startDate = LocalDate.of(2023, 1, 1)
        val endDate = LocalDate.of(2023, 1, 31)
        val payDateReference = PayDateReference.PAY_SCHEDULE_END_DATE
        val relativePayDays = -1L
        val expectedPayDate = LocalDate.of(2023, 1, 30)
        val actualPayDate = NonInstallmentMonthlyPayScheduleStrategy()
            .estimatePayDate(startDate, endDate, payDateReference, relativePayDays)
        Assertions.assertEquals(expectedPayDate, actualPayDate)
    }

    @Test
    fun `estimatePayDate with zero relative pay days`() {
        val startDate = LocalDate.of(2023, 1, 1)
        val endDate = LocalDate.of(2023, 3, 31)
        val payDateReference = PayDateReference.PAY_SCHEDULE_END_DATE
        val relativePayDays = 0L
        val actualPayDate = NonInstallmentQuarterlyPayScheduleStrategy()
            .estimatePayDate(startDate, endDate, payDateReference, relativePayDays)
        Assertions.assertEquals(endDate, actualPayDate)
    }
}
