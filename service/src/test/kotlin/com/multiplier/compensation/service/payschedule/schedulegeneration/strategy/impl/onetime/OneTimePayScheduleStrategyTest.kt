package com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.impl.onetime

import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.payschedule.common.verifyIntervals
import com.multiplier.compensation.service.payschedule.schedulegeneration.dto.PayScheduleInterval
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@ExtendWith(MockKExtension::class)
class OneTimePayScheduleStrategyTest {
    private val strategy = OneTimePayScheduleStrategy()

    @Test
    fun `generates pay schedule item for non installment`() {
        val paySchedule = getMockedPaySchedule(
            startDateReference = LocalDate.of(2024, 2, 1),
            endDateReference = LocalDate.of(2024, 2, 1),
            payDateReferenceType = PayDateReference.PAY_SCHEDULE_END_DATE,
            relativePayDays = 1,
            isInstallment = false,
        )

        val intervals = strategy.generate(
            paySchedule = paySchedule,
            from = LocalDate.of(2024, 1, 1),
            to = LocalDate.of(2024, 3, 1),
        )

        verifyIntervals(
            intervals = intervals,
            expectedIntervals = listOf(
                PayScheduleInterval(
                    LocalDate.of(2024, 2, 1),
                    LocalDate.of(2024, 2, 1),
                    LocalDate.of(2024, 2, 2),
                ),
            ),
        )
    }

    @Test
    fun `generates pay schedule item for installment`() {
        val paySchedule = getMockedPaySchedule(
            startDateReference = LocalDate.of(2024, 1, 1),
            endDateReference = LocalDate.of(2024, 1, 1),
            payDateReferenceType = PayDateReference.COMPENSATION_START_DATE,
            relativePayDays = 0,
            isInstallment = true,
        )

        val intervals = strategy.generate(
            paySchedule = paySchedule,
            from = LocalDate.of(2024, 3, 1),
            to = LocalDate.of(2024, 3, 1),
        )

        verifyIntervals(
            intervals = intervals,
            expectedIntervals = listOf(
                PayScheduleInterval(
                    LocalDate.of(2024, 3, 1),
                    LocalDate.of(2024, 3, 1),
                    LocalDate.of(2024, 3, 1),
                ),
            ),
        )
    }

    private fun getMockedPaySchedule(
        startDateReference: LocalDate,
        endDateReference: LocalDate,
        payDateReferenceType: PayDateReference,
        relativePayDays: Long,
        isInstallment: Boolean,
    ) = PaySchedule(
        id = UUID.randomUUID(),
        entityId = 1L,
        companyId = 1L,
        name = "OnneTime-05",
        frequency = PayScheduleFrequency.ONE_TIME,
        configurationScope = ConfigurationScope.COMPANY,
        country = CountryCode.USA,
        startDateReference = startDateReference,
        endDateReference = endDateReference,
        payDateReferenceType = payDateReferenceType,
        relativePayDays = relativePayDays,
        isInstallment = isInstallment,
        isActive = true,
        createdOn = LocalDateTime.now(),
        createdBy = 1,
        updatedOn = LocalDateTime.now(),
        updatedBy = 1,
        label = "One Time Pay Schedule",
    )
}
