package com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.impl.semimonthly

import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.payschedule.common.getExpectedIntervals
import com.multiplier.compensation.service.payschedule.common.verifyIntervals
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@ExtendWith(MockKExtension::class)
class InstallmentSemiMonthlyPayScheduleStrategyTest {
    private val strategy = InstallmentSemiMonthlyPayScheduleStrategy()
    private val sampleDataDirectory =
        "src/test/resources/payschedule/schedulegeneration/sample/semimonthly/installment"

    @Test
    fun `generates intervals when component start date same as month first date`() {
        val paySchedule = getMockedPaySchedule(
            startDateReference = LocalDate.of(2024, 1, 1),
            endDateReference = LocalDate.of(2024, 1, 15),
            payDateReferenceType = PayDateReference.COMPENSATION_START_DATE,
            relativePayDays = 0,
        )

        val intervals = strategy.generate(
            paySchedule = paySchedule,
            from = LocalDate.of(2024, 1, 1),
            to = LocalDate.of(2025, 2, 28),
        )

        verifyIntervals(
            intervals = intervals,
            expectedIntervals = getExpectedIntervals(
                "$sampleDataDirectory/" +
                    "semimonthly_installment_test_1_component_start_date_same_as_month_first_date.csv",
            ),
        )
    }

    @Test
    fun `generates intervals when component start date not same as month first date with leap year edge case`() {
        val paySchedule = getMockedPaySchedule(
            startDateReference = LocalDate.of(2024, 1, 1),
            endDateReference = LocalDate.of(2024, 1, 15),
            payDateReferenceType = PayDateReference.COMPENSATION_START_DATE,
            relativePayDays = 0,
        )

        val intervals = strategy.generate(
            paySchedule = paySchedule,
            from = LocalDate.of(2024, 1, 30),
            to = LocalDate.of(2025, 5, 29),
        )

        verifyIntervals(
            intervals = intervals,
            expectedIntervals = getExpectedIntervals(
                "$sampleDataDirectory/" +
                    "semimonthly_installment_test_2_component_start_date_not_same_as_month_first_date_with_leap_year_edge_case.csv",
            ),
        )
    }

    @Test
    fun `generates intervals when component start date not same as month first date`() {
        val paySchedule = getMockedPaySchedule(
            startDateReference = LocalDate.of(2024, 1, 1),
            endDateReference = LocalDate.of(2024, 1, 15),
            payDateReferenceType = PayDateReference.PAY_SCHEDULE_START_DATE,
            relativePayDays = 0,
        )

        val intervals = strategy.generate(
            paySchedule = paySchedule,
            from = LocalDate.of(2024, 2, 27),
            to = LocalDate.of(2025, 6, 26),
        )

        verifyIntervals(
            intervals = intervals,
            expectedIntervals = getExpectedIntervals(
                "$sampleDataDirectory/" +
                    "semimonthly_installment_test_3_component_start_date_not_same_as_month_first_date.csv",
            ),
        )
    }

    @Test
    fun `generates intervals when component start date not same as month first date with multiple month end edge cases`() {
        val paySchedule = getMockedPaySchedule(
            startDateReference = LocalDate.of(2024, 1, 1),
            endDateReference = LocalDate.of(2024, 1, 15),
            payDateReferenceType = PayDateReference.COMPENSATION_START_DATE,
            relativePayDays = 0,
        )

        val intervals = strategy.generate(
            paySchedule = paySchedule,
            from = LocalDate.of(2024, 1, 31),
            to = LocalDate.of(2025, 5, 30),
        )

        verifyIntervals(
            intervals = intervals,
            expectedIntervals = getExpectedIntervals(
                "$sampleDataDirectory/" +
                    "semimonthly_installment_test_4_component_start_date_not_same_as_month_first_date_with_multiple_month_end_edge_cases.csv",
            ),
        )
    }

    private fun getMockedPaySchedule(
        startDateReference: LocalDate,
        endDateReference: LocalDate,
        payDateReferenceType: PayDateReference,
        relativePayDays: Long,
    ) = PaySchedule(
        id = UUID.randomUUID(),
        entityId = 1L,
        companyId = 1L,
        name = "SemiMonthly-05",
        frequency = PayScheduleFrequency.SEMI_MONTHLY,
        configurationScope = ConfigurationScope.COMPANY,
        country = CountryCode.USA,
        startDateReference = startDateReference,
        endDateReference = endDateReference,
        payDateReferenceType = payDateReferenceType,
        relativePayDays = relativePayDays,
        isInstallment = true,
        isActive = true,
        createdOn = LocalDateTime.now(),
        createdBy = 1,
        updatedOn = LocalDateTime.now(),
        updatedBy = 1,
        label = "Simi Monthly Pay Schedule",
    )
}
