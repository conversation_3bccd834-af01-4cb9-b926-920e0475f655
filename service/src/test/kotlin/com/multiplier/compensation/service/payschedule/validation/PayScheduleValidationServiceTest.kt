package com.multiplier.compensation.service.payschedule.validation

import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.payschedule.dto.PayScheduleBulkValidationInput
import com.multiplier.compensation.service.payschedule.dto.PayScheduleDraft
import com.multiplier.compensation.service.payschedule.dto.PayScheduleValidatorContext
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class PayScheduleValidationServiceTest {
    private lateinit var payScheduleContextProvider: PayScheduleContextProvider
    private lateinit var payScheduleValidationPipeline: PayScheduleValidationPipeline
    private lateinit var payScheduleValidationService: PayScheduleValidationService

    @BeforeEach
    fun setup() {
        payScheduleContextProvider = mockk()
        payScheduleValidationPipeline = mockk()
        payScheduleValidationService = PayScheduleValidationService(
            payScheduleContextProvider,
            payScheduleValidationPipeline,
        )
    }

    @Test
    fun `validate should return ValidationDataCollector when validation is executed`() {
        val entityId = 12345L
        val validationItems = listOf(mockk<ValidationInputItem>())
        val request = PayScheduleBulkValidationInput(
            entityId = entityId,
            items = validationItems,
            customParams = emptyMap(),
        )
        val validationContext = mockk<PayScheduleValidatorContext>()
        val expectedCollector = ValidationDataCollector<PayScheduleDraft>(
            rowValidationResult = mutableMapOf("row1" to mutableListOf(mockk())),
            drafts = mutableMapOf("draft1" to mockk()),
            inputPlusDerivedRows = emptyMap(),
        )
        every { payScheduleContextProvider.getValidationContext(entityId, emptyMap()) } returns validationContext
        every { payScheduleValidationPipeline.execute(validationContext, request.items) } returns expectedCollector

        val actualCollector = payScheduleValidationService.validate(request)

        assertEquals(expectedCollector, actualCollector)

        verify(exactly = 1) { payScheduleContextProvider.getValidationContext(entityId, emptyMap()) }
        verify(exactly = 1) { payScheduleValidationPipeline.execute(validationContext, validationItems) }
    }
}
