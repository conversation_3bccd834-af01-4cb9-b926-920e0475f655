package com.multiplier.compensation.service.payschedule.validation.validators

import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.payschedule.dto.PayScheduleDraft
import com.multiplier.compensation.service.payschedule.dto.PayScheduleValidatorContext
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@ExtendWith(MockKExtension::class)
class PayScheduleReferencePayDateValidatorTest {
    private lateinit var payScheduleReferencePayDateValidator: PayScheduleReferencePayDateValidator

    @BeforeEach
    fun setup() {
        payScheduleReferencePayDateValidator = PayScheduleReferencePayDateValidator()
    }

    @Test
    fun`validate should return false when installment is there and payDateReferenceType is not Compensation start date`() {
        val input = ValidationInputItem(
            id = "12345L",
            fieldKeyValuePairs = listOf(KeyValuePair("key1", "Value1")),
        )
        val context = PayScheduleValidatorContext(entityId = 45678, skeleton = mockk<Skeleton>())
        val collector = ValidationDataCollector<PayScheduleDraft>(
            rowValidationResult = mutableMapOf("row1" to mutableListOf(mockk())),
            drafts = mutableMapOf(
                input.id to PayScheduleDraft(
                    companyId = 12345L,
                    name = "Monthly-05",
                    countryCode = CountryCode.USA,
                    frequency = PayScheduleFrequency.BI_WEEKLY,
                    configurationScope = ConfigurationScope.COMPANY,
                    startDateReference = LocalDate.now(),
                    endDateReference = LocalDate.now(),
                    payDateReferenceType = PayDateReference.PAY_SCHEDULE_START_DATE,
                    isInstallment = true,
                    relativePayDays = -15,
                    label = "Monthly Pay Schedule",
                ),
            ),
            inputPlusDerivedRows = emptyMap(),
        )
        val result = payScheduleReferencePayDateValidator.validate(input, context, collector)
        assertFalse(result)
    }

    @ParameterizedTest(name = "{index} => {2}")
    @MethodSource("provideValidPayDateReferenceTypeTestCase")
    fun `validate should return true for valid PayDateReferenceType`(
        isInstallment: Boolean,
        payDateReferenceType: PayDateReference,
        description: String,
    ) {
        val input = ValidationInputItem(
            id = "12345L",
            fieldKeyValuePairs = listOf(KeyValuePair("key1", "Value1")),
        )
        val context = PayScheduleValidatorContext(entityId = 45678, skeleton = mockk<Skeleton>())
        val collector = ValidationDataCollector<PayScheduleDraft>(
            rowValidationResult = mutableMapOf("row1" to mutableListOf(mockk())),
            drafts = mutableMapOf(
                input.id to PayScheduleDraft(
                    companyId = 12345L,
                    name = "Monthly-05",
                    countryCode = CountryCode.USA,
                    frequency = PayScheduleFrequency.BI_WEEKLY,
                    configurationScope = ConfigurationScope.COMPANY,
                    startDateReference = LocalDate.now(),
                    endDateReference = LocalDate.now(),
                    payDateReferenceType = payDateReferenceType,
                    isInstallment = isInstallment,
                    relativePayDays = -15,
                    label = "Monthly Pay Schedule",
                ),
            ),
            inputPlusDerivedRows = emptyMap(),
        )
        val result = payScheduleReferencePayDateValidator.validate(input, context, collector)
        assertTrue(result)
    }

    @ParameterizedTest(name = "{index} => {2}")
    @MethodSource("provideInvalidPayDateReferenceTypeTestCase")
    fun `validate should return false for invalid payDateReference`(
        isInstallment: Boolean,
        payDateReferenceType: PayDateReference,
        description: String,
    ) {
        val input = ValidationInputItem(
            id = "12345L",
            fieldKeyValuePairs = listOf(KeyValuePair("key1", "Value1")),
        )
        val context = PayScheduleValidatorContext(entityId = 45678, skeleton = mockk<Skeleton>())
        val collector = ValidationDataCollector<PayScheduleDraft>(
            rowValidationResult = mutableMapOf("row1" to mutableListOf(mockk())),
            drafts = mutableMapOf(
                input.id to PayScheduleDraft(
                    companyId = 12345L,
                    name = "Monthly-05",
                    countryCode = CountryCode.USA,
                    frequency = PayScheduleFrequency.BI_WEEKLY,
                    configurationScope = ConfigurationScope.COMPANY,
                    startDateReference = LocalDate.now(),
                    endDateReference = LocalDate.now(),
                    payDateReferenceType = payDateReferenceType,
                    isInstallment = isInstallment,
                    relativePayDays = -15,
                    label = "Monthly Pay Schedule",
                ),
            ),
            inputPlusDerivedRows = emptyMap(),
        )
        val result = payScheduleReferencePayDateValidator.validate(input, context, collector)
        assertFalse(result)
    }

    @Test
    fun`validate should return false when draft is not found in collector`() {
        val input = ValidationInputItem(
            id = "12345L",
            fieldKeyValuePairs = listOf(KeyValuePair("key1", "Value1")),
        )
        val collector = mockk<ValidationDataCollector<PayScheduleDraft>>()
        every { collector.drafts[input.id] } returns null
        val context = PayScheduleValidatorContext(entityId = 45678, skeleton = mockk<Skeleton>())

        val result = payScheduleReferencePayDateValidator.validate(input, context, collector)
        assertFalse(result)
    }

    @Test
    fun`validate should return false when payDateReferenceType is not found`() {
        val input = ValidationInputItem(
            id = "12345L",
            fieldKeyValuePairs = listOf(KeyValuePair("key1", "Value1")),
        )
        val collector = ValidationDataCollector<PayScheduleDraft>(
            rowValidationResult = mutableMapOf("row1" to mutableListOf(mockk())),
            drafts = mutableMapOf(input.id to mockk()),
            inputPlusDerivedRows = emptyMap(),
        )
        val context = mockk<PayScheduleValidatorContext>()
        val draft = collector.drafts[input.id]
        every { draft!!.payDateReferenceType } returns null
        val result = payScheduleReferencePayDateValidator.validate(input, context, collector)

        assertFalse(result)
    }

    @Test
    fun`validate should return false when installment is null`() {
        val input = ValidationInputItem(
            id = "12345L",
            fieldKeyValuePairs = listOf(KeyValuePair("key1", "Value1")),
        )
        val collector = ValidationDataCollector<PayScheduleDraft>(
            rowValidationResult = mutableMapOf("row1" to mutableListOf(mockk())),
            drafts = mutableMapOf(input.id to mockk()),
            inputPlusDerivedRows = emptyMap(),
        )
        val context = mockk<PayScheduleValidatorContext>()
        val draft = collector.drafts[input.id]
        val payDateReferenceType = PayDateReference.PAY_SCHEDULE_START_DATE
        every { draft!!.payDateReferenceType } returns payDateReferenceType
        every { draft!!.isInstallment } returns null

        val result = payScheduleReferencePayDateValidator.validate(input, context, collector)

        assertFalse(result)
    }

    private companion object {
        @JvmStatic
        fun provideInvalidPayDateReferenceTypeTestCase(): List<Arguments> = listOf(
            createInstallmentPayScheduleStartDateTestCase(),
            createInstallmentPayScheduleEndDateTestCase(),
            createInstallmentCompensationEndDateTestCase(),
            createNonInstallmentCompensationEndDateTestCase(),
        )

        @JvmStatic
        fun provideValidPayDateReferenceTypeTestCase(): List<Arguments> = listOf(
            createInstallmentCompensationStartDateTestCase(),
            createNonInstallmentPayScheduleStartDateTestCase(),
            createNonInstallmentPayScheduleEndDateTestCase(),
            createNonInstallmentCompensationStartDateTestCase(),
        )

        private fun createInstallmentPayScheduleStartDateTestCase(): Arguments = Arguments.of(
            true,
            PayDateReference.PAY_SCHEDULE_START_DATE,
            "Installment with Pay Schedule Start Date",
        )

        private fun createInstallmentCompensationEndDateTestCase(): Arguments = Arguments.of(
            true,
            PayDateReference.COMPENSATION_END_DATE,
            "Installment with Pay Schedule End Date",
        )

        private fun createNonInstallmentCompensationStartDateTestCase(): Arguments = Arguments.of(
            false,
            PayDateReference.COMPENSATION_START_DATE,
            "Non Installment with Compensation Start Date",
        )

        private fun createNonInstallmentCompensationEndDateTestCase(): Arguments = Arguments.of(
            false,
            PayDateReference.COMPENSATION_END_DATE,
            "Non Installment with Compensation End Date",
        )

        private fun createInstallmentPayScheduleEndDateTestCase(): Arguments = Arguments.of(
            true,
            PayDateReference.PAY_SCHEDULE_END_DATE,
            "Installment with Pay Schedule End Date",
        )

        private fun createInstallmentCompensationStartDateTestCase(): Arguments = Arguments.of(
            true,
            PayDateReference.COMPENSATION_START_DATE,
            "Installment with Compensation Start Date",
        )

        private fun createNonInstallmentPayScheduleStartDateTestCase(): Arguments = Arguments.of(
            false,
            PayDateReference.PAY_SCHEDULE_START_DATE,
            "Non Installment with PaySchedule Start Date",
        )

        private fun createNonInstallmentPayScheduleEndDateTestCase(): Arguments = Arguments.of(
            false,
            PayDateReference.PAY_SCHEDULE_END_DATE,
            "Non Installment with PaySchedule End Date",
        )
    }
}
