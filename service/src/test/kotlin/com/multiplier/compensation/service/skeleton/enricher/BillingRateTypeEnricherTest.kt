package com.multiplier.compensation.service.skeleton.enricher

import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.service.common.bulkplatform.BulkJsonCustomParamsUtil
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.skeleton.structure.SkeletonStructureData
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class BillingRateTypeEnricherTest {
    private lateinit var enricher: BillingRateTypeEnricher
    private val skeletonType = SkeletonType.COMPENSATION_SCHEMA
    private val entityId = 123L
    private val skeletonData = mockk<SkeletonStructureData>()

    @BeforeEach
    fun setUp() {
        enricher = BillingRateTypeEnricher()
        mockkObject(BulkJsonCustomParamsUtil)
    }

    @Test
    fun `should return VALUE description and BASE_PAY_PERCENTAGE description when offering type is EOR`() {
        // Given
        val customParams = mapOf("OFFERING_TYPE" to "EOR")
        every { BulkJsonCustomParamsUtil.getOfferingType(customParams) } returns OfferingType.EOR

        // When
        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        // Then
        assertEquals(2, result.size)
        assertTrue(result.contains(BillingRateType.VALUE.description))
        assertTrue(result.contains(BillingRateType.BASE_PAY_PERCENTAGE.description))
        assertEquals(listOf("Value", "% of Base Pay"), result)
    }

    @Test
    fun `should return VALUE name when offering type is GLOBAL_PAYROLL`() {
        // Given
        val customParams = mapOf("OFFERING_TYPE" to "GLOBAL_PAYROLL")
        every { BulkJsonCustomParamsUtil.getOfferingType(customParams) } returns OfferingType.GLOBAL_PAYROLL

        // When
        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        // Then
        assertEquals(1, result.size)
        assertEquals(listOf(BillingRateType.VALUE.description), result)
        assertEquals(listOf("Value"), result)
    }

    @Test
    fun `should return VALUE name when offering type is null (defaults to GLOBAL_PAYROLL)`() {
        // Given
        val customParams = emptyMap<String, String>()
        every { BulkJsonCustomParamsUtil.getOfferingType(customParams) } returns OfferingType.GLOBAL_PAYROLL

        // When
        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        // Then
        assertEquals(1, result.size)
        assertEquals(listOf("Value"), result)
    }

    @Test
    fun `should handle empty custom params map`() {
        // Given
        val customParams = emptyMap<String, String>()
        every { BulkJsonCustomParamsUtil.getOfferingType(customParams) } returns OfferingType.GLOBAL_PAYROLL

        // When
        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        // Then
        assertEquals(1, result.size)
        assertEquals(listOf("Value"), result)
    }

    @Test
    fun `should work with different skeleton types`() {
        // Given
        val customParams = mapOf("OFFERING_TYPE" to "EOR")
        every { BulkJsonCustomParamsUtil.getOfferingType(customParams) } returns OfferingType.EOR

        // When - Test with different skeleton types
        val resultCompensationSchema = enricher.enrich(
            SkeletonType.COMPENSATION_SCHEMA,
            entityId,
            customParams,
            skeletonData,
        )
        val resultCompensationSetup = enricher.enrich(
            SkeletonType.COMPENSATION_SETUP,
            entityId,
            customParams,
            skeletonData,
        )

        // Then - Both should return the same result since skeleton type doesn't affect the logic
        assertEquals(resultCompensationSchema, resultCompensationSetup)
        assertEquals(2, resultCompensationSchema.size)
        assertTrue(resultCompensationSchema.contains("Value"))
        assertTrue(resultCompensationSchema.contains("% of Base Pay"))
    }

    @Test
    fun `should verify billing rate type enum values are correctly used`() {
        // Given
        val customParams = mapOf("OFFERING_TYPE" to "EOR")
        every { BulkJsonCustomParamsUtil.getOfferingType(customParams) } returns OfferingType.EOR

        // When
        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        // Then - Verify the actual enum values are used correctly
        assertEquals(BillingRateType.VALUE.description, "Value")
        assertEquals(BillingRateType.BASE_PAY_PERCENTAGE.description, "% of Base Pay")
        assertEquals(BillingRateType.VALUE.name, "VALUE")

        assertTrue(result.contains(BillingRateType.VALUE.description))
        assertTrue(result.contains(BillingRateType.BASE_PAY_PERCENTAGE.description))
    }
}
