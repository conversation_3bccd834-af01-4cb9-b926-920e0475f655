package com.multiplier.compensation.service.skeleton.enricher

import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE
import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_CONTRACT_BASE_PAY
import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_ADDITIONAL
import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP
import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_EMPLOYEE_CONTRIBUTION
import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_EMPLOYEE_DEDUCTION
import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_EMPLOYER_CONTRIBUTION
import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_EMPLOYER_DEDUCTION
import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_MONTH_PAY_13TH_14TH
import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_PAY_SUPPLEMENT
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.domain.skeleton.enums.ValueType
import com.multiplier.compensation.grpc.client.adapter.PayrollSchemaServiceAdapter
import com.multiplier.compensation.service.skeleton.structure.SkeletonStructureData
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class EntityCategoryEnricherTest {
    private lateinit var payrollSchemaServiceAdapter: PayrollSchemaServiceAdapter
    private lateinit var enricher: EntityCategoryEnricher

    private val entityId = 1001L
    private val skeletonData = SkeletonStructureData(
        fieldId = "field_1",
        fieldName = "Test Field",
        valueType = ValueType.STRING,
        description = "Test field description",
        mandatory = false,
        possibleValues = null,
        validationRegex = null,
        defaultValue = null,
    )

    @BeforeEach
    fun setup() {
        payrollSchemaServiceAdapter = mockk()
        enricher = EntityCategoryEnricher(payrollSchemaServiceAdapter)
    }

    @Test
    fun `should return filtered categories when offering type is GLOBAL_PAYROLL`() {
        val skeletonType = SkeletonType.COMPENSATION_SCHEMA
        val customParams = mapOf("OFFERING_TYPE" to "GLOBAL_PAYROLL")

        val filteredCategories = listOf(
            CATEGORY_CONTRACT_BASE_PAY_ADDITIONAL,
            CATEGORY_CONTRACT_BASE_PAY,
            CATEGORY_CONTRACT_BASE_PAY_BREAKUP,
            CATEGORY_MONTH_PAY_13TH_14TH,
            CATEGORY_CONTRACT_ALLOWANCE,
            CATEGORY_EMPLOYER_CONTRIBUTION,
            CATEGORY_EMPLOYEE_CONTRIBUTION,
            CATEGORY_PAY_SUPPLEMENT,
            CATEGORY_EMPLOYEE_DEDUCTION,
            CATEGORY_EMPLOYER_DEDUCTION,
        )

        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        assertEquals(filteredCategories, result)
    }

    @Test
    fun `should return original categories when offering type is EOR`() {
        val skeletonType = SkeletonType.COMPENSATION_SCHEMA
        val customParams = mapOf("OFFERING_TYPE" to "EOR")
        val originalCategories = skeletonType.compensationCategories

        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        assertEquals(originalCategories, result)
    }

    @Test
    fun `should default to GLOBAL_PAYROLL behavior when custom params are empty`() {
        val skeletonType = SkeletonType.COMPENSATION_REVISION
        val customParams = emptyMap<String, String>()
        val filteredCategories = listOf(
            CATEGORY_CONTRACT_BASE_PAY_ADDITIONAL,
            CATEGORY_CONTRACT_BASE_PAY,
            CATEGORY_CONTRACT_BASE_PAY_BREAKUP,
            CATEGORY_MONTH_PAY_13TH_14TH,
            CATEGORY_CONTRACT_ALLOWANCE,
            CATEGORY_EMPLOYER_CONTRIBUTION,
            CATEGORY_EMPLOYEE_CONTRIBUTION,
        )

        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        assertEquals(filteredCategories, result)
    }

    @Test
    fun `should handle COMPENSATION_SETUP skeleton type with GLOBAL_PAYROLL`() {
        val skeletonType = SkeletonType.COMPENSATION_SETUP
        val customParams = mapOf("OFFERING_TYPE" to "GLOBAL_PAYROLL")
        val expectedFilteredCategories = listOf(
            CATEGORY_CONTRACT_BASE_PAY_ADDITIONAL,
            CATEGORY_CONTRACT_BASE_PAY,
            CATEGORY_CONTRACT_BASE_PAY_BREAKUP,
            CATEGORY_MONTH_PAY_13TH_14TH,
            CATEGORY_CONTRACT_ALLOWANCE,
            CATEGORY_EMPLOYER_CONTRIBUTION,
            CATEGORY_EMPLOYEE_CONTRIBUTION,
        )

        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        assertEquals(expectedFilteredCategories, result)
    }

    @Test
    fun `should handle COMPENSATION_SCHEMA skeleton type with EOR`() {
        val skeletonType = SkeletonType.COMPENSATION_SCHEMA
        val customParams = mapOf("OFFERING_TYPE" to "EOR")
        val expectedCategories = skeletonType.compensationCategories

        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        assertEquals(expectedCategories, result)
        assertEquals(12, result.size)
    }

    @Test
    fun `should handle PAY_SCHEDULE skeleton type with empty categories`() {
        val skeletonType = SkeletonType.PAY_SCHEDULE
        val customParams = mapOf("OFFERING_TYPE" to "EOR")
        val expectedCategories = emptyList<String>()

        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)
        assertEquals(expectedCategories, result)
    }

    @Test
    fun `should handle DEDUCTION skeleton type with GLOBAL_PAYROLL`() {
        val skeletonType = SkeletonType.DEDUCTION
        val customParams = mapOf("OFFERING_TYPE" to "GLOBAL_PAYROLL")
        val filteredCategories = listOf("EMPLOYEE_DEDUCTION", "EMPLOYER_DEDUCTION")

        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        assertEquals(filteredCategories, result)
    }

    @Test
    fun `should handle case insensitive offering type`() {
        val skeletonType = SkeletonType.COMPENSATION_SETUP
        val customParams = mapOf("OFFERING_TYPE" to "eor")
        val expectedCategories = skeletonType.compensationCategories

        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        assertEquals(expectedCategories, result)
    }
}
