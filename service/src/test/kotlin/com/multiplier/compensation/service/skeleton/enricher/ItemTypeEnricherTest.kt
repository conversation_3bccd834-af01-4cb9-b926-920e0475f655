package com.multiplier.compensation.service.skeleton.enricher

import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.service.common.bulkplatform.BulkJsonCustomParamsUtil
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.skeleton.structure.SkeletonStructureData
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ItemTypeEnricherTest {
    private lateinit var enricher: ItemTypeEnricher
    private val skeletonType = SkeletonType.COMPENSATION_SCHEMA
    private val entityId = 123L
    private val skeletonData = mockk<SkeletonStructureData>()

    @BeforeEach
    fun setUp() {
        enricher = ItemTypeEnricher()
        mockkObject(BulkJsonCustomParamsUtil)
    }

    @Test
    fun `should return all item types when offering type is EOR`() {
        // Given
        val customParams = mapOf("OFFERING_TYPE" to "EOR")
        every { BulkJsonCustomParamsUtil.getOfferingType(customParams) } returns OfferingType.EOR

        // When
        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        // Then
        val expectedItemTypes = ItemType.entries.map { it.name }
        assertEquals(expectedItemTypes.size, result.size)
        assertTrue(result.containsAll(expectedItemTypes))
        assertTrue(result.contains("FIXED"))
        assertTrue(result.contains("CALCULATED"))
        assertTrue(result.contains("INPUT"))
    }

    @Test
    fun `should return only INPUT item type when offering type is GLOBAL_PAYROLL`() {
        // Given
        val customParams = mapOf("OFFERING_TYPE" to "GLOBAL_PAYROLL")
        every { BulkJsonCustomParamsUtil.getOfferingType(customParams) } returns OfferingType.GLOBAL_PAYROLL

        // When
        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        // Then
        assertEquals(1, result.size)
        assertEquals(listOf("INPUT"), result)
    }

    @Test
    fun `should return only INPUT item type when offering type is null (defaults to GLOBAL_PAYROLL)`() {
        // Given
        val customParams = emptyMap<String, String>()
        every { BulkJsonCustomParamsUtil.getOfferingType(customParams) } returns OfferingType.GLOBAL_PAYROLL

        // When
        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        // Then
        assertEquals(1, result.size)
        assertEquals(listOf("INPUT"), result)
    }

    @Test
    fun `should handle empty custom params map`() {
        // Given
        val customParams = emptyMap<String, String>()
        every { BulkJsonCustomParamsUtil.getOfferingType(customParams) } returns OfferingType.GLOBAL_PAYROLL

        // When
        val result = enricher.enrich(skeletonType, entityId, customParams, skeletonData)

        // Then
        assertEquals(1, result.size)
        assertEquals(listOf("INPUT"), result)
    }

    @Test
    fun `should work with different skeleton types`() {
        // Given
        val customParams = mapOf("OFFERING_TYPE" to "EOR")
        every { BulkJsonCustomParamsUtil.getOfferingType(customParams) } returns OfferingType.EOR

        // When - Test with different skeleton types
        val resultCompensationSchema = enricher.enrich(
            SkeletonType.COMPENSATION_SCHEMA,
            entityId,
            customParams,
            skeletonData,
        )
        val resultCompensationSetup = enricher.enrich(
            SkeletonType.COMPENSATION_SETUP,
            entityId,
            customParams,
            skeletonData,
        )
        val resultPaySchedule = enricher.enrich(SkeletonType.PAY_SCHEDULE, entityId, customParams, skeletonData)

        // Then - All should return the same result since skeleton type doesn't affect the logic
        val expectedItemTypes = ItemType.entries.map { it.name }
        assertEquals(expectedItemTypes, resultCompensationSchema)
        assertEquals(expectedItemTypes, resultCompensationSetup)
        assertEquals(expectedItemTypes, resultPaySchedule)
    }
}
