package com.multiplier.compensation.service.validation.common

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.domain.skeleton.SkeletonData
import com.multiplier.compensation.domain.skeleton.ValidationRegex
import com.multiplier.compensation.domain.skeleton.enums.ValueType
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.common.validators.SkeletonValidator
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@ExtendWith(MockKExtension::class)
class SkeletonValidatorTest {
    private lateinit var skeletonValidator: SkeletonValidator

    private lateinit var context: CompensationValidatorContext
    private lateinit var item: ValidationInputItem

    @BeforeEach
    fun setUp() {
        skeletonValidator = SkeletonValidator()

        context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = Skeleton(
                data = listOf(
                    SkeletonData(
                        fieldName = "Component Name",
                        fieldId = CompensationSkeletonField.COMPONENT_NAME.id,
                        description = "Component Name",
                        valueType = ValueType.STRING,
                        mandatory = true,
                        possibleValues = listOf("Base Pay", "Meal Allowance"),
                        defaultValue = null,
                        validationRegex = ValidationRegex(
                            "^(Base Pay|Meal Allowance)$",
                            "Should be either Base Pay or Meal Allowance",
                        ),
                    ),
                    SkeletonData(
                        fieldName = "Contract ID",
                        fieldId = CommonSkeletonField.CONTRACT_ID.id,
                        description = "Contract ID",
                        valueType = ValueType.INTEGER,
                        mandatory = false,
                        possibleValues = null,
                        defaultValue = null,
                        validationRegex = null,
                    ),
                    SkeletonData(
                        fieldName = "Start Date",
                        fieldId = CompensationSkeletonField.START_DATE.id,
                        description = "Start date of the component.",
                        valueType = ValueType.DATE,
                        mandatory = true,
                        possibleValues = null,
                        defaultValue = null,
                        validationRegex = null,
                    ),
                    SkeletonData(
                        fieldName = "Pay schedule name",
                        fieldId = CompensationSkeletonField.PAY_SCHEDULE_NAME.id,
                        description = "Payment schedule name.",
                        valueType = ValueType.STRING,
                        mandatory = true,
                        possibleValues = null,
                        defaultValue = null,
                        validationRegex = ValidationRegex(
                            "^(MONTHLY|SEMI_MONTHLY)-\\d+\$",
                            "Date should be in <Frequency>-<Number> format",
                        ),
                    ),
                    SkeletonData(
                        fieldName = "Pay schedule name",
                        fieldId = CompensationSkeletonField.PAY_SCHEDULE_NAME.id,
                        description = "Payment schedule name.",
                        valueType = ValueType.STRING,
                        mandatory = true,
                        possibleValues = null,
                        defaultValue = null,
                        validationRegex = ValidationRegex(
                            "^(MONTHLY|SEMI_MONTHLY)-\\d+\$",
                            "Date should be in <Frequency>-<Number> format",
                        ),
                    ),
                    SkeletonData(
                        fieldName = "Billing rate",
                        fieldId = CompensationSkeletonField.BILLING_RATE.id,
                        description = "Billing rate of the component.",
                        valueType = ValueType.DOUBLE,
                        mandatory = false,
                        possibleValues = null,
                        defaultValue = null,
                        validationRegex = null,
                    ),
                ),
                keys = emptyList(),
            ),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                50000L to Contract(
                    id = 50000L,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ONBOARDING,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "CA",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        item = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CompensationSkeletonField.COMPONENT_NAME.id,
                    value = "Meal Allowance",
                ),
                KeyValuePair(
                    key = CommonSkeletonField.CONTRACT_ID.id,
                    value = "50000",
                ),
                KeyValuePair(
                    key = CompensationSkeletonField.START_DATE.id,
                    value = "2024-12-01",
                ),
                KeyValuePair(
                    key = CompensationSkeletonField.PAY_SCHEDULE_NAME.id,
                    value = "MONTHLY-05",
                ),
            ),
        )
    }

    @Test
    fun `should validate a valid input successfully`() {
        val result = skeletonValidator.validate(input = item, skeleton = context.skeleton)

        assertTrue { result.isEmpty() }
    }

    @Test
    fun `should skip other skeleton validations on invalid input fieldKey`() {
        val invalidField = KeyValuePair(
            key = "INVALID_COMPONENT_NAME",
            value = "Meal Allowance",
        )
        item = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = item.fieldKeyValuePairs + invalidField,
        )

        val result = skeletonValidator.validate(input = item, skeleton = context.skeleton)

        assertTrue { result.isEmpty() }
    }

    @Test
    fun `should validate an invalid input fieldValue and populate results`() {
        val invalidField = KeyValuePair(
            key = CompensationSkeletonField.COMPONENT_NAME.id,
            value = "Invalid Meal Allowance",
        )
        item = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = item.fieldKeyValuePairs + invalidField,
        )

        val result = skeletonValidator.validate(input = item, skeleton = context.skeleton)

        val expected = CellValidationResult(
            field = invalidField,
            type = ValidationResultType.ERROR,
            message = "${invalidField.value} is not a valid value for Component Name",
        )

        assertTrue { result.isNotEmpty() }
        assertEquals(expected = expected, actual = result[0])
    }

    @Test
    fun `should validate no missing skeleton fields in the input`() {
        item = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CompensationSkeletonField.COMPONENT_NAME.id,
                    value = "Meal Allowance",
                ),
            ),
        )

        val result = skeletonValidator.validate(input = item, skeleton = context.skeleton)

        val expected = CellValidationResult(
            field = KeyValuePair(
                key = CompensationSkeletonField.START_DATE.id,
                value = null,
            ),
            type = ValidationResultType.ERROR,
            message = "Start Date is missing",
        )

        assertTrue { result.isNotEmpty() }
        assertEquals(expected = expected, actual = result[0])
    }

    @Test
    fun `should validate double data types not matching in the input`() {
        val invalidField = KeyValuePair(
            key = "BILLING_RATE",
            value = "1+1",
        )
        item = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = item.fieldKeyValuePairs + invalidField,
        )

        val result = skeletonValidator.validate(input = item, skeleton = context.skeleton)

        val expected = CellValidationResult(
            field = invalidField,
            type = ValidationResultType.ERROR,
            message = "${invalidField.value} is not a valid data type format for Billing Rate. Expected format: any " +
                "decimal number including scientific notation",
        )

        assertTrue { result.isNotEmpty() }
        assertEquals(expected = expected, actual = result[0])
    }

    @Test
    fun `should validate date data types not matching in the input`() {
        val invalidField = KeyValuePair(
            key = "START_DATE",
            value = "01/12/2024",
        )
        item = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = item.fieldKeyValuePairs + invalidField,
        )

        val result = skeletonValidator.validate(input = item, skeleton = context.skeleton)

        val expected = CellValidationResult(
            field = invalidField,
            type = ValidationResultType.ERROR,
            message = "${invalidField.value} is not a valid data type format for Start Date. Expected format: " +
                "YYYY-MM-DD",
        )

        assertTrue { result.isNotEmpty() }
        assertEquals(expected = expected, actual = result[0])
    }

    @Test
    fun `should validate regex not matching in the input`() {
        val invalidField = KeyValuePair(
            key = CompensationSkeletonField.PAY_SCHEDULE_NAME.id,
            value = "TwiceMonthly-A",
        )
        item = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = item.fieldKeyValuePairs + invalidField,
        )

        val result = skeletonValidator.validate(input = item, skeleton = context.skeleton)

        val expected = CellValidationResult(
            field = invalidField,
            type = ValidationResultType.ERROR,
            message = "${invalidField.value} is not a valid value for Pay Schedule Name. " +
                "Date should be in <Frequency>-<Number> format",
        )

        assertTrue { result.isNotEmpty() }
        assertEquals(expected = expected, actual = result[0])
    }

    @Test
    fun `should validate mandatory fields not passed in the input`() {
        val updatedSkeleton = context.skeleton.copy(
            data = context.skeleton.data.plus(
                listOf(
                    SkeletonData(
                        fieldName = "Is Installment?",
                        fieldId = "IS_INSTALLMENT",
                        description = "Flag indicating whether component is an installment.",
                        valueType = ValueType.BOOLEAN,
                        mandatory = true,
                        possibleValues = null,
                        defaultValue = null,
                        validationRegex = null,
                    ),
                    SkeletonData(
                        fieldName = "Number of installments",
                        fieldId = "NUMBER_OF_INSTALLMENTS",
                        description = "Number of installments to be paid.",
                        valueType = ValueType.INTEGER,
                        mandatory = false,
                        possibleValues = null,
                        defaultValue = null,
                        validationRegex = null,
                    ),
                ),
            ),
        )

        val invalidKeys = listOf(
            KeyValuePair(
                key = "IS_INSTALLMENT",
                value = null,
            ),
            KeyValuePair(
                key = "NUMBER_OF_INSTALLMENTS",
                value = null,
            ),
        )

        item = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = item.fieldKeyValuePairs + invalidKeys,
        )

        val result = skeletonValidator.validate(input = item, skeleton = updatedSkeleton)

        val expected = CellValidationResult(
            field = KeyValuePair(
                key = "IS_INSTALLMENT",
                value = null,
            ),
            type = ValidationResultType.ERROR,
            message = "Is Installment? is a mandatory field and should not be null.",
        )

        assertTrue { result.isNotEmpty() }
        assertEquals(expected = expected, actual = result[0])
    }

    @Test
    fun `should not throw error for non-mandatory field with null value`() {
        val updatedSkeleton = context.skeleton.copy(
            data = context.skeleton.data.plus(
                listOf(
                    SkeletonData(
                        fieldName = "Reason Code",
                        fieldId = "REASON_CODE",
                        description = "Code indicating the reason for a particular action.",
                        valueType = ValueType.STRING,
                        mandatory = false,
                        possibleValues = listOf("CODE_A", "CODE_B", "CODE_C"),
                        defaultValue = null,
                        validationRegex = null,
                    ),
                ),
            ),
        )

        val reasonCodeKeyValuePair = KeyValuePair(
            key = "REASON_CODE",
            value = null,
        )

        item = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = item.fieldKeyValuePairs + reasonCodeKeyValuePair,
        )

        val result = skeletonValidator.validate(input = item, skeleton = updatedSkeleton)
        assertTrue { result.isEmpty() }
    }

    @Test
    fun `should validate scientific notation billing rate format successfully`() {
        val scientificNotationField = KeyValuePair(
            key = CompensationSkeletonField.BILLING_RATE.id,
            value = "9.9974076E8",
        )
        item = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = item.fieldKeyValuePairs + scientificNotationField,
        )

        val result = skeletonValidator.validate(input = item, skeleton = context.skeleton)

        assertTrue { result.isEmpty() }
    }
}
