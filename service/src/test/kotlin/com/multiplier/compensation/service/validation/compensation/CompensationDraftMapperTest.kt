package com.multiplier.compensation.service.validation.compensation

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationschema.CompensationSchema
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.common.mapper.SkeletonCompensationMapperUtils.toCompensationDraft
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.mapper.toBackFilledCompensation
import com.multiplier.compensation.service.compensation.mapper.toCompensation
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.rowItemFixture
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class CompensationDraftMapperTest {
    @Test
    fun `test mapToCompensationDraft`() {
        val validationInputItem = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = rowItemFixture().keyValuePairs,
        )
        val schemaItemId = UUID.randomUUID()
        val payScheduleId = UUID.randomUUID()

        val schemaItems = mapOf(
            "COMPONENT_1" to mockk<CompensationSchemaItem> {
                every { id } returns schemaItemId
                every { category } returns "CATEGORY"
                every { componentName } returns "COMPONENT_1"
            },
        )

        val paySchedules = mapOf(
            "SCHEDULE_1" to mockk<PaySchedule> { every { id } returns payScheduleId },
        )
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = mapOf(
                CompensationSchema(
                    id = UUID.randomUUID(),
                    entityId = 1L,
                    country = CountryCode.USA,
                    companyId = 1L,
                    isDefault = true,
                    name = "Default Schema",
                    isActive = true,
                    createdOn = LocalDateTime.now(),
                    createdBy = 1L,
                    updatedOn = LocalDateTime.now(),
                    updatedBy = 1L,
                    schemaItems = schemaItems.values.toList(),
                    tags = listOf("GLOBAL_PAYROLL"),
                    configurationScope = ConfigurationScope.COMPANY,
                    description = "Test schema description",
                ) to schemaItems,
            ),
            paySchedules = paySchedules,
            contracts = emptyMap(),
            activeCompContractIds = emptySet(),
            validCompensations = emptyMap(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_REVISION,
        )

        val compensationDraft = validationInputItem.toCompensationDraft(context)

        assertEquals(123L, compensationDraft.contractId)
        assertEquals(schemaItemId, compensationDraft.schemaItemId)
        assertEquals("CATEGORY", compensationDraft.schemaCategory)
        assertEquals("USD", compensationDraft.currency)
        assertEquals(BillingRateType.VALUE, compensationDraft.billingRateType)
        assertEquals(100.0, compensationDraft.billingRate)
        assertEquals(BillingFrequency.MONTHLY, compensationDraft.billingFrequency)
        assertEquals(payScheduleId, compensationDraft.payScheduleId)
        assertEquals(LocalDate.parse("2023-01-01"), compensationDraft.startDate)
        assertEquals(LocalDate.parse("2023-12-31"), compensationDraft.endDate)
        assertEquals(true, compensationDraft.isInstallment)
        assertEquals(12, compensationDraft.noOfInstallments)
    }

    @Test
    fun `test toCompensation with all fields`() {
        val entityId = 1L
        val companyId = 1L
        val previousId = UUID.randomUUID()

        val compensationDraft = buildCompensationDraftWithValidEndDate()

        val compensation = compensationDraft.toCompensation(
            uuidGenerator = mockk {
                every { generate() } returns UUID.randomUUID()
            },
            entityId = entityId,
            previousId = previousId,
            paySchedules = mapOf(
                compensationDraft.payScheduleId!! to mockk<PaySchedule> {
                    every { frequency } returns PayScheduleFrequency.MONTHLY
                },
            ),
        )

        assertEquals(entityId, compensation.entityId)
        assertEquals(companyId, compensation.companyId)
        assertEquals(compensationDraft.contractId, compensation.contractId)
        assertEquals(compensationDraft.schemaItemId, compensation.schemaItemId)
        assertEquals(compensationDraft.currency, compensation.currency)
        assertEquals(compensationDraft.billingRateType, compensation.billingRateType)
        assertEquals(compensationDraft.billingRate, compensation.billingRate)
        assertEquals(compensationDraft.billingFrequency, compensation.billingFrequency)
        assertEquals(compensationDraft.payScheduleId, compensation.payScheduleId)
        assertEquals(compensationDraft.startDate, compensation.startDate)
        assertEquals(compensationDraft.endDate, compensation.endDate)
        assertEquals(compensationDraft.isInstallment, compensation.isInstallment)
        assertEquals(compensationDraft.noOfInstallments, compensation.noOfInstallments)
        assertEquals(previousId, compensation.previousId)
    }

    @Test
    fun `test toCompensation with nullable fields`() {
        val entityId = 1L
        val companyId = 1L

        val compensationDraft = buildCompensationDraftWithNullableFields()

        val compensation = compensationDraft.toCompensation(
            uuidGenerator = mockk {
                every { generate() } returns UUID.randomUUID()
            },
            entityId = entityId,
            paySchedules = mapOf(
                compensationDraft.payScheduleId!! to mockk<PaySchedule> {
                    every { isInstallment } returns true
                    every { frequency } returns PayScheduleFrequency.MONTHLY
                    every { payDateReferenceType } returns PayDateReference.COMPENSATION_START_DATE
                },
            ),
        )

        assertEquals(entityId, compensation.entityId)
        assertEquals(companyId, compensation.companyId)
        assertEquals(compensationDraft.contractId, compensation.contractId)
        assertEquals(compensationDraft.schemaItemId, compensation.schemaItemId)
        assertEquals(compensationDraft.currency, compensation.currency)
        assertEquals(compensationDraft.billingRateType, compensation.billingRateType)
        assertEquals(compensationDraft.billingRate, compensation.billingRate)
        assertEquals(compensationDraft.billingFrequency, compensation.billingFrequency)
        assertEquals(compensationDraft.payScheduleId, compensation.payScheduleId)
        assertEquals(compensationDraft.startDate, compensation.startDate)
        assertEquals(LocalDate.now().plusMonths(1).minusDays(1), compensation.endDate)
        assertEquals(compensationDraft.isInstallment, compensation.isInstallment)
        assertEquals(compensationDraft.noOfInstallments, compensation.noOfInstallments)
        assertEquals(compensationDraft.reasonCode, null)
        assertNull(compensation.previousId)
    }

    @Test
    fun `test toBackFilledCompensation with valid end date`() {
        val entityId = 1L
        val companyId = 1L
        val previousId = UUID.randomUUID()

        val compensationDraft = buildCompensationDraftWithValidEndDate()

        val compensation = compensationDraft.toBackFilledCompensation(
            uuidGenerator = mockk {
                every { generate() } returns UUID.randomUUID()
            },
            entityId = entityId,
            previousId = previousId,
            paySchedules = mapOf(
                compensationDraft.payScheduleId!! to mockk<PaySchedule> {
                    every { frequency } returns PayScheduleFrequency.MONTHLY
                },
            ),
        )

        assertEquals(entityId, compensation.entityId)
        assertEquals(companyId, compensation.companyId)
        assertEquals(compensationDraft.contractId, compensation.contractId)
        assertEquals(compensationDraft.schemaItemId, compensation.schemaItemId)
        assertEquals(compensationDraft.currency, compensation.currency)
        assertEquals(compensationDraft.billingRateType, compensation.billingRateType)
        assertEquals(compensationDraft.billingRate, compensation.billingRate)
        assertEquals(compensationDraft.billingFrequency, compensation.billingFrequency)
        assertEquals(compensationDraft.payScheduleId, compensation.payScheduleId)
        assertEquals(compensationDraft.startDate, compensation.startDate)
        assertEquals(compensationDraft.endDate, compensation.endDate)
        assertEquals(compensationDraft.isInstallment, compensation.isInstallment)
        assertEquals(compensationDraft.noOfInstallments, compensation.noOfInstallments)
        assertEquals(compensationDraft.startDate, compensation.processingFrom)
        assertEquals(compensationDraft.endDate, compensation.processingTo)
        assertEquals(previousId, compensation.previousId)
    }

    @Test
    fun `test toBackFilledCompensation with end date null`() {
        val entityId = 1L
        val companyId = 1L

        val compensationDraft = buildCompensationDraftWithNullableFields()

        val compensation = compensationDraft.toBackFilledCompensation(
            uuidGenerator = mockk {
                every { generate() } returns UUID.randomUUID()
            },
            entityId = entityId,
            paySchedules = mapOf(
                compensationDraft.payScheduleId!! to mockk<PaySchedule> {
                    every { isInstallment } returns true
                    every { frequency } returns PayScheduleFrequency.MONTHLY
                    every { payDateReferenceType } returns PayDateReference.COMPENSATION_START_DATE
                },
            ),
        )

        assertEquals(entityId, compensation.entityId)
        assertEquals(companyId, compensation.companyId)
        assertEquals(compensationDraft.contractId, compensation.contractId)
        assertEquals(compensationDraft.schemaItemId, compensation.schemaItemId)
        assertEquals(compensationDraft.currency, compensation.currency)
        assertEquals(compensationDraft.billingRateType, compensation.billingRateType)
        assertEquals(compensationDraft.billingRate, compensation.billingRate)
        assertEquals(compensationDraft.billingFrequency, compensation.billingFrequency)
        assertEquals(compensationDraft.payScheduleId, compensation.payScheduleId)
        assertEquals(compensationDraft.startDate, compensation.startDate)
        assertEquals(LocalDate.now().plusMonths(1).minusDays(1), compensation.endDate)
        assertEquals(compensationDraft.isInstallment, compensation.isInstallment)
        assertEquals(compensationDraft.noOfInstallments, compensation.noOfInstallments)
        assertEquals(compensationDraft.startDate, compensation.processingFrom)
        assertEquals(compensationDraft.endDate, compensation.processingTo)
        assertEquals(compensationDraft.reasonCode, null)
        assertNull(compensation.previousId)
    }

    private fun buildCompensationDraftWithNullableFields() = CompensationDraft(
        companyId = 1L,
        employeeId = "1",
        contractId = 123L,
        schemaItemId = UUID.randomUUID(),
        schemaCategory = "CATEGORY",
        currency = "USD",
        billingRateType = BillingRateType.VALUE,
        billingRate = 100.0,
        billingFrequency = BillingFrequency.MONTHLY,
        payScheduleId = UUID.randomUUID(),
        startDate = LocalDate.now(),
        endDate = null,
        isInstallment = true,
        noOfInstallments = 1,
        reasonCode = null,
    )

    private fun buildCompensationDraftWithValidEndDate() = CompensationDraft(
        companyId = 1L,
        employeeId = "1",
        contractId = 123L,
        schemaItemId = UUID.randomUUID(),
        schemaCategory = "CATEGORY",
        currency = "USD",
        billingRateType = BillingRateType.VALUE,
        billingRate = 100.0,
        billingFrequency = BillingFrequency.MONTHLY,
        payScheduleId = UUID.randomUUID(),
        startDate = LocalDate.now(),
        endDate = LocalDate.now().plusYears(1),
        isInstallment = true,
        noOfInstallments = 12,
        reasonCode = null,
    )
}
