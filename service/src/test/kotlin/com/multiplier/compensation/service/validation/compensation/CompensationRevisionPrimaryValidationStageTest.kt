package com.multiplier.compensation.service.validation.compensation

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.domain.skeleton.SkeletonData
import com.multiplier.compensation.domain.skeleton.ValidationRegex
import com.multiplier.compensation.domain.skeleton.enums.ValueType
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.compensation.validation.stages.CompensationRevisionPrimaryValidationStage
import com.multiplier.compensation.service.compensation.validation.validators.ActiveContractValidator
import com.multiplier.compensation.service.compensation.validation.validators.BasePayBreakdownChangeValidator
import com.multiplier.compensation.service.compensation.validation.validators.BasePayChangeValidator
import com.multiplier.compensation.service.compensation.validation.validators.BasePayDateGapsValidator
import com.multiplier.compensation.service.compensation.validation.validators.BasePayDateRangeValidator
import com.multiplier.compensation.service.compensation.validation.validators.BasePayDependantDateRangeValidator
import com.multiplier.compensation.service.compensation.validation.validators.ComponentChangeValidator
import com.multiplier.compensation.service.compensation.validation.validators.ContractIdValidator
import com.multiplier.compensation.service.compensation.validation.validators.DeletionValidator
import com.multiplier.compensation.service.compensation.validation.validators.DuplicateComponentNameValidator
import com.multiplier.compensation.service.compensation.validation.validators.RevisionStartDateValidator
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate

@ExtendWith(MockKExtension::class)
class CompensationRevisionPrimaryValidationStageTest {
    private lateinit var primaryValidationStage: CompensationRevisionPrimaryValidationStage

    private lateinit var duplicateComponentNameValidator: DuplicateComponentNameValidator
    private lateinit var contractIdValidator: ContractIdValidator
    private lateinit var deletionValidator: DeletionValidator
    private lateinit var activeContractValidator: ActiveContractValidator
    private lateinit var componentChangeValidator: ComponentChangeValidator
    private lateinit var revisionStartDateValidator: RevisionStartDateValidator
    private lateinit var basePayChangeValidator: BasePayChangeValidator
    private lateinit var basePayDateGapsValidator: BasePayDateGapsValidator
    private lateinit var basePayBreakdownChangeValidator: BasePayBreakdownChangeValidator
    private lateinit var basePayDateRangeValidator: BasePayDateRangeValidator
    private lateinit var basePayDependantDateRangeValidator: BasePayDependantDateRangeValidator

    private lateinit var context: CompensationValidatorContext
    private lateinit var item: ValidationInputItem

    @BeforeEach
    fun setUp() {
        duplicateComponentNameValidator = mockk()
        contractIdValidator = mockk()
        activeContractValidator = mockk()
        deletionValidator = mockk()
        componentChangeValidator = mockk()
        revisionStartDateValidator = mockk()
        basePayBreakdownChangeValidator = mockk()
        basePayChangeValidator = mockk()
        basePayDateGapsValidator = mockk()
        basePayDateRangeValidator = mockk()
        basePayDependantDateRangeValidator = mockk()

        primaryValidationStage = CompensationRevisionPrimaryValidationStage(
            duplicateComponentNameValidator,
            contractIdValidator,
            activeContractValidator,
            deletionValidator,
            componentChangeValidator,
            revisionStartDateValidator,
            basePayChangeValidator,
            basePayDateGapsValidator,
            basePayBreakdownChangeValidator,
            basePayDateRangeValidator,
            basePayDependantDateRangeValidator,
        )

        context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = Skeleton(
                data = listOf(
                    SkeletonData(
                        fieldName = "Component Name",
                        fieldId = "COMPONENT_NAME",
                        description = "Component Name",
                        valueType = ValueType.DOUBLE,
                        mandatory = true,
                        possibleValues = listOf("Base Pay", "Meal Allowance"),
                        defaultValue = null,
                        validationRegex = ValidationRegex(
                            "^(Base Pay|Meal Allowance)$",
                            "Should be either Base Pay or Meal Allowance",
                        ),
                    ),
                ),
                keys = emptyList(),
            ),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                50000L to Contract(
                    id = 50000L,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ACTIVE,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_REVISION,
        )

        item = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = "CONTRACT_ID",
                    value = "50000",
                ),
                KeyValuePair(
                    key = "COMPONENT_NAME",
                    value = "Meal Allowance",
                ),
            ),
        )

        every { contractIdValidator.validate(any(), any(), any()) } returns true
    }

    @Test
    fun `should process primary validation stage successfully`() {
        val results = mutableMapOf<String, MutableList<CellValidationResult>>()
        val output = mutableMapOf<String, CompensationDraft>()
        val collector = ValidationDataCollector(results, output, inputPlusDerivedRows = emptyMap())

        every {
            duplicateComponentNameValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            contractIdValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            activeContractValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            contractIdValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            deletionValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            componentChangeValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            revisionStartDateValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            basePayChangeValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            basePayBreakdownChangeValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            basePayDateRangeValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            basePayDependantDateRangeValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true

        every {
            basePayDateGapsValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        assertTrue { primaryValidationStage.process(item, context, collector) }
    }
}
