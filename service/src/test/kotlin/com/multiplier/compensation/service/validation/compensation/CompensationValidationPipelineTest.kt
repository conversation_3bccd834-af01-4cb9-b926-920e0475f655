package com.multiplier.compensation.service.validation.compensation

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.domain.skeleton.SkeletonData
import com.multiplier.compensation.domain.skeleton.ValidationRegex
import com.multiplier.compensation.domain.skeleton.enums.ValueType
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.mapper.SkeletonCompensationMapperUtils.toCompensationDraft
import com.multiplier.compensation.service.common.validationpipeline.ValidationPipeline
import com.multiplier.compensation.service.common.validationpipeline.common.validators.SkeletonValidator
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.BaseCompensationValidationPipeline
import com.multiplier.compensation.service.compensation.validation.CompensationValidationPipeline
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.compensation.validation.stages.CompensationGroupValidationStage
import com.multiplier.compensation.service.compensation.validation.stages.CompensationPrimaryValidationStage
import com.multiplier.compensation.service.compensation.validation.stages.CompensationSecondaryValidationStage
import io.mockk.called
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import kotlin.test.assertTrue

@ExtendWith(MockKExtension::class)
class CompensationValidationPipelineTest {
    private lateinit var groupValidationStage:
        CompensationGroupValidationStage
    private lateinit var primaryValidationStage: CompensationPrimaryValidationStage
    private lateinit var secondaryValidationStage: CompensationSecondaryValidationStage

    private lateinit var skeletonValidator: SkeletonValidator

    private lateinit var validationPipeline: ValidationPipeline<CompensationValidatorContext, CompensationDraft>

    private lateinit var context: CompensationValidatorContext
    private lateinit var item: ValidationInputItem

    private lateinit var draft: CompensationDraft

    @BeforeEach
    fun setUp() {
        groupValidationStage = mockk()
        primaryValidationStage = mockk()
        secondaryValidationStage = mockk()

        skeletonValidator = mockk()

        validationPipeline = CompensationValidationPipeline(
            groupValidationStage = groupValidationStage,
            primaryValidationStage = primaryValidationStage,
            secondaryValidationStage = secondaryValidationStage,
            skeletonValidator = skeletonValidator,
        )

        context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = Skeleton(
                data = listOf(
                    SkeletonData(
                        fieldName = "Component Name",
                        fieldId = "COMPONENT_NAME",
                        description = "Component Name",
                        valueType = ValueType.DOUBLE,
                        mandatory = true,
                        possibleValues = listOf("Base Pay", "Meal Allowance"),
                        defaultValue = null,
                        validationRegex = ValidationRegex(
                            regex = "^(Base Pay|Meal Allowance)$",
                            "Should be either Base Pay or Meal Allowance",
                        ),
                    ),
                ),
                keys = emptyList(),
            ),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                50000L to Contract(
                    id = 50000L,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ONBOARDING,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.VALIDATE,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        item = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = "CONTRACT_ID",
                    value = "50000",
                ),
                KeyValuePair(
                    key = "COMPONENT_NAME",
                    value = "Meal Allowance",
                ),
            ),
        )

        draft = item.toCompensationDraft(context)
    }

    @Test
    fun `pipeline should pass when primary and secondary validation stages are passed`() {
        every { skeletonValidator.validate(item, context.skeleton) } returns mutableListOf()
        every {
            groupValidationStage.process(any(), context, any<ValidationDataCollector<CompensationDraft>>())
        } returns true
        every {
            primaryValidationStage.process(item, context, any<ValidationDataCollector<CompensationDraft>>())
        } returns
            true
        every {
            secondaryValidationStage.process(item, context, any<ValidationDataCollector<CompensationDraft>>())
        } returns
            true

        validationPipeline.execute(context, listOf(item))

        verify {
            primaryValidationStage.process(item, context, any<ValidationDataCollector<CompensationDraft>>())
            secondaryValidationStage.process(item, context, any<ValidationDataCollector<CompensationDraft>>())
        }
    }

    @Test
    fun `pipeline should not continue when primary validation stage is failed`() {
        every { skeletonValidator.validate(item, context.skeleton) } returns mutableListOf()
        every {
            groupValidationStage.process(any(), context, any<ValidationDataCollector<CompensationDraft>>())
        } returns true
        every {
            primaryValidationStage.process(
                item,
                context,
                any<ValidationDataCollector<CompensationDraft>>(),
            )
        } returns false

        validationPipeline.execute(context, listOf(item))

        verify { primaryValidationStage.process(item, context, any<ValidationDataCollector<CompensationDraft>>()) }
        verify { secondaryValidationStage wasNot called }
    }

    @Test
    fun `pipeline should failed when secondary validation stage is failed`() {
        every { skeletonValidator.validate(item, context.skeleton) } returns mutableListOf()
        every {
            groupValidationStage.process(any(), context, any<ValidationDataCollector<CompensationDraft>>())
        } returns true
        every {
            primaryValidationStage.process(item, context, any<ValidationDataCollector<CompensationDraft>>())
        } returns
            true
        every {
            secondaryValidationStage.process(
                item,
                context,
                any<ValidationDataCollector<CompensationDraft>>(),
            )
        } returns false

        validationPipeline.execute(context, listOf(item))

        verify {
            primaryValidationStage.process(item, context, any<ValidationDataCollector<CompensationDraft>>())
            secondaryValidationStage.process(item, context, any<ValidationDataCollector<CompensationDraft>>())
        }
    }

    @Test
    fun `pipeline should failed when skeleton validation failed`() {
        val errorResult = mockk<CellValidationResult> { every { type } returns ValidationResultType.ERROR }
        every { skeletonValidator.validate(item, context.skeleton) } returns mutableListOf(errorResult)
        every {
            groupValidationStage.process(any(), context, any<ValidationDataCollector<CompensationDraft>>())
        } returns true
        val result = validationPipeline.execute(context, listOf(item))

        verify(exactly = 0) {
            primaryValidationStage.process(any(), any(), any())
            secondaryValidationStage.process(any(), any(), any())
        }

        assertTrue { result.rowValidationResult.isNotEmpty() }
        assertTrue(validationPipeline is BaseCompensationValidationPipeline)
    }
}
