package com.multiplier.compensation.service.validation.compensation

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.domain.skeleton.SkeletonData
import com.multiplier.compensation.domain.skeleton.ValidationRegex
import com.multiplier.compensation.domain.skeleton.enums.ValueType
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.compensation.validation.stages.DeductionSecondaryValidationStage
import com.multiplier.compensation.service.compensation.validation.validators.BillingFrequencyValidator
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateInputValidator
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateTypeValidator
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateValidator
import com.multiplier.compensation.service.compensation.validation.validators.CompensationDateValidator
import com.multiplier.compensation.service.compensation.validation.validators.ContractSchemaMappingValidator
import com.multiplier.compensation.service.compensation.validation.validators.DependentComponentCurrencyValidator
import com.multiplier.compensation.service.compensation.validation.validators.InstallmentValidator
import com.multiplier.compensation.service.compensation.validation.validators.PayScheduleTypeValidator
import com.multiplier.compensation.service.compensation.validation.validators.PayScheduleValidator
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verifyAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@ExtendWith(MockKExtension::class)
class DeductionSecondaryValidationStageTest {
    private lateinit var billingFrequencyValidator: BillingFrequencyValidator
    private lateinit var billingRateTypeValidator: BillingRateTypeValidator
    private lateinit var billingRateValidator: BillingRateValidator
    private lateinit var compensationDateValidator: CompensationDateValidator
    private lateinit var installmentValidator: InstallmentValidator
    private lateinit var payScheduleTypeValidator: PayScheduleTypeValidator
    private lateinit var payScheduleValidator: PayScheduleValidator
    private lateinit var contractSchemaMappingValidator: ContractSchemaMappingValidator
    private lateinit var dependentComponentCurrencyValidator: DependentComponentCurrencyValidator
    private lateinit var billingRateInputValidator: BillingRateInputValidator

    private lateinit var secondaryValidationStage: DeductionSecondaryValidationStage

    private lateinit var context: CompensationValidatorContext
    private lateinit var item: ValidationInputItem

    @BeforeEach
    fun setUp() {
        billingFrequencyValidator = mockk()
        billingRateTypeValidator = mockk()
        billingRateValidator = mockk()
        compensationDateValidator = mockk()
        installmentValidator = mockk()
        payScheduleTypeValidator = mockk()
        payScheduleValidator = mockk()
        contractSchemaMappingValidator = mockk()
        dependentComponentCurrencyValidator = mockk()
        billingRateInputValidator = mockk()

        secondaryValidationStage = DeductionSecondaryValidationStage(
            billingFrequencyValidator,
            billingRateTypeValidator,
            billingRateValidator,
            compensationDateValidator,
            installmentValidator,
            payScheduleTypeValidator,
            payScheduleValidator,
            contractSchemaMappingValidator,
            dependentComponentCurrencyValidator,
            billingRateInputValidator,
        )

        context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = Skeleton(
                data = listOf(
                    SkeletonData(
                        fieldName = "Component Name",
                        fieldId = "COMPONENT_NAME",
                        description = "Component Name",
                        valueType = ValueType.DOUBLE,
                        mandatory = true,
                        possibleValues = listOf("Loan 1", "Loan 2"),
                        defaultValue = null,
                        validationRegex = ValidationRegex(
                            regex = "^(Loan 1|Loan 2)$",
                            "Should be either Loan 1 or Loan 2",
                        ),
                    ),
                ),
                keys = emptyList(),
            ),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                50000L to Contract(
                    id = 50000L,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ACTIVE,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.DEDUCTION,
        )

        item = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = "CONTRACT_ID",
                    value = "50000",
                ),
                KeyValuePair(
                    key = "COMPONENT_NAME",
                    value = "Loan 2",
                ),
            ),
        )
    }

    @Test
    fun `should process secondary validation stage successfully`() {
        val results = mutableMapOf<String, MutableList<CellValidationResult>>()
        val output = mutableMapOf<String, CompensationDraft>()
        val collector = ValidationDataCollector(results, output, inputPlusDerivedRows = emptyMap())

        every {
            compensationDateValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            billingFrequencyValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            billingRateTypeValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            billingRateValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            installmentValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            payScheduleValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            payScheduleTypeValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true

        every {
            contractSchemaMappingValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            dependentComponentCurrencyValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            billingRateInputValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true

        assertTrue { secondaryValidationStage.process(item, context, collector) }
    }

    @Test
    fun `should process secondary validation stage successfully even one validator failed`() {
        val results = mutableMapOf<String, MutableList<CellValidationResult>>()
        val output = mutableMapOf<String, CompensationDraft>()
        val collector = ValidationDataCollector(results, output, inputPlusDerivedRows = emptyMap())

        every {
            compensationDateValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns false
        every {
            billingFrequencyValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            billingRateTypeValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            billingRateValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            installmentValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            payScheduleValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            payScheduleTypeValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true

        every {
            contractSchemaMappingValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            dependentComponentCurrencyValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true
        every {
            billingRateInputValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        } returns true

        assertFalse { secondaryValidationStage.process(item, context, collector) }

        verifyAll {
            compensationDateValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
            billingFrequencyValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
            billingRateValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
            installmentValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
            payScheduleValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
            payScheduleTypeValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
            contractSchemaMappingValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
            dependentComponentCurrencyValidator.validate(
                any<ValidationInputItem>(),
                context,
                collector,
            )
        }
    }
}
