package com.multiplier.compensation.service.validation.compensation.utils.fixtures

import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.dto.RowItem
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField

fun rowItemFixture(
    id: String = "1",
    contractId: String = "123",
    componentName: String = "COMPONENT_1",
    currency: String = "USD",
    billingRateType: String = "Value",
    billingRate: String = "100.0",
    billingFrequency: String = "MONTHLY",
    payScheduleName: String = "SCHEDULE_1",
    startDate: String = "2023-01-01",
    endDate: String = "2023-12-31",
    isInstallment: String = "Yes",
    numberOfInstallments: String = "12",
    notes: String = "Test note",
): RowItem = RowItem(
    id = id,
    keyValuePairs = listOf(
        KeyValuePair(CommonSkeletonField.CONTRACT_ID.id, contractId),
        KeyValuePair(CompensationSkeletonField.COMPONENT_NAME.id, componentName),
        KeyValuePair(CommonSkeletonField.CURRENCY.id, currency),
        KeyValuePair(CompensationSkeletonField.BILLING_RATE_TYPE.id, billingRateType),
        KeyValuePair(CompensationSkeletonField.BILLING_RATE.id, billingRate),
        KeyValuePair(CompensationSkeletonField.BILLING_FREQUENCY.id, billingFrequency),
        KeyValuePair(CompensationSkeletonField.PAY_SCHEDULE_NAME.id, payScheduleName),
        KeyValuePair(CompensationSkeletonField.START_DATE.id, startDate),
        KeyValuePair(CompensationSkeletonField.END_DATE.id, endDate),
        KeyValuePair(CompensationSkeletonField.IS_INSTALLMENT.id, isInstallment),
        KeyValuePair(CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id, numberOfInstallments),
        KeyValuePair(CompensationSkeletonField.NOTES.id, notes),
    ),
)
