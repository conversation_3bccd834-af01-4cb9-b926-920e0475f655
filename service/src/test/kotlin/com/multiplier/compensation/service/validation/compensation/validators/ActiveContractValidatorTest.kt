package com.multiplier.compensation.service.validation.compensation.validators

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.mapper.SkeletonCompensationMapperUtils.toCompensationDraft
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.compensation.validation.validators.ActiveContractValidator
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationValidatorContextFixture
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import kotlin.test.assertFalse

class ActiveContractValidatorTest {
    private lateinit var validator: ActiveContractValidator
    private lateinit var collector: ValidationDataCollector<CompensationDraft>
    private lateinit var input: ValidationInputItem

    private val testFieldKey = CommonSkeletonField.CONTRACT_ID.id

    @BeforeEach
    fun setUp() {
        validator = ActiveContractValidator()

        input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.CONTRACT_ID.id,
                    value = "50000",
                ),
            ),
        )
        val context = compensationValidatorContextFixture()
        val draft = input.toCompensationDraft(context)

        collector = ValidationDataCollector(
            drafts = mutableMapOf(input.id to draft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )
    }

    @Test
    fun `should return true when contract is active`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                50000L to Contract(
                    id = 50000L,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ACTIVE,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_REVISION,
        )

        assertTrue { validator.validate(input, context, collector) }
        assertTrue(collector.rowValidationResult[input.id].isNullOrEmpty())
    }

    @Test
    fun `should return false when draft not found`() {
        val context = compensationValidatorContextFixture()
        val input = ValidationInputItem(id = "1", fieldKeyValuePairs = listOf())
        assertFalse {
            validator.validate(
                input,
                context,
                ValidationDataCollector(inputPlusDerivedRows = emptyMap()),
            )
        }
        assertTrue { collector.rowValidationResult[input.id].isNullOrEmpty() }
    }

    @Test
    fun `should return false when contract is not active`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                50000L to Contract(
                    id = 50000L,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.OFFBOARDING,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_REVISION,
        )

        assertFalse { validator.validate(input, context, collector) }
        assertTrue {
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == testFieldKey && it.message == "Contract is not active"
            }
        }
    }
}
