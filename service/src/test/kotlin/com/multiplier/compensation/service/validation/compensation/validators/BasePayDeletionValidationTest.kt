package com.multiplier.compensation.service.validation.compensation.validators

import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationschema.CompensationSchema
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.mapper.SkeletonCompensationMapperUtils.toCompensationDraft
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.compensation.validation.validators.BasePayDateRangeValidator
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationFixture
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationSchemaItemFixture
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertTrue

class BasePayDeletionValidationTest {
    private lateinit var validator: BasePayDateRangeValidator
    private lateinit var context: CompensationValidatorContext
    private lateinit var collector: ValidationDataCollector<CompensationDraft>

    private val basePaySchemaItemId = UUID.randomUUID()
    private val dependantSchemaItemId = UUID.randomUUID()
    private val monthlyPayScheduleId = UUID.randomUUID()

    private val contractIdInputKeyValuePair = KeyValuePair(
        key = CommonSkeletonField.CONTRACT_ID.id,
        value = "50000",
    )
    private val basePayCategoryInputKeyValuePair = KeyValuePair(
        key = CompensationSkeletonField.COMPONENT_NAME.id,
        value = "Base Pay",
    )
    private val dependantCategoryInputKeyValuePair = KeyValuePair(
        key = CompensationSkeletonField.COMPONENT_NAME.id,
        value = "EPF",
    )
    private val payScheduleInputKeyValuePair = KeyValuePair(
        key = CompensationSkeletonField.PAY_SCHEDULE_NAME.id,
        value = "MONTHLY_SCHEDULE",
    )

    private val basePayPercentageInputKeyValuePair = KeyValuePair(
        key = CompensationSkeletonField.BILLING_RATE_TYPE.id,
        value = BillingRateType.BASE_PAY_PERCENTAGE.description,
    )

    private lateinit var contextBasePay: Compensation
    private lateinit var contextBasePayDependant: Compensation

    private lateinit var compensationSchema: CompensationSchema

    private lateinit var compensationSchemaItemFixtureBasePay: CompensationSchemaItem
    private lateinit var compensationSchemaItemFixtureDependant: CompensationSchemaItem

    private lateinit var schemaItems: Map<String, CompensationSchemaItem>

    @BeforeEach
    fun setUp() {
        validator = BasePayDateRangeValidator()

        contextBasePay = compensationFixture(
            schemaItemId = dependantSchemaItemId,
            payScheduleId = monthlyPayScheduleId,
            category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
            billingRateType = BillingRateType.VALUE,
            startDate = LocalDate.of(2025, 1, 1),
            endDate = LocalDate.of(2025, 12, 31),
        )

        contextBasePayDependant = compensationFixture(
            schemaItemId = dependantSchemaItemId,
            payScheduleId = monthlyPayScheduleId,
            category = CategoryConstants.CATEGORY_EMPLOYEE_DEDUCTION,
            billingRateType = BillingRateType.BASE_PAY_PERCENTAGE,
            startDate = LocalDate.of(2025, 1, 1),
            endDate = LocalDate.of(2025, 10, 31),
        )

        compensationSchemaItemFixtureBasePay = compensationSchemaItemFixture(
            id = basePaySchemaItemId,
            category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
            componentName = "Base Pay",
        )

        compensationSchemaItemFixtureDependant = compensationSchemaItemFixture(
            id = dependantSchemaItemId,
            category = CategoryConstants.CATEGORY_EMPLOYEE_DEDUCTION,
            componentName = "EPF",
        )

        schemaItems = mapOf(
            "Base Pay" to compensationSchemaItemFixtureBasePay,
            "EPF" to compensationSchemaItemFixtureDependant,
        )

        compensationSchema = CompensationSchema(
            id = UUID.randomUUID(),
            entityId = 1L,
            country = CountryCode.USA,
            companyId = 1L,
            isDefault = true,
            name = "Default Schema",
            isActive = true,
            createdOn = LocalDateTime.now(),
            createdBy = 1L,
            updatedOn = LocalDateTime.now(),
            updatedBy = 1L,
            schemaItems = schemaItems.values.toList(),
            tags = listOf("GLOBAL_PAYROLL"),
            configurationScope = ConfigurationScope.COMPANY,
            description = "Test schema description",
        )

        context = CompensationValidatorContext(
            entityId = 1L,
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_REVISION,
            skeleton = mockk(),
            schemaItems = mapOf(
                compensationSchema to schemaItems,
            ),
            paySchedules = mapOf(
                "MONTHLY_SCHEDULE" to mockk<PaySchedule> { every { id } returns monthlyPayScheduleId },
            ),
            validCompensations = mapOf(
                50000L to listOf(
                    contextBasePay,
                    contextBasePayDependant,
                ),
            ),
        )

        collector = ValidationDataCollector(inputPlusDerivedRows = emptyMap())
    }

    @Test
    fun `should fail when draft is not found`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(),
        )

        assertFalse { validator.validate(input, context, collector) }
    }

    @Test
    fun `should fail when contractId is null`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CompensationSkeletonField.BILLING_RATE.id,
                    value = "100.0",
                ),
            ),
        )

        collector.drafts[input.id] = input.toCompensationDraft(context)

        assertFalse { validator.validate(input, context, collector) }
    }

    @Test
    fun `should success when it is not a base pay component`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdInputKeyValuePair,
                KeyValuePair(
                    key = CompensationSkeletonField.COMPONENT_NAME.id,
                    value = "Random Component",
                ),
            ),
        )

        collector.drafts[input.id] = input.toCompensationDraft(context)

        assertTrue { validator.validate(input, context, collector) }
    }

    @Test
    fun `should success when dependants are provided for deletion`() {
        val basePayInput = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                basePayCategoryInputKeyValuePair,
                contractIdInputKeyValuePair,
                payScheduleInputKeyValuePair,
                KeyValuePair(
                    key = CompensationSkeletonField.BILLING_RATE.id,
                    value = "0",
                ),
                KeyValuePair(
                    key = CompensationSkeletonField.START_DATE.id,
                    value = "2025-01-01",
                ),
                KeyValuePair(
                    key = CompensationSkeletonField.END_DATE.id,
                    value = "2025-12-31",
                ),
            ),
        )

        val dependantInput = ValidationInputItem(
            id = "2",
            fieldKeyValuePairs = listOf(
                dependantCategoryInputKeyValuePair,
                contractIdInputKeyValuePair,
                basePayPercentageInputKeyValuePair,
                payScheduleInputKeyValuePair,
                KeyValuePair(
                    key = CompensationSkeletonField.BILLING_RATE.id,
                    value = "0",
                ),
                KeyValuePair(
                    key = CompensationSkeletonField.START_DATE.id,
                    value = "2025-01-01",
                ),
                KeyValuePair(
                    key = CompensationSkeletonField.END_DATE.id,
                    value = "2025-10-31",
                ),
            ),
        )

        collector.drafts[basePayInput.id] = basePayInput.toCompensationDraft(context)
        collector.drafts[dependantInput.id] = dependantInput.toCompensationDraft(context)

        assertTrue { validator.validate(basePayInput, context, collector) }
    }

    @Nested
    inner class WhenBasePayDeletionIsGivenWithoutDependantDeletionInTheInput {
        @Test
        fun `should fail when base pay is deleted and conflicts with existing dependent's terms`() {
            val input = ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    contractIdInputKeyValuePair,
                    basePayCategoryInputKeyValuePair,
                    KeyValuePair(
                        key = CompensationSkeletonField.BILLING_RATE.id,
                        value = "0",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.START_DATE.id,
                        value = "2025-01-01",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.END_DATE.id,
                        value = "2025-12-31",
                    ),
                ),
            )

            collector.drafts[input.id] = input.toCompensationDraft(context)

            assertFalse { validator.validate(input, context, collector) }
            assertTrue {
                collector.rowValidationResult[input.id]!!.any {
                    it.field.key == CompensationSkeletonField.COMPONENT_NAME.id &&
                        it.message == "All dependant components dates " +
                        "should fall within the parent component start and end date"
                }
            }
        }

        @Test
        fun `should success when base pay is deleted and does not conflict with existing dependent's terms`() {
            val input = ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    contractIdInputKeyValuePair,
                    basePayCategoryInputKeyValuePair,
                    KeyValuePair(
                        key = CompensationSkeletonField.BILLING_RATE.id,
                        value = "0",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.START_DATE.id,
                        value = "2025-01-01",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.END_DATE.id,
                        value = "2025-12-31",
                    ),
                ),
            )

            context = CompensationValidatorContext(
                entityId = 1L,
                contextUseCase = ContextUseCase.UPSERT,
                contextSource = RequestType.COMPENSATION_REVISION,
                skeleton = mockk(),
                schemaItems = mapOf(
                    compensationSchema to mapOf(
                        compensationSchemaItemFixtureBasePay.componentName to compensationSchemaItemFixtureBasePay,
                        compensationSchemaItemFixtureDependant.componentName to compensationSchemaItemFixtureDependant,
                    ),
                ),
                paySchedules = mapOf(
                    "MONTHLY_SCHEDULE" to mockk<PaySchedule> { every { id } returns monthlyPayScheduleId },
                ),
                validCompensations = mapOf(
                    50000L to listOf(
                        contextBasePay,
                    ),
                ),
            )

            collector.drafts[input.id] = input.toCompensationDraft(context)

            assertTrue { validator.validate(input, context, collector) }
        }
    }

    @Nested
    inner class WhenBasePayDeletionIsGivenWithDependantDeletionInTheInput {
        @Test
        fun `should fail when dependant is early terminated and conflicts with base pay deletion`() {
            val basePayInput = ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    basePayCategoryInputKeyValuePair,
                    contractIdInputKeyValuePair,
                    payScheduleInputKeyValuePair,
                    KeyValuePair(
                        key = CompensationSkeletonField.BILLING_RATE.id,
                        value = "0",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.START_DATE.id,
                        value = "2025-01-01",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.END_DATE.id,
                        value = "2025-12-31",
                    ),
                ),
            )

            val dependantInput = ValidationInputItem(
                id = "2",
                fieldKeyValuePairs = listOf(
                    dependantCategoryInputKeyValuePair,
                    contractIdInputKeyValuePair,
                    basePayPercentageInputKeyValuePair,
                    payScheduleInputKeyValuePair,
                    KeyValuePair(
                        key = CompensationSkeletonField.BILLING_RATE.id,
                        value = "100",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.START_DATE.id,
                        value = "2025-01-01",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.END_DATE.id,
                        value = "2025-05-15",
                    ),
                ),
            )

            collector.drafts[basePayInput.id] = basePayInput.toCompensationDraft(context)
            collector.drafts[dependantInput.id] = dependantInput.toCompensationDraft(context)

            assertFalse { validator.validate(basePayInput, context, collector) }
            assertTrue {
                collector.rowValidationResult[basePayInput.id]!!.any {
                    it.field.key == CompensationSkeletonField.COMPONENT_NAME.id &&
                        it.message == "All dependant components dates " +
                        "should fall within the parent component start and end date"
                }
            }
        }

        @Test
        fun `should fail when dependant rate changed and conflicts with base pay deletion`() {
            val basePayInput = ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    basePayCategoryInputKeyValuePair,
                    contractIdInputKeyValuePair,
                    payScheduleInputKeyValuePair,
                    KeyValuePair(
                        key = CompensationSkeletonField.BILLING_RATE.id,
                        value = "0",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.START_DATE.id,
                        value = "2025-01-01",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.END_DATE.id,
                        value = "2025-12-31",
                    ),
                ),
            )

            val dependantInput = ValidationInputItem(
                id = "2",
                fieldKeyValuePairs = listOf(
                    dependantCategoryInputKeyValuePair,
                    contractIdInputKeyValuePair,
                    basePayPercentageInputKeyValuePair,
                    payScheduleInputKeyValuePair,
                    KeyValuePair(
                        key = CompensationSkeletonField.BILLING_RATE.id,
                        value = "101",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.START_DATE.id,
                        value = "2025-01-01",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.END_DATE.id,
                        value = "2025-10-15",
                    ),
                ),
            )

            collector.drafts[basePayInput.id] = basePayInput.toCompensationDraft(context)
            collector.drafts[dependantInput.id] = dependantInput.toCompensationDraft(context)

            assertFalse { validator.validate(basePayInput, context, collector) }
            assertTrue {
                collector.rowValidationResult[basePayInput.id]!!.any {
                    it.field.key == CompensationSkeletonField.COMPONENT_NAME.id &&
                        it.message == "All dependant components dates " +
                        "should fall within the parent component start and end date"
                }
            }
        }

        @Test
        fun `should fail when instalment increment conflicts with base pay deletion`() {
            val basePayInput = ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    basePayCategoryInputKeyValuePair,
                    contractIdInputKeyValuePair,
                    payScheduleInputKeyValuePair,
                    KeyValuePair(
                        key = CompensationSkeletonField.BILLING_RATE.id,
                        value = "0",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.START_DATE.id,
                        value = "2025-01-01",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.END_DATE.id,
                        value = "2025-12-31",
                    ),
                ),
            )

            val dependantInput = ValidationInputItem(
                id = "2",
                fieldKeyValuePairs = listOf(
                    dependantCategoryInputKeyValuePair,
                    contractIdInputKeyValuePair,
                    basePayPercentageInputKeyValuePair,
                    payScheduleInputKeyValuePair,
                    KeyValuePair(
                        key = CompensationSkeletonField.START_DATE.id,
                        value = "2024-10-01",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.IS_INSTALLMENT.id,
                        value = "Yes",
                    ),
                    KeyValuePair(
                        key = CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                        value = "12",
                    ),
                ),
            )

            contextBasePayDependant = compensationFixture(
                schemaItemId = dependantSchemaItemId,
                payScheduleId = monthlyPayScheduleId,
                category = CategoryConstants.CATEGORY_EMPLOYEE_DEDUCTION,
                billingRateType = BillingRateType.BASE_PAY_PERCENTAGE,
                startDate = LocalDate.of(2024, 10, 1),
                endDate = LocalDate.of(2024, 12, 1),
                isInstallment = true,
                noOfInstallments = 2,
            )

            context = CompensationValidatorContext(
                entityId = 1L,
                contextUseCase = ContextUseCase.UPSERT,
                contextSource = RequestType.COMPENSATION_REVISION,
                skeleton = mockk(),
                schemaItems = mapOf(
                    compensationSchema to schemaItems,
                ),
                paySchedules = mapOf(
                    "MONTHLY_SCHEDULE" to mockk<PaySchedule> {
                        every { id } returns monthlyPayScheduleId
                        every { isInstallment } returns true
                        every { frequency } returns PayScheduleFrequency.MONTHLY
                        every { payDateReferenceType } returns PayDateReference.COMPENSATION_START_DATE
                    },
                ),
                validCompensations = mapOf(
                    50000L to listOf(
                        contextBasePay,
                        contextBasePayDependant,
                    ),
                ),
            )

            collector.drafts[basePayInput.id] = basePayInput.toCompensationDraft(context)
            collector.drafts[dependantInput.id] = dependantInput.toCompensationDraft(context)

            assertFalse { validator.validate(basePayInput, context, collector) }
            assertTrue {
                collector.rowValidationResult[basePayInput.id]!!.any {
                    it.field.key == CompensationSkeletonField.COMPONENT_NAME.id &&
                        it.message == "All dependant components dates " +
                        "should fall within the parent component start and end date"
                }
            }
        }
    }
}
