package com.multiplier.compensation.service.validation.compensation.validators

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractOffBoardingStatus
import com.multiplier.compensation.domain.common.contract.ContractOffboarding
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.mapper.SkeletonCompensationMapperUtils.toCompensationDraft
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.compensation.validation.validators.InActiveContractValidator
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationValidatorContextFixture
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import kotlin.test.assertFalse

class InActiveContractValidatorTest {
    private lateinit var validator: InActiveContractValidator
    private lateinit var collector: ValidationDataCollector<CompensationDraft>
    private lateinit var input: ValidationInputItem

    private val contractId = 50000L
    private val testFieldKey = CommonSkeletonField.CONTRACT_ID.id
    private val contractIdKeyValuePair = KeyValuePair(
        key = CommonSkeletonField.CONTRACT_ID.id,
        value = contractId.toString(),
    )

    @BeforeEach
    fun setUp() {
        validator = InActiveContractValidator()

        input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(contractIdKeyValuePair),
        )
        val context = compensationValidatorContextFixture()
        val draft = input.toCompensationDraft(context)

        collector = ValidationDataCollector(
            drafts = mutableMapOf(input.id to draft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )
    }

    @Test
    fun `should return true when contract is active`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ACTIVE,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_REVISION,
        )

        assertTrue { validator.validate(input, context, collector) }
        assertTrue(collector.rowValidationResult[input.id].isNullOrEmpty())
    }

    @Test
    fun `should return true when contract is offboarding but fnf is not complete`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.OFFBOARDING,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            offBoardingContracts = mapOf(
                contractId to ContractOffboarding(
                    offboardingId = contractId,
                    contractId = contractId,
                    offBoardingStatus = ContractOffBoardingStatus.FNF_PENDING,
                    lastWorkingDay = LocalDate.now(),
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_REVISION,
        )

        assertTrue { validator.validate(input, context, collector) }
        assertTrue(collector.rowValidationResult[input.id].isNullOrEmpty())
    }

    @Test
    fun `should return false when draft not found`() {
        val context = compensationValidatorContextFixture()
        val input = ValidationInputItem(id = "1", fieldKeyValuePairs = listOf())
        assertFalse {
            validator.validate(
                input,
                context,
                ValidationDataCollector(inputPlusDerivedRows = emptyMap()),
            )
        }
        assertTrue { collector.rowValidationResult[input.id].isNullOrEmpty() }
    }

    @Test
    fun `should return true when ended contract has valid installment compensation`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ENDED,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            offBoardingContracts = mapOf(
                contractId to ContractOffboarding(
                    offboardingId = contractId,
                    contractId = contractId,
                    offBoardingStatus = ContractOffBoardingStatus.COMPLETED,
                    lastWorkingDay = LocalDate.now(),
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_REVISION,
        )

        val validInstallmentInput = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "Yes",
                ),
                KeyValuePair(
                    CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                    "1",
                ),
                KeyValuePair(
                    CompensationSkeletonField.BILLING_FREQUENCY.id,
                    "ONETIME",
                ),
            ),
        )

        val validCollector = ValidationDataCollector(
            drafts = mutableMapOf(
                validInstallmentInput.id to
                    validInstallmentInput.toCompensationDraft(compensationValidatorContextFixture()),
            ),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertTrue { validator.validate(validInstallmentInput, context, validCollector) }
        assertTrue(validCollector.rowValidationResult[validInstallmentInput.id].isNullOrEmpty())
    }

    @Test
    fun `should return false and add validation error when contract status is ONBOARDING`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ONBOARDING,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_REVISION,
        )

        assertFalse { validator.validate(input, context, collector) }
        assertTrue {
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == testFieldKey && it.message == "Contract is neither ACTIVE nor OFFBOARDING"
            }
        }
    }

    @Test
    fun `should return false and add validation error when contract status is DELETED`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.DELETED,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_REVISION,
        )

        assertFalse { validator.validate(input, context, collector) }
        assertTrue {
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == testFieldKey && it.message == "Contract is neither ACTIVE nor OFFBOARDING"
            }
        }
    }

    @Test
    fun `should return false when contract not found in context`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = emptyMap(), // No contracts in context
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_REVISION,
        )

        assertFalse { validator.validate(input, context, collector) }
        assertTrue { collector.rowValidationResult[input.id].isNullOrEmpty() }
    }

    @Test
    fun `should return false when draft has null contract id`() {
        val context = compensationValidatorContextFixture()
        val inputWithoutContractId = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.ENTITY_ID.id,
                    value = "1",
                ),
            ),
        )
        val draftWithNullContractId = inputWithoutContractId.toCompensationDraft(context)
        val collectorWithNullContractId = ValidationDataCollector(
            drafts = mutableMapOf(inputWithoutContractId.id to draftWithNullContractId),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(inputWithoutContractId, context, collectorWithNullContractId) }
        assertTrue { collectorWithNullContractId.rowValidationResult[inputWithoutContractId.id].isNullOrEmpty() }
    }

    @Test
    fun `should return false when ended contract has non-installment compensation`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ENDED,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val nonInstallmentInput = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "No",
                ),
            ),
        )

        val nonInstallmentCollector = ValidationDataCollector(
            drafts = mutableMapOf(
                nonInstallmentInput.id to
                    nonInstallmentInput.toCompensationDraft(compensationValidatorContextFixture()),
            ),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(nonInstallmentInput, context, nonInstallmentCollector) }
        assertTrue {
            nonInstallmentCollector.rowValidationResult[nonInstallmentInput.id]!!.any {
                it.field.key == CompensationSkeletonField.IS_INSTALLMENT.id &&
                    it.message == "Only installment compensation is allowed for ended contracts"
            }
        }
    }

    @Test
    fun `should return false when ended contract has installment with more than 1 installment`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ENDED,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val multipleInstallmentsInput = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "Yes",
                ),
                KeyValuePair(
                    CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                    "3",
                ),
            ),
        )

        val multipleInstallmentsCollector = ValidationDataCollector(
            drafts = mutableMapOf(
                multipleInstallmentsInput.id to
                    multipleInstallmentsInput.toCompensationDraft(compensationValidatorContextFixture()),
            ),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(multipleInstallmentsInput, context, multipleInstallmentsCollector) }
        assertTrue {
            multipleInstallmentsCollector.rowValidationResult[multipleInstallmentsInput.id]!!.any {
                it.field.key == CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id &&
                    it.message ==
                    "Billing frequency should be ONETIME and number of installment must be 1 for ended contracts"
            }
        }
    }

    @Test
    fun `should return false when ended contract has installment with null number of installments`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ENDED,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val nullInstallmentsInput = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "Yes",
                ),
                // No NUMBER_OF_INSTALLMENTS field, so it will be null
            ),
        )

        val nullInstallmentsCollector = ValidationDataCollector(
            drafts = mutableMapOf(
                nullInstallmentsInput.id to
                    nullInstallmentsInput.toCompensationDraft(compensationValidatorContextFixture()),
            ),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(nullInstallmentsInput, context, nullInstallmentsCollector) }
        assertTrue {
            nullInstallmentsCollector.rowValidationResult[nullInstallmentsInput.id]!!.any {
                it.field.key == CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id &&
                    it.message ==
                    "Billing frequency should be ONETIME and number of installment must be 1 for ended contracts"
            }
        }
    }

    @Test
    fun `should return false when ended contract has installment with zero installments`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ENDED,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val zeroInstallmentsInput = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "Yes",
                ),
                KeyValuePair(
                    CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                    "0",
                ),
            ),
        )

        val zeroInstallmentsCollector = ValidationDataCollector(
            drafts = mutableMapOf(
                zeroInstallmentsInput.id to
                    zeroInstallmentsInput.toCompensationDraft(compensationValidatorContextFixture()),
            ),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(zeroInstallmentsInput, context, zeroInstallmentsCollector) }
        assertTrue {
            zeroInstallmentsCollector.rowValidationResult[zeroInstallmentsInput.id]!!.any {
                it.field.key == CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id &&
                    it.message ==
                    "Billing frequency should be ONETIME and number of installment must be 1 for ended contracts"
            }
        }
    }

    @Test
    fun `should return true when ended contract has ONETIME billing frequency`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ENDED,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val onetimeBillingInput = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "Yes",
                ),
                KeyValuePair(
                    CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                    "1",
                ),
                KeyValuePair(
                    CompensationSkeletonField.BILLING_FREQUENCY.id,
                    "ONETIME",
                ),
            ),
        )

        val onetimeBillingContext = compensationValidatorContextFixture()
        val onetimeBillingDraft = onetimeBillingInput.toCompensationDraft(onetimeBillingContext)

        val onetimeBillingCollector = ValidationDataCollector(
            drafts = mutableMapOf(onetimeBillingInput.id to onetimeBillingDraft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertTrue { validator.validate(onetimeBillingInput, context, onetimeBillingCollector) }
        assertTrue(onetimeBillingCollector.rowValidationResult[onetimeBillingInput.id].isNullOrEmpty())
    }

    @Test
    fun `should return false when ended contract has MONTHLY billing frequency`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ENDED,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val monthlyBillingInput = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "Yes",
                ),
                KeyValuePair(
                    CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                    "1",
                ),
                KeyValuePair(
                    CompensationSkeletonField.BILLING_FREQUENCY.id,
                    "MONTHLY",
                ),
            ),
        )

        val monthlyBillingContext = compensationValidatorContextFixture()
        val monthlyBillingDraft = monthlyBillingInput.toCompensationDraft(monthlyBillingContext)

        val monthlyBillingCollector = ValidationDataCollector(
            drafts = mutableMapOf(monthlyBillingInput.id to monthlyBillingDraft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(monthlyBillingInput, context, monthlyBillingCollector) }
        assertTrue {
            monthlyBillingCollector.rowValidationResult[monthlyBillingInput.id]!!.any {
                it.field.key == CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id &&
                    it.message ==
                    "Billing frequency should be ONETIME and number of installment must be 1 for ended contracts"
            }
        }
    }

    @Test
    fun `should return false when ended contract has ANNUALLY billing frequency`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ENDED,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val annuallyBillingInput = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "Yes",
                ),
                KeyValuePair(
                    CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                    "1",
                ),
                KeyValuePair(
                    CompensationSkeletonField.BILLING_FREQUENCY.id,
                    "ANNUALLY",
                ),
            ),
        )

        val annuallyBillingContext = compensationValidatorContextFixture()
        val annuallyBillingDraft = annuallyBillingInput.toCompensationDraft(annuallyBillingContext)

        val annuallyBillingCollector = ValidationDataCollector(
            drafts = mutableMapOf(annuallyBillingInput.id to annuallyBillingDraft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(annuallyBillingInput, context, annuallyBillingCollector) }
        assertTrue {
            annuallyBillingCollector.rowValidationResult[annuallyBillingInput.id]!!.any {
                it.field.key == CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id &&
                    it.message ==
                    "Billing frequency should be ONETIME and number of installment must be 1 for ended contracts"
            }
        }
    }

    @Test
    fun `should return false when ended contract has WEEKLY billing frequency`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ENDED,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val weeklyBillingInput = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "Yes",
                ),
                KeyValuePair(
                    CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                    "1",
                ),
                KeyValuePair(
                    CompensationSkeletonField.BILLING_FREQUENCY.id,
                    "WEEKLY",
                ),
            ),
        )

        val weeklyBillingContext = compensationValidatorContextFixture()
        val weeklyBillingDraft = weeklyBillingInput.toCompensationDraft(weeklyBillingContext)

        val weeklyBillingCollector = ValidationDataCollector(
            drafts = mutableMapOf(weeklyBillingInput.id to weeklyBillingDraft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(weeklyBillingInput, context, weeklyBillingCollector) }
        assertTrue {
            weeklyBillingCollector.rowValidationResult[weeklyBillingInput.id]!!.any {
                it.field.key == CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id &&
                    it.message ==
                    "Billing frequency should be ONETIME and number of installment must be 1 for ended contracts"
            }
        }
    }

    @Test
    fun `should return false when ended contract has SEMIMONTHLY billing frequency`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ENDED,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val semiMonthlyBillingInput = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "Yes",
                ),
                KeyValuePair(
                    CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                    "1",
                ),
                KeyValuePair(
                    CompensationSkeletonField.BILLING_FREQUENCY.id,
                    "SEMIMONTHLY",
                ),
            ),
        )

        val semiMonthlyBillingContext = compensationValidatorContextFixture()
        val semiMonthlyBillingDraft = semiMonthlyBillingInput.toCompensationDraft(semiMonthlyBillingContext)

        val semiMonthlyBillingCollector = ValidationDataCollector(
            drafts = mutableMapOf(semiMonthlyBillingInput.id to semiMonthlyBillingDraft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(semiMonthlyBillingInput, context, semiMonthlyBillingCollector) }
        assertTrue {
            semiMonthlyBillingCollector.rowValidationResult[semiMonthlyBillingInput.id]!!.any {
                it.field.key == CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id &&
                    it.message ==
                    "Billing frequency should be ONETIME and number of installment must be 1 for ended contracts"
            }
        }
    }

    @Test
    fun `should return false when ended contract has ONETIME billing frequency but null installments`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ENDED,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val onetimeNullInstallmentsInput = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "Yes",
                ),
                KeyValuePair(
                    CompensationSkeletonField.BILLING_FREQUENCY.id,
                    "ONETIME",
                ),
                // No NUMBER_OF_INSTALLMENTS field, so it will be null
            ),
        )

        val onetimeNullInstallmentsContext = compensationValidatorContextFixture()
        val onetimeNullInstallmentsDraft = onetimeNullInstallmentsInput.toCompensationDraft(
            onetimeNullInstallmentsContext,
        )

        val onetimeNullInstallmentsCollector = ValidationDataCollector(
            drafts = mutableMapOf(onetimeNullInstallmentsInput.id to onetimeNullInstallmentsDraft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(onetimeNullInstallmentsInput, context, onetimeNullInstallmentsCollector) }
        assertTrue {
            onetimeNullInstallmentsCollector.rowValidationResult[onetimeNullInstallmentsInput.id]!!.any {
                it.field.key == CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id &&
                    it.message ==
                    "Billing frequency should be ONETIME and number of installment must be 1 for ended contracts"
            }
        }
    }

    @Test
    fun `should return false when ended contract has ONETIME billing frequency but 2 installments`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ENDED,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val onetimeTwoInstallmentsInput = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "Yes",
                ),
                KeyValuePair(
                    CompensationSkeletonField.BILLING_FREQUENCY.id,
                    "ONETIME",
                ),
                KeyValuePair(
                    CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                    "2",
                ),
            ),
        )

        val onetimeTwoInstallmentsContext = compensationValidatorContextFixture()
        val onetimeTwoInstallmentsDraft = onetimeTwoInstallmentsInput.toCompensationDraft(onetimeTwoInstallmentsContext)

        val onetimeTwoInstallmentsCollector = ValidationDataCollector(
            drafts = mutableMapOf(onetimeTwoInstallmentsInput.id to onetimeTwoInstallmentsDraft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(onetimeTwoInstallmentsInput, context, onetimeTwoInstallmentsCollector) }
        assertTrue {
            onetimeTwoInstallmentsCollector.rowValidationResult[onetimeTwoInstallmentsInput.id]!!.any {
                it.field.key == CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id &&
                    it.message ==
                    "Billing frequency should be ONETIME and number of installment must be 1 for ended contracts"
            }
        }
    }
}
