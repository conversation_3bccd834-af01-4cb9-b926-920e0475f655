package com.multiplier.compensation.service.validation.compensation.validators

import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationschema.CompensationSchema
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.mapper.SkeletonCompensationMapperUtils.toCompensationDraft
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.compensation.validation.validators.MandatoryComponentValidator
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationSchemaItemFixture
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertFalse

class MandatoryComponentValidatorTest {
    private lateinit var validator: MandatoryComponentValidator
    private lateinit var context: CompensationValidatorContext
    private lateinit var collector: ValidationDataCollector<CompensationDraft>

    private val comp1SchemaItemId1 = UUID.randomUUID()
    private val comp1SchemaItemId2 = UUID.randomUUID()

    @BeforeEach
    fun setUp() {
        validator = MandatoryComponentValidator()

        val schemaItemMap = mapOf(
            "Base Pay" to compensationSchemaItemFixture(
                id = comp1SchemaItemId1,
                category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                componentName = "Base Pay",
                isMandatory = true,
            ),
            "Optional Pay" to compensationSchemaItemFixture(
                id = comp1SchemaItemId2,
                category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP,
                componentName = "Optional Pay",
                isMandatory = false,
            ),
        )
        context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = mapOf(
                CompensationSchema(
                    id = UUID.randomUUID(),
                    entityId = 1L,
                    country = CountryCode.USA,
                    companyId = 1L,
                    isDefault = true,
                    name = "Default Schema",
                    isActive = true,
                    createdOn = LocalDateTime.now(),
                    createdBy = 1L,
                    updatedOn = LocalDateTime.now(),
                    updatedBy = 1L,
                    schemaItems = schemaItemMap.values.toList(),
                    tags = listOf("GLOBAL_PAYROLL"),
                    configurationScope = ConfigurationScope.COMPANY,
                    description = "Test schema description",
                ) to schemaItemMap,
            ),
            paySchedules = emptyMap(),
            contracts = mapOf(
                50000L to Contract(
                    id = 50000L,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ONBOARDING,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = setOf(50000L),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        collector =
            ValidationDataCollector(
                drafts = mutableMapOf(),
                rowValidationResult = mutableMapOf(),
                inputPlusDerivedRows = emptyMap(),
            )
    }

    @Test
    fun `validate should return false when draft is missing`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(),
        )
        assertFalse { validator.validate(input, context, collector) }
    }

    @Test
    fun `validate should return false when employeeId is missing`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(),
        )

        val draft = input.toCompensationDraft(context)
        collector.drafts[input.id] = draft

        assertFalse { validator.validate(input, context, collector) }
    }

    @Test
    fun `validate should return false when mandatory components are missing`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.EMPLOYEE_ID.id,
                    value = "EMP1",
                ),
                KeyValuePair(
                    key = CompensationSkeletonField.COMPONENT_NAME.id,
                    value = "Optional Pay",
                ),
            ),
        )

        val draft = input.toCompensationDraft(context)
        collector.drafts[input.id] = draft

        assertFalse { validator.validate(input, context, collector) }
        assertTrue {
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == CommonSkeletonField.EMPLOYEE_ID.id &&
                    it.message == "Mandatory components [Base Pay] are not provided for employee [EMP1]"
            }
        }
    }

    @Test
    fun `validate should return true when all mandatory components are present`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.EMPLOYEE_ID.id,
                    value = "EMP1",
                ),
                KeyValuePair(
                    key = CompensationSkeletonField.COMPONENT_NAME.id,
                    value = "Base Pay",
                ),
            ),
        )

        val draft = input.toCompensationDraft(context)
        collector.drafts[input.id] = draft

        assertTrue { validator.validate(input, context, collector) }
    }
}
