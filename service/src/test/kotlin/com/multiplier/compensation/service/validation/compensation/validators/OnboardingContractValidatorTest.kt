package com.multiplier.compensation.service.validation.compensation.validators

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.mapper.SkeletonCompensationMapperUtils.toCompensationDraft
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.compensation.validation.validators.OnboardingContractValidator
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationValidatorContextFixture
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import kotlin.test.assertFalse

class OnboardingContractValidatorTest {
    private lateinit var validator: OnboardingContractValidator
    private lateinit var collector: ValidationDataCollector<CompensationDraft>
    private lateinit var input: ValidationInputItem

    private val testFieldKey = CommonSkeletonField.CONTRACT_ID.id

    @BeforeEach
    fun setUp() {
        validator = OnboardingContractValidator()

        input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.CONTRACT_ID.id,
                    value = "50000",
                ),
            ),
        )
        val context = compensationValidatorContextFixture()
        val draft = input.toCompensationDraft(context)

        collector = ValidationDataCollector(
            drafts = mutableMapOf(input.id to draft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )
    }

    @Test
    fun `should return false when draft not found`() {
        val context = compensationValidatorContextFixture()
        val input = ValidationInputItem(id = "1", fieldKeyValuePairs = listOf())
        assertFalse {
            validator.validate(
                input,
                context,
                ValidationDataCollector(inputPlusDerivedRows = emptyMap()),
            )
        }
        assertTrue { collector.rowValidationResult[input.id].isNullOrEmpty() }
    }

    @Test
    fun `should return true when contract is onboarding during upsert flow`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                50000L to Contract(
                    id = 50000L,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ONBOARDING,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        assertTrue { validator.validate(input, context, collector) }
        assertTrue(collector.rowValidationResult[input.id].isNullOrEmpty())
    }

    @Test
    fun `should return false when contract is not onboarding during upsert flow`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                50000L to Contract(
                    id = 50000L,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ACTIVE,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        assertFalse { validator.validate(input, context, collector) }
        assertTrue(
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == testFieldKey &&
                    it.message == "Contract should be " +
                    "in onboarding status to setup compensations"
            },
        )
    }

    @Test
    fun `should return false when contract id is invalid during upsert flow`() {
        // Create context with empty contracts map to simulate invalid contract id
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = emptyMap(), // Empty contracts map
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        // Create input with a contract ID that doesn't exist in context
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.CONTRACT_ID.id,
                    value = "999999", // Use different contract ID than what's in setUp()
                ),
            ),
        )

        val draft = input.toCompensationDraft(context)
        val collector = ValidationDataCollector(
            drafts = mutableMapOf(input.id to draft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(input, context, collector) }
        assertTrue(
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == testFieldKey &&
                    it.message == "Contract id is either not passed in input or is invalid"
            },
        )
    }

    @Test
    fun `should return false when contract status is active during upsert flow`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = mapOf(
                50000L to Contract(
                    id = 50000L,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ACTIVE,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        assertFalse { validator.validate(input, context, collector) }
        assertTrue(
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == testFieldKey &&
                    it.message == "Contract should be in onboarding status to setup compensations"
            },
        )
    }

    @Test
    fun `should return false when contract draft status is not passed during validate flow and contract is not created`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = emptyMap(),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.VALIDATE,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.CONTRACT_STATUS.id,
                    value = null,
                ),
            ),
        )

        val draft = input.toCompensationDraft(context)

        collector = ValidationDataCollector(
            drafts = mutableMapOf(input.id to draft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(input, context, collector) }
        assertTrue(
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == CommonSkeletonField.CONTRACT_STATUS.id &&
                    it.message == "Contract onboarding status is not passed in input"
            },
        )
    }

    @Test
    fun `should return false when contract draft status is active during validate flow and contract is not created`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = emptyMap(),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.VALIDATE,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.CONTRACT_STATUS.id,
                    value = "ACTIVE",
                ),
            ),
        )

        val draft = input.toCompensationDraft(context)

        collector = ValidationDataCollector(
            drafts = mutableMapOf(input.id to draft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertFalse { validator.validate(input, context, collector) }
        assertTrue(
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == CommonSkeletonField.CONTRACT_ID.id &&
                    it.message == "Contract should be in onboarding status to setup compensations"
            },
        )
    }

    @Test
    fun `should return true when contract draft status is onboarding during validate flow and contract is not created`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = emptyMap(),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.VALIDATE,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.CONTRACT_STATUS.id,
                    value = "ONBOARDING",
                ),
            ),
        )

        val draft = input.toCompensationDraft(context)

        collector = ValidationDataCollector(
            drafts = mutableMapOf(input.id to draft),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        assertTrue { validator.validate(input, context, collector) }
        assertTrue(collector.rowValidationResult[input.id].isNullOrEmpty())
    }
}
