package com.multiplier.compensation.service.validation.compensationschema.validators

import com.multiplier.compensation.domain.compensationschema.SchemaTags
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaDraft
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaValidationContext
import com.multiplier.compensation.service.compensationschema.mapper.CompensationSchemaSkeletonField
import com.multiplier.compensation.service.compensationschema.validation.validators.SchemaNameGroupValidator
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class SchemaNameGroupValidatorTest {
    private lateinit var validator: SchemaNameGroupValidator
    private val skeleton = mockk<Skeleton>()

    @BeforeEach
    fun setUp() {
        validator = SchemaNameGroupValidator()
    }

    @Test
    fun `should return true when only one schema name is present`() {
        val inputs = listOf(
            ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(
                        key = CompensationSchemaSkeletonField.SCHEMA_NAME.id,
                        value = "Schema1",
                    ),
                ),
            ),
            ValidationInputItem(
                id = "2",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(
                        key = CompensationSchemaSkeletonField.SCHEMA_NAME.id,
                        value = "Schema1",
                    ),
                ),
            ),
        )
        val context = CompensationSchemaValidationContext(
            skeleton = skeleton,
            entityId = 1L,
            schemaInputItems = inputs,
            schemaList = emptyList(),
            paySchedules = emptyList(),
        )
        val collector = mockk<ValidationDataCollector<CompensationSchemaDraft>>(relaxed = true)

        val result = validator.validate(inputs, context, collector)

        assertTrue(result)
    }

    @Test
    fun `should return false and log an error when multiple schema names are detected`() {
        val inputs = listOf(
            ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(
                        key = CompensationSchemaSkeletonField.SCHEMA_NAME.id,
                        value = "Schema1",
                    ),
                ),
            ),
            ValidationInputItem(
                id = "2",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(
                        key = CompensationSchemaSkeletonField.SCHEMA_NAME.id,
                        value = "Schema2",
                    ),
                ),
            ),
        )
        val context = CompensationSchemaValidationContext(
            skeleton = skeleton,
            entityId = 1L,
            schemaInputItems = inputs,
            schemaList = emptyList(),
            paySchedules = emptyList(),
        )
        val collector: ValidationDataCollector<CompensationSchemaDraft> =
            ValidationDataCollector<CompensationSchemaDraft>(
                drafts = mutableMapOf(),
                rowValidationResult = mutableMapOf(),
                inputPlusDerivedRows = emptyMap(),
            )

        val result = validator.validate(inputs, context, collector)

        assertFalse(result)

        assertTrue {
            collector.rowValidationResult["1"]!!.any {
                it.field.key == CompensationSchemaSkeletonField.SCHEMA_NAME.name &&
                    it.message.startsWith("Only one unique schema name is allowed for an entity")
            }
        }
    }

    @Test
    fun `should return true when no schema name is present`() {
        val inputs = listOf(
            ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = emptyList(),
            ),
            ValidationInputItem(
                id = "2",
                fieldKeyValuePairs = emptyList(),
            ),
        )
        val context = CompensationSchemaValidationContext(
            skeleton = skeleton,
            entityId = 1L,
            schemaInputItems = inputs,
            schemaList = emptyList(),
            paySchedules = emptyList(),
        )
        val collector = mockk<ValidationDataCollector<CompensationSchemaDraft>>(relaxed = true)

        val result = validator.validate(inputs, context, collector)

        assertTrue(result)
    }

    @Test
    fun `should return true when schema name is present for EOR`() {
        val inputs = listOf(
            ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(
                        key = CompensationSchemaSkeletonField.SCHEMA_NAME.id,
                        value = "Name",
                    ),
                    KeyValuePair(
                        key = CompensationSchemaSkeletonField.TAGS.id,
                        value = SchemaTags.MULTIPLIER_EOR_OPS_APPROVED.toString(),
                    ),
                ),
            ),
            ValidationInputItem(
                id = "2",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(
                        key = CompensationSchemaSkeletonField.SCHEMA_NAME.id,
                        value = "Name",
                    ),
                    KeyValuePair(
                        key = CompensationSchemaSkeletonField.TAGS.id,
                        value = SchemaTags.MULTIPLIER_EOR_OPS_APPROVED.toString(),
                    ),
                ),
            ),
        )
        val context = CompensationSchemaValidationContext(
            skeleton = skeleton,
            entityId = 1L,
            schemaInputItems = inputs,
            schemaList = emptyList(),
            paySchedules = emptyList(),
        )
        val collector = mockk<ValidationDataCollector<CompensationSchemaDraft>>(relaxed = true)

        val result = validator.validate(inputs, context, collector)

        assertTrue(result)
    }

    @Test
    fun `should return false when combination of schema name and null is present`() {
        val inputs = listOf(
            ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = emptyList(),
            ),
            ValidationInputItem(
                id = "2",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(
                        key = CompensationSchemaSkeletonField.SCHEMA_NAME.id,
                        value = "Schema1",
                    ),
                ),
            ),
        )
        val context = CompensationSchemaValidationContext(
            skeleton = skeleton,
            entityId = 1L,
            schemaInputItems = inputs,
            schemaList = emptyList(),
            paySchedules = emptyList(),
        )
        val collector = mockk<ValidationDataCollector<CompensationSchemaDraft>>(relaxed = true)

        val result = validator.validate(inputs, context, collector)

        assertFalse(result)
    }
}
