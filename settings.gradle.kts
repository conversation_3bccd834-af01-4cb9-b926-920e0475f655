rootProject.name = "compensation-service"

enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

include(
    "app",
    "database",
    "domain",
    "graph",
    "grpc-client",
    "grpc-schema",
    "grpc-server",
    "kafka-consumer",
    "service",
    "testing",
)

dependencyResolutionManagement {
    versionCatalogs {
        create("multiplier") {
            version("access-control", "2.0.9")
            version("common", "7.6.23")
            version("graphql", "1.1.3")
            version("grpc-common", "1.2.31-pr1")
            version("grpc", "1.69.0")
            version("logging-keys", "1.0.5")
            version("transaction", "1.1.15")
            version("multiplier-starter", "2.2.0")
            version("messaging-registry", "0.2.43")

            // Access Control
            library("access-control-spring", "com.multiplier", "access-control-spring").versionRef("access-control")
            library("access-control-spring-2", "com.multiplier", "access-control-spring-2").versionRef("access-control")

            // Transaction
            library("transaction-coroutine", "com.multiplier.transaction", "transaction-coroutine").versionRef("transaction")
            library("transaction-coroutine-spring", "com.multiplier.transaction", "transaction-coroutine-spring").versionRef("transaction")
            library("transaction-database-jooq-spring", "com.multiplier.transaction", "transaction-database-jooq-spring").versionRef("transaction")
            library("transaction-graphql-dgs", "com.multiplier.transaction", "transaction-graphql-dgs").versionRef("transaction")
            library("transaction-grpc-spring", "com.multiplier.transaction", "transaction-grpc-spring").versionRef("transaction")
            library("transaction-spring", "com.multiplier.transaction", "transaction-spring").versionRef("transaction")

            // Framework
            library("graphql-spring", "com.multiplier.platform", "graphql-spring").versionRef("graphql")

            // For all internal multiplier libraries
            library("spring-autoconfig-starter", "com.multiplier.platform", "spring-autoconfig-starter").versionRef("multiplier-starter")
            library("platform.spring-starter", "com.multiplier.platform", "spring-starter").versionRef("multiplier-starter")
            library("spring-messaging-kafka-starter", "com.multiplier.platform", "spring-messaging-kafka-starter").versionRef("multiplier-starter")
            library("spring-jobs-starter", "com.multiplier.platform", "spring-jobs-starter").versionRef("multiplier-starter")
            library("spring-user-context-starter", "com.multiplier.platform", "spring-user-context-starter").versionRef("multiplier-starter")

            // Growthbook
            library("growthbook-sdk", "com.multiplier", "growthbook-sdk").version("1.1.2")

            // Logging
            library("logging-keys", "com.multiplier.common", "logging-keys").versionRef("logging-keys")

            // GraphQL
            library("compensation-service-graph", "com.multiplier", "compensation-service-graph").version("1.14.740")

            // gRPC
            library("grpc-common", "com.multiplier.grpc", "grpc-common").versionRef("grpc-common")
            library("grpc-bulkupload", "com.multiplier.grpc", "grpc-bulkupload").versionRef("grpc-common")
            library("company-service-schema", "com.multiplier", "company-service-schema").version("3.5.96")
            library("contract-offboarding-service-schema", "com.multiplier", "contract-offboarding-service-schema").version("1.8.4")
            library("contract-service-schema", "com.multiplier", "contract-service-schema").version("1.4.72")
            library("core-service-schema", "com.multiplier", "core-service-schema").version("1.1.186")
            library("country-service-schema", "com.multiplier", "country-service-schema").version("1.9.6")
            library("payroll-schema-service-grpc-schema", "com.multiplier", "payroll-schema-service-grpc-schema").version("0.0.20")
            library("org-management-service-schema", "com.multiplier", "org-management-service-schema").version("1.0.11")
            library("pigeon-service-schema", "com.multiplier", "pigeon-service-schema").version("1.3.9")
            library("pigeon-service-client", "com.multiplier", "pigeon-service-client").version("1.3.9")

            // Messaging
            library("messaging-payroll-input-service", "com.multiplier.messaging", "payroll-input-service").versionRef("messaging-registry")

            // Common
            library("common-transport-spring", "com.multiplier.common", "transport-spring").versionRef("common")

            library("platform.spring-messaging-starter", "com.multiplier.platform", "spring-messaging-kafka-starter").versionRef("multiplier-starter")
        }

        create("kt") {
            version("kotlin", "2.1.10")
            version("kotlinx.coroutines", "1.10.1")

            plugin("jvm", "org.jetbrains.kotlin.jvm").versionRef("kotlin")
            plugin("spring", "org.jetbrains.kotlin.plugin.spring").versionRef("kotlin")
            plugin("jpa", "org.jetbrains.kotlin.plugin.jpa").versionRef("kotlin")

            // Kotlin
            library("reflect", "org.jetbrains.kotlin", "kotlin-reflect").versionRef("kotlin")
            library("test-junit5", "org.jetbrains.kotlin", "kotlin-test-junit5").versionRef("kotlin")

            library("coroutines-slf4j", "org.jetbrains.kotlinx", "kotlinx-coroutines-slf4j").versionRef("kotlinx.coroutines")
            library("coroutines-core", "org.jetbrains.kotlinx", "kotlinx-coroutines-core").versionRef("kotlinx.coroutines")
            library("coroutines-test", "org.jetbrains.kotlinx", "kotlinx-coroutines-test").versionRef("kotlinx.coroutines")

            library("logging", "io.github.oshai", "kotlin-logging").version("7.0.3")
        }

        create("spring") {
            version("boot", "3.3.9")
            version("kafka", "3.2.4")

            plugin("boot", "org.springframework.boot").versionRef("boot")

            library("boot", "org.springframework.boot", "spring-boot").versionRef("boot")
            library("boot-starter", "org.springframework.boot", "spring-boot-starter").versionRef("boot")
            library("boot-starter-web", "org.springframework.boot", "spring-boot-starter-web").versionRef("boot")
            library("boot-starter-security", "org.springframework.boot", "spring-boot-starter-security").versionRef("boot")
            library("boot-starter-data-jpa", "org.springframework.boot", "spring-boot-starter-data-jpa").versionRef("boot")
            library("boot-starter-jdbc", "org.springframework.boot", "spring-boot-starter-jdbc").versionRef("boot")
            library("boot-starter-validation", "org.springframework.boot", "spring-boot-starter-validation").versionRef("boot")
            library("boot-starter-actuator", "org.springframework.boot", "spring-boot-starter-actuator").versionRef("boot")
            library("boot-starter-aop", "org.springframework.boot", "spring-boot-starter-aop").versionRef("boot")
            library("boot-starter-test", "org.springframework.boot", "spring-boot-starter-test").versionRef("boot")

            library("kafka", "org.springframework.kafka", "spring-kafka").versionRef("kafka")
        }

        create("libs") {
            version("dgs", "8.7.1")
            version("protobuf", "3.25.5")
            version("grpc", "1.63.0")
            version("grpcKotlin", "1.4.1")
            version("protocGenDoc", "1.5.1")
            version("jooq", "3.19.1")
            version("shedlock", "5.15.0")
            version("commons-lang3", "3.13.0")

            plugin("protobuf", "com.google.protobuf").version("0.9.4")
            plugin("jooq-codegen", "org.jooq.jooq-codegen-gradle").versionRef("jooq")
            plugin("sonarqube", "org.sonarqube").version("5.0.0.4638")
            plugin("detekt", "io.gitlab.arturbosch.detekt").version("1.23.5")
            plugin("ktlint", "org.jlleitschuh.gradle.ktlint").version("12.1.0")

            // GraphQL
            library("graphql-dgs-spring-boot-starter", "com.netflix.graphql.dgs", "graphql-dgs-spring-boot-starter").versionRef("dgs")
            library("graphql-dgs-extended-scalars", "com.netflix.graphql.dgs", "graphql-dgs-extended-scalars").versionRef("dgs")

            // GRPC
            library("grpc-spring-boot-starter", "net.devh", "grpc-spring-boot-starter").version("3.1.0.RELEASE")
            library("grpc-protobuf", "io.grpc", "grpc-protobuf").versionRef("grpc")
            library("grpc-stub", "io.grpc", "grpc-stub").versionRef("grpc")
            library("grpc-kotlin-stub", "io.grpc", "grpc-kotlin-stub").versionRef("grpcKotlin")
            library("protobuf-kotlin", "com.google.protobuf", "protobuf-kotlin").versionRef("protobuf")

            // Serialization
            library("java-uuid-generator", "com.fasterxml.uuid", "java-uuid-generator").version("5.0.0")
            library("jackson-module-kotlin", "com.fasterxml.jackson.module", "jackson-module-kotlin").version("2.17.1")
            library("json", "org.json", "json").version("20231013")
            library("kafka-protobuf-serde", "com.github.daniel-shuy", "kafka-protobuf-serde").version("2.2.0")

            // Cache
            library("caffeine", "com.github.ben-manes.caffeine", "caffeine").version("3.1.8")

            // Database
            library("jooq-kotlin", "org.jooq", "jooq-kotlin").versionRef("jooq")
            library("jooq-meta-extensions", "org.jooq", "jooq-meta-extensions").versionRef("jooq")
            library("jooq-meta-extensions-liquibase", "org.jooq", "jooq-meta-extensions-liquibase").versionRef("jooq")
            library("hikari", "com.zaxxer", "HikariCP").version("5.1.0")
            library("postgresql", "org.postgresql", "postgresql").version("42.7.3")

            // Liquibase
            library("liquibase-core", "org.liquibase", "liquibase-core").version("4.28.0")

            // Logging
            library("logback-classic", "ch.qos.logback", "logback-classic").version("1.5.16")
            library("janino", "org.codehaus.janino", "janino").version("3.1.12") // Logback conditions
            library("logstash-logback-encoder", "net.logstash.logback", "logstash-logback-encoder").version("7.4") // Structured (JSON) logging

            // Shedlock
            library("shedlock-spring", "net.javacrumbs.shedlock", "shedlock-spring").versionRef("shedlock")
            library("shedlock-provider-jooq", "net.javacrumbs.shedlock", "shedlock-provider-jooq").versionRef("shedlock")

            library("commons-lang3", "org.apache.commons", "commons-lang3").versionRef("commons-lang3")

            // EvalEx
            library("evalex", "com.udojava", "EvalEx").version("2.7")
        }

        create("test") {
            version("junit", "5.10.2")

            library("junit-jupiter-api", "org.junit.jupiter", "junit-jupiter-api").versionRef("junit")
            library("junit-jupiter-params", "org.junit.jupiter", "junit-jupiter-params").versionRef("junit")
            library("junit-jupiter-engine", "org.junit.jupiter", "junit-jupiter-engine").versionRef("junit")
            library("assertk", "com.willowtreeapps.assertk", "assertk").version("0.28.1")
            library("mockk", "io.mockk", "mockk").version("1.13.11")
            library("awaitility", "org.awaitility", "awaitility").version("4.2.1")
            library("h2", "com.h2database", "h2").version("2.2.224")
            library("spring-security-test", "org.springframework.security", "spring-security-test").version("6.0.2")
        }
    }
}
include("kafka-consumer")
