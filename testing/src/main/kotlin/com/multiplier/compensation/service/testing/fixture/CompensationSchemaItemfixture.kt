package com.multiplier.compensation.service.testing.fixture

import com.multiplier.compensation.database.mapper.common.toDatabase
import com.multiplier.compensation.database.mapper.compensation.common.toDatabase
import com.multiplier.compensation.database.tables.records.CompensationSchemaItemRecord
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.ItemType
import java.time.LocalDateTime
import java.util.UUID

fun createCompensationSchemaItemRecordFixture(
    id: UUID = UUID.randomUUID(),
    schemaId: UUID = UUID.randomUUID(),
    componentName: String = "componentName",
    category: String = "category",
    isTaxable: Boolean = true,
    isFixed: Boolean = true,
    isProrated: Boolean = true,
    isMandatory: Boolean = true,
    isPartOfBasePay: Boolean = true,
    isActive: Boolean = true,
    createdOn: LocalDateTime = LocalDateTime.now(),
    createdBy: Long = -1,
    updatedOn: LocalDateTime = LocalDateTime.now(),
    updatedBy: Long = -1,
    label: String = "label",
    itemType: ItemType = ItemType.INPUT,
    validation: String? = null,
    calculation: String? = null,
    billingRateType: BillingRateType = BillingRateType.VALUE,
    isOvertimeEligible: Boolean = false,
    description: String = "Test component description",
    billingFrequency: BillingFrequency? = BillingFrequency.MONTHLY,
    payScheduleId: UUID? = null,
    currency: String = "USD",
    isPartOfCtc: Boolean = true,
): CompensationSchemaItemRecord = CompensationSchemaItemRecord(
    id = id,
    schemaId = schemaId,
    componentName = componentName,
    category = category,
    isTaxable = isTaxable,
    isFixed = isFixed,
    isProrated = isProrated,
    isMandatory = isMandatory,
    isPartOfBasePay = isPartOfBasePay,
    isActive = isActive,
    createdOn = createdOn,
    createdBy = createdBy,
    updatedOn = updatedOn,
    updatedBy = updatedBy,
    label = label,
    itemType = itemType.toDatabase(),
    validation = validation,
    calculation = calculation,
    billingRateType = billingRateType.toDatabase(),
    isOvertimeEligible = isOvertimeEligible,
    description = description,
    billingFrequency = billingFrequency?.toDatabase(),
    payScheduleId = payScheduleId,
    currency = currency,
    isPartOfCtc = isPartOfCtc,
)
