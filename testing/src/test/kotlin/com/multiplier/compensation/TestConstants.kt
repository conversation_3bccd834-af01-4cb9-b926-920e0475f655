package com.multiplier.compensation

object TestConstants {
    const val HEADER_ROW = 1
    const val INDEX_OF_ERROR_MSG = "0"
    const val PLATFORM_KEY_PREFIX = "PK"
    const val MONTH = 11
    const val DAY = 1
    const val HOUR = 2
    const val MINUTES = 56
    const val COMPENSATION_FILTER = "CONTRACT_BASE_PAY"
    const val SLEEP_DURATION_MILLIS = 1000L
    const val COMP_EXCLUDED_CATEGORIES = "excluded_categories"
    const val COMP_INCLUDED_CATEGORIES = "included_categories"
    const val COMP_STATUS = "status"
    const val COMP_CONTRACT_ID = "contract_id"
    const val COMP_IDS = "ids"
    const val ID = "ID"
    const val SCHEMA_ITEM_ID = "SCHEMA_ITEM_ID"
    const val COMPENSATION_SCHEMA_INPUT_LIST = "compensationSchemaInputList"
    const val PK_ENTITY_ID = "PK_ENTITY_ID"
    const val ENTITY_ID = "ENTITY_ID"
    const val EXPECTED_SUCCESS_STATUS = "EXPECTED_SUCCESS_STATUS"
    const val ERROR_MSG = "ERROR_MSG"
    const val COMPANY_ID = "COMPANY_ID"
    const val CONTRACT_ID = "CONTRACT_ID"
    const val COUNTRY_CODE = "COUNTRY_CODE"
    const val COMPENSATION_ID = "COMPENSATION_ID"
    const val COMPENSATION_SCHEMA_ID = "COMPENSATION_SCHEMA_ID"
    const val COMPENSATION = "compensation"
    const val COMPENSATION_INPUT = "compensationInput"
    const val COMPENSATION_ITEM = "compensationItem"
    const val COMPENSATION_LOG = "compensationLog"
    const val EMPLOYEE_DEDUCTION = "EMPLOYEE_DEDUCTION"
    const val EMPLOYER_DEDUCTION = "EMPLOYER_DEDUCTION"
    const val END_DATE = "END_DATE"
    const val BILLING_RATE = "BILLING_RATE"
    const val START_DATE = "START_DATE"
    const val BILLING_FREQUENCY = "BILLING_FREQUENCY"
    const val CURRENCY = "CURRENCY"
    const val NEW = "NEW"
    const val STATUS = "STATUS"
    const val PAY_SUPPLEMENT = "PAY_SUPPLEMENT"
    const val COMPENSATION_REVISION = "COMPENSATION_REVISION"
    const val REQUEST_TYPE = "REQUEST_TYPE"
    const val ACTIVATED = "ACTIVATED"
    const val DEDUCTION = "DEDUCTION"
    const val COMPENSATION_START_DATE = "COMPENSATION_START_DATE"
    const val COMPENSATION_END_DATE = "COMPENSATION_END_DATE"
    const val SUCCEEDED = "SUCCEEDED"
}
