package com.multiplier.compensation.assertions

import com.multiplier.compensation.ScenarioContext
import com.multiplier.compensation.TestConstants
import com.multiplier.compensation.dbRepository.CompensationDb
import com.multiplier.compensation.grpc.schema.UpdateCompensationStatusBulkResponse
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensationschema.mapper.CompensationSchemaSkeletonField
import com.multiplier.compensation.utils.GeneralUtils
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkResponse
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkResponse
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputRequest
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputResponse
import org.jooq.Record
import org.junit.jupiter.api.TestInstance
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.stereotype.Component
import org.springframework.test.context.ActiveProfiles
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("integration-test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Component
class CompensationAssertions(
    @Autowired
    private val compensationDb: CompensationDb,
    @Autowired
    private val compensationLogAssertions: CompensationLogAssertions,
    @Autowired
    private val compensationInputAssertions: CompensationInputAssertions,
    @Autowired
    private val compensationItemAssertions: CompensationItemAssertions,
) {
    fun assertCompensationStatus(
        bulkUpdateCompensationStatusMap: Map<String, Any>,
        validateUpsertOutputMapPerEntity: Map<String, MutableList<ValidateUpsertInputRequest>>,
    ) {
        validateUpsertOutputMapPerEntity.forEach { (entityName, validateUpsertInputList) ->
            val updateCompensationStatusBulkResponse = bulkUpdateCompensationStatusMap[entityName]
                as? UpdateCompensationStatusBulkResponse

            validateUpsertInputList.forEachIndexed { index, validateUpsertInput ->
                val contractId = validateUpsertInput.keys?.contractId
                val errMsg = validateUpsertInput.dataMap?.get("ERROR_MESSAGE")
                val componentName = validateUpsertInput.dataMap?.get(CompensationSchemaSkeletonField.COMPONENT_NAME.id)
                val billingRate = validateUpsertInput.dataMap?.get(CompensationSkeletonField.BILLING_RATE.id)
                if (validateUpsertInput.dataMap["EXPECTED_SUCCESS_STATUS"] == "TRUE") {
                    updateCompensationStatusBulkResponse?.resultsList?.getOrNull(index)?.let { result ->
                        assertEquals(contractId, result.contractId)
                        assertEquals(
                            "Compensation [$componentName] with rate [${billingRate?.toDouble()}] is activated for contract [$contractId]",
                            result.message,
                        )
                    }
                } else {
                    updateCompensationStatusBulkResponse?.resultsList?.getOrNull(index)?.let { result ->
                        assertEquals(contractId, result.contractId)
                        assertEquals(errMsg, result.message)
                    }
                }
            }
        }
    }

    fun assertBulkValidate(
        expectedMap: HashMap<String, MutableList<ValidateUpsertInputRequest>>,
        responseMap: HashMap<String, Any>,
    ) {
        expectedMap.forEach {
            val actualValidateUpsertInputBulkResponse = responseMap[it.key] as ValidateUpsertInputBulkResponse
            validateUpsertInputBulkAssertion(
                it.value,
                actualValidateUpsertInputBulkResponse,
            )
        }
    }

    fun validateUpsertInputBulkAssertion(
        expectedUpsertResponse: MutableList<ValidateUpsertInputRequest>,
        actualUpsertResponse: ValidateUpsertInputBulkResponse,
    ) {
        // Map expected data for quick lookup by input_id
        val expectedDataMap = expectedUpsertResponse.associateBy { it.inputId }

        // Iterate over actual results
        actualUpsertResponse.resultsList.forEach { actualResult ->
            val inputId = actualResult.inputId
            val expectedData = getExpectedData(expectedDataMap, inputId)

            // Validate success status
            validateSuccessStatus(actualResult, expectedData, inputId)

            // Validate data key-value pairs
            actualResult.validatedInputDataMap.forEach { (key, value) ->
                validateDataKeyValuePairs(key, value, expectedData, inputId)
            }
        }
    }

    fun validateDataKeyValuePairs(
        key: Any,
        value: Any,
        expectedData: ValidateUpsertInputRequest,
        inputId: String,
    ) {
        if (key != TestConstants.ENTITY_ID &&
            key != TestConstants.COMPANY_ID &&
            key != TestConstants.COUNTRY_CODE &&
            key != TestConstants.CONTRACT_ID
        ) {
            val expectedValue = expectedData.dataMap[key]
                ?: throw AssertionError("Key $key not found in expected data for input_id: $inputId")
            assert(value == expectedValue) {
                "Mismatch for key=$key for input_id: $inputId. Expected: $expectedValue, Actual: $value"
            }
        } else {
            val expectedValue = when (key) {
                TestConstants.ENTITY_ID -> expectedData.keys.entityId.toString()
                TestConstants.COMPANY_ID -> expectedData.keys.companyId.toString()
                TestConstants.CONTRACT_ID -> expectedData.keys.contractId.toString()
                TestConstants.COUNTRY_CODE -> expectedData.keys.countryCode
                else -> throw AssertionError("Unexpected key $key in keys for input_id: $inputId")
            }
            assert(value == expectedValue) {
                "Mismatch for key=$key for input_id: $inputId. Expected: $expectedValue, Actual: $value"
            }
        }
    }

    fun assertBulkUpsert(
        expectedMap: HashMap<String, MutableList<ValidateUpsertInputRequest>>,
        responseMap: HashMap<String, Any>,
    ) {
        expectedMap.forEach {
            val actualValidateUpsertInputBulkResponse = responseMap[it.key] as UpsertBulkResponse
            genericAssertionForBulkApi(
                it.value,
                actualValidateUpsertInputBulkResponse,
            )
        }
    }

    fun genericAssertionForBulkApi(
        expectedUpsertResponse: MutableList<ValidateUpsertInputRequest>,
        actualUpsertResponse: UpsertBulkResponse,
    ) {
        // Map expected data for quick lookup by input_id
        val expectedDataMap = expectedUpsertResponse.associateBy { it.inputId }

        // Iterate over actual results
        actualUpsertResponse.resultsList.forEach { actualResult ->
            val inputId = actualResult.inputId
            val expectedData = expectedDataMap[inputId]
                ?: throw AssertionError("No expected data found for input_id: $inputId")

            // Validate success status
            val expectedSuccessStatus = expectedData.dataMap["EXPECTED_SUCCESS_STATUS"]?.let { it.toBoolean() }
                ?: throw AssertionError("EXPECTED_SUCCESS_STATUS not found for input_id: $inputId")
            if (expectedSuccessStatus) {
                assertEquals(actualResult.success, true)
            } else {
                assertEquals(actualResult.errorsList[0], expectedData.dataMap[TestConstants.ERROR_MSG])
            }
        }
    }

    fun postRevisionAssertions(
        expectedMap: HashMap<String, MutableList<ValidateUpsertInputRequest>>,
        contractsStatusBeforeRevision: HashMap<String, HashMap<String, Int>>,
    ) {
        expectedMap.forEach { (_, upsertRequests) ->
            upsertRequests.forEach {
                val contractId = it.keys.contractId
                when (it.dataMap["UPDATED_IN"]) {
                    "compensation" -> {
                        /**
                         * From contractsStatusBeforeRevision we are extracting the compensation data as per the
                         * contract_id and TestConstants.COMPENSATION which will return an int value which is
                         * stored in compensationDbCount.
                         * The whole idea behind this activity is that the number of rows before compensation
                         * revision should be less than the compensation rows after revision.
                         * it.dataMap["UPDATED_IN"] is coming from output csv to know which table to validate.
                         */
                        contractsStatusBeforeRevision[contractId.toString()]?.get(TestConstants.COMPENSATION)
                            ?.let { compensationDbCount ->
                                compensationAssertion(
                                    false,
                                    contractId.toString(),
                                    compensationDbCount,
                                    it.dataMap,
                                )
                            }
                        contractsStatusBeforeRevision[contractId.toString()]?.get(TestConstants.COMPENSATION_LOG)
                            ?.let { compensationLogDbCount ->
                                compensationLogAssertions.compensationLogAssertion(
                                    contractId.toString(),
                                    compensationLogDbCount,
                                    it.dataMap,
                                )
                            }
                        contractsStatusBeforeRevision[contractId.toString()]?.get(TestConstants.COMPENSATION_INPUT)
                            ?.let { compensationInputDbCount ->
                                compensationInputAssertions.compensationInputAssertion(
                                    contractId.toString(),
                                    compensationInputDbCount,
                                    it.dataMap,
                                )
                            }
                        contractsStatusBeforeRevision[contractId.toString()]?.get(TestConstants.COMPENSATION_ITEM)
                            ?.let { compensationItemDbCount ->
                                compensationItemAssertions.compensationItemAssertion(
                                    contractId.toString(),
                                    compensationItemDbCount,
                                    it.dataMap,
                                )
                            }
                    }

                    "compensationItem" -> {
                        // Skip compensationItem db validation
                        contractsStatusBeforeRevision[contractId.toString()]?.get(TestConstants.COMPENSATION)
                            ?.let { compensationDbCount ->
                                compensationAssertion(
                                    true,
                                    contractId.toString(),
                                    compensationDbCount,
                                    it.dataMap,
                                )
                            }
                        contractsStatusBeforeRevision[contractId.toString()]?.get(TestConstants.COMPENSATION_LOG)
                            ?.let { compensationLogDbCount ->
                                compensationLogAssertions.compensationLogAssertion(
                                    contractId.toString(),
                                    compensationLogDbCount,
                                    it.dataMap,
                                )
                            }
                        contractsStatusBeforeRevision[contractId.toString()]?.get(TestConstants.COMPENSATION_INPUT)
                            ?.let { compensationInputDbCount ->
                                compensationInputAssertions.compensationInputAssertion(
                                    contractId.toString(),
                                    compensationInputDbCount,
                                    it.dataMap,
                                )
                            }
                    }

                    else -> {
                        contractsStatusBeforeRevision[contractId.toString()]?.get(TestConstants.COMPENSATION)
                            ?.let { compensationDbCount ->
                                compensationAssertion(
                                    true,
                                    contractId.toString(),
                                    compensationDbCount,
                                    it.dataMap,
                                )
                            }
                        contractsStatusBeforeRevision[contractId.toString()]?.get(TestConstants.COMPENSATION_LOG)
                            ?.let { compensationLogDbCount ->
                                compensationLogAssertions.compensationLogAssertion(
                                    contractId.toString(),
                                    compensationLogDbCount,
                                    it.dataMap,
                                )
                            }
                        contractsStatusBeforeRevision[contractId.toString()]?.get(TestConstants.COMPENSATION_INPUT)
                            ?.let { compensationInputDbCount ->
                                compensationInputAssertions.compensationInputAssertion(
                                    contractId.toString(),
                                    compensationInputDbCount,
                                    it.dataMap,
                                )
                            }
                        contractsStatusBeforeRevision[contractId.toString()]?.get(TestConstants.COMPENSATION_ITEM)
                            ?.let { compensationItemDbCount ->
                                compensationItemAssertions.compensationItemAssertion(
                                    contractId.toString(),
                                    compensationItemDbCount,
                                    it.dataMap,
                                )
                            }
                    }
                }
            }
        }
    }

    fun paySupplementAssertion(
        contractId: String,
        dataMap: MutableMap<String, String>,
    ) {
        val paySupplementEntryInCompensation =
            compensationDb.getCompensationViaCategoryAndContract(TestConstants.PAY_SUPPLEMENT, contractId)
        paySupplementEntryInCompensation.forEach { record ->
            ScenarioContext.set(TestConstants.COMPENSATION_ID, record[TestConstants.ID].toString())
            ScenarioContext.set(TestConstants.COMPENSATION_SCHEMA_ID, record[TestConstants.SCHEMA_ITEM_ID].toString())
            validateRecord(record, dataMap, contractId)
        }
    }

    fun deductionAssertion(
        category: String,
        contractId: String,
        dataMap: MutableMap<String, String>,
    ) {
        val deductionEntryInCompensation =
            compensationDb.getCompensationViaCategoryAndContract(category, contractId)
        deductionEntryInCompensation.forEach { record ->
            ScenarioContext.set(TestConstants.COMPENSATION_ID, record[TestConstants.ID].toString())
            ScenarioContext.set(TestConstants.COMPENSATION_SCHEMA_ID, record[TestConstants.SCHEMA_ITEM_ID].toString())
            validateRecord(record, dataMap, contractId)
        }
    }

    private fun getExpectedData(
        expectedDataMap: Map<String, ValidateUpsertInputRequest>,
        inputId: String,
    ): ValidateUpsertInputRequest = expectedDataMap[inputId]
        ?: throw AssertionError("No expected data found for input_id: $inputId")

    private fun validateSuccessStatus(
        actualResult: ValidateUpsertInputResponse,
        expectedData: ValidateUpsertInputRequest,
        inputId: String,
    ) {
        val expectedSuccessStatus = expectedData.dataMap[TestConstants.EXPECTED_SUCCESS_STATUS]?.toBoolean()
            ?: throw AssertionError("EXPECTED_SUCCESS_STATUS not found for input_id: $inputId")

        if (expectedSuccessStatus) {
            assert(actualResult.success) {
                "Expected success=true but found failure for input_id: \" +\n" +
                    "                    \"$inputId and entity_id:\" + " +
                    actualResult.validatedInputDataMap[TestConstants.ENTITY_ID] +
                    "with error message: ${actualResult.messagesList[0].errorsList} for input_id: $inputId"
            }
        } else {
            assert(!actualResult.success) {
                "Expected success=false but found failure for input_id: \" +\n" +
                    "                    \"$inputId and entity_id:\" + " +
                    actualResult.validatedInputDataMap[TestConstants.ENTITY_ID] +
                    "with error message: ${actualResult.messagesList[0].errorsList} for input_id: $inputId"
            }

            // Match error message
            val expectedErrorMsg = expectedData.dataMap[TestConstants.ERROR_MSG]
                ?: throw AssertionError("ERROR_MSG not found for input_id: $inputId")
            assert(actualResult.messagesList[0].errorsList[0] == expectedErrorMsg) {
                "Expected error message: $expectedErrorMsg " +
                    "but got: ${actualResult.messagesList[0].errorsList} for input_id: $inputId"
            }
        }
    }

    private fun compensationAssertion(
        isNewEntry: Boolean,
        contractId: String,
        resultCount: Int,
        dataMap: MutableMap<String, String>,
    ) {
        /**
         * isNewEntry --> meaning a new entry is added, else we assert the existing db entry
         */
        if (isNewEntry) {
            val compensationPostRevision = compensationDb.getCompensationViaContractId(contractId).fetch()
            val actualCount = compensationPostRevision.count()

            assertTrue(
                resultCount < actualCount,
                "Count mismatch in compensation for $contractId. Expected: $resultCount, Actual: $actualCount",
            )

            val revisedRecord = compensationPostRevision.last()
            ScenarioContext.set(TestConstants.COMPENSATION_ID, revisedRecord[TestConstants.ID].toString())
            ScenarioContext.set(
                TestConstants.COMPENSATION_SCHEMA_ID,
                revisedRecord[TestConstants.SCHEMA_ITEM_ID].toString(),
            )

            validateRecord(revisedRecord, dataMap, contractId)
        } else {
            val compensationPostRevision =
                compensationDb.getCompensationViaEndDate(dataMap[TestConstants.END_DATE].toString()).fetch()

            compensationPostRevision.forEach { record ->
                ScenarioContext.set(TestConstants.COMPENSATION_ID, record[TestConstants.ID].toString())
                ScenarioContext.set(
                    TestConstants.COMPENSATION_SCHEMA_ID,
                    record[TestConstants.SCHEMA_ITEM_ID].toString(),
                )
                validateRecord(record, dataMap, contractId)
            }
        }
    }

    private fun validateRecord(
        record: Record,
        dataMap: MutableMap<String, String>,
        contractId: String,
    ) {
        assertEquals(
            dataMap[TestConstants.BILLING_RATE].toString(),
            GeneralUtils.formatNumber(record[TestConstants.BILLING_RATE] as Double),
            "Billing rate mismatch for $contractId",
        )
        assertEquals(
            dataMap[TestConstants.START_DATE].toString(),
            record[TestConstants.START_DATE].toString(),
            "Start date mismatch for $contractId",
        )
        assertEquals(
            dataMap[TestConstants.BILLING_FREQUENCY],
            record[TestConstants.BILLING_FREQUENCY],
            "Billing frequency mismatch for $contractId",
        )
        assertEquals(
            dataMap[TestConstants.END_DATE].toString(),
            record[TestConstants.END_DATE].toString(),
            "End date mismatch for $contractId",
        )
        assertEquals(
            dataMap[TestConstants.CURRENCY],
            record[TestConstants.CURRENCY],
            "Currency mismatch for $contractId",
        )
        assertEquals(TestConstants.NEW, record[TestConstants.STATUS], "Status mismatch for $contractId")
    }
}
