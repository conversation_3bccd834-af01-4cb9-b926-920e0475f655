package com.multiplier.compensation.dbRepository

import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.ResultQuery
import org.springframework.stereotype.Component

@Component
class CompensationDb(
    private val dsl: DSLContext,
) {
    fun getCompensationViaContractId(contractId: String): ResultQuery<Record> =
        dsl.resultQuery("SELECT * FROM compensation.compensation where contract_id = ?", contractId)

    fun getCompensationViaEndDate(endDate: String): ResultQuery<Record> =
        dsl.resultQuery("SELECT * FROM compensation.compensation WHERE end_date = ?", endDate)

    fun getCompensationViaCategoryAndContract(
        category: String,
        contractId: String,
    ): ResultQuery<Record> = dsl.resultQuery(
        "SELECT * FROM compensation.compensation WHERE category = ? and contract_id = ?",
        category,
        contractId,
    )
}
