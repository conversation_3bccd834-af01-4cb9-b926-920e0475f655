package com.multiplier.compensation.mock.databaseMocking

import com.multiplier.compensation.database.tables.records.CompensationSchemaItemRecord
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.service.testing.fixture.createCompensationSchemaItemRecordFixture
import org.jooq.DSLContext
import org.springframework.stereotype.Component

@Component
class CompensationSchemaItemDbMock(
    private val dsl: DSLContext,
) {
    lateinit var compensationSchemaItemRecordMock: CompensationSchemaItemRecord

    fun initAndInsertMockCompensationSchemaItem(compensationSchemaItem: CompensationSchemaItem) {
        compensationSchemaItemRecordMock = mockCompensationSchemaItem(compensationSchemaItem)
        insertCompensationSchemaItemSnapshotRecord(compensationSchemaItemRecordMock)
    }

    private fun mockCompensationSchemaItem(
        compensationSchemaItem: CompensationSchemaItem,
    ): CompensationSchemaItemRecord = createCompensationSchemaItemRecordFixture(
        id = compensationSchemaItem.id,
        schemaId = compensationSchemaItem.schemaId,
        componentName = compensationSchemaItem.componentName,
        category = compensationSchemaItem.category,
        isTaxable = compensationSchemaItem.isTaxable,
        isFixed = compensationSchemaItem.isFixed,
        isProrated = compensationSchemaItem.isProrated,
        isMandatory = compensationSchemaItem.isMandatory,
        isPartOfBasePay = compensationSchemaItem.isPartOfBasePay,
        isActive = compensationSchemaItem.isActive,
        createdOn = compensationSchemaItem.createdOn,
        createdBy = compensationSchemaItem.createdBy,
        updatedOn = compensationSchemaItem.updatedOn,
        updatedBy = compensationSchemaItem.updatedBy,
    )

    private fun insertCompensationSchemaItemSnapshotRecord(compensationSchemaItemRecord: CompensationSchemaItemRecord) {
        dsl.transaction { trx ->
            trx.dsl().executeInsert(compensationSchemaItemRecord)
        }
    }
}
